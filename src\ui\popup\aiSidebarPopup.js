/**
 * @file AI侧边栏弹窗控制器
 * @description 管理弹窗控制中心的交互逻辑和状态显示
 */

/**
 * @class AiSidebarPopup - AI侧边栏弹窗控制类
 * @description 负责弹窗界面的所有交互和状态管理
 */

// 引入统一的消息类型常量 - 改为全局变量访问以兼容扩展环境
// import { MESSAGE_TYPES } from '../../shared/messageTypes.js';
const MESSAGE_TYPES = window.MESSAGE_TYPES || globalThis.MESSAGE_TYPES || {};

class AiSidebarPopup {
  /**
   * @function constructor - 构造函数
   * @description 初始化弹窗控制器，设置事件监听和状态
   */
  constructor() {
    // 初始化DOM元素引用
    this.initializeElements();
    
    // 设置事件监听器
    this.setupEventListeners();
    
    // 初始化状态
    this.initializeState();
    
    // 建立通信连接
    this.setupMessageConnection();
    
    console.log('AI侧边栏弹窗已初始化');
  }

  /**
   * @function initializeElements - 初始化DOM元素引用
   * @description 获取并缓存关键DOM元素的引用
   */
  initializeElements() {
    // 主容器
    this.container = document.getElementById('ai-popup-container');
    
    // 状态指示器
    this.connectionStatus = document.getElementById('ai-connection-status');
    this.statusIndicator = this.connectionStatus?.querySelector('.ai-popup__status-indicator');
    this.statusText = this.connectionStatus?.querySelector('span');
    this.pageStatus = document.getElementById('ai-current-page-status');
    
    // 操作按钮
    this.toggleSidebarBtn = document.getElementById('ai-toggle-sidebar-btn');
    this.analyzePageBtn = document.getElementById('ai-analyze-current-page-btn');
    this.quickReplyBtn = document.getElementById('ai-quick-reply-btn');
    
    // 工具按钮
    this.settingsBtn = document.getElementById('ai-settings-btn');
  }

  /**
   * @function setupEventListeners - 设置事件监听器
   * @description 为所有交互元素添加事件监听器
   */
  setupEventListeners() {
    // 主要操作按钮事件
    this.toggleSidebarBtn?.addEventListener('click', () => this.handleToggleSidebar());
    this.analyzePageBtn?.addEventListener('click', () => this.handleAnalyzePage());
    this.quickReplyBtn?.addEventListener('click', () => this.handleQuickReply());
    
    // 工具按钮事件
    this.settingsBtn?.addEventListener('click', () => this.handleOpenSettings());
  }

  /**
   * @function initializeState - 初始化弹窗状态
   * @description 设置弹窗的初始状态和数据
   */
  async initializeState() {
    // 连接状态
    this.connectionState = 'checking';
    this.sidebarVisible = false;
    this.currentPageAnalyzed = false;
    
    // 检查侧边栏状态
    await this.checkSidebarStatus();
    
    // 检查当前页面状态
    await this.checkCurrentPageStatus();
    
    // 更新UI显示
    this.updateUI();
  }

  /**
   * @function setupMessageConnection - 建立消息通信连接
   * @description 设置与background和content scripts的消息通信
   */
  setupMessageConnection() {
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleBackgroundMessage(message, sendResponse);
    });
    
    // 发送弹窗连接确认
    this.sendToBackground({
      type: 'POPUP_CONNECTED',
      timestamp: Date.now()
    });
  }

  /**
   * @function handleToggleSidebar - 处理切换侧边栏显示
   * @description 打开或关闭侧边栏面板
   */
  async handleToggleSidebar() {
    this.setButtonLoading(this.toggleSidebarBtn, true);
    try {
      await this.sendToBackground({ type: MESSAGE_TYPES.SIDEBAR_TOGGLE });
    } catch (error) {
      console.error('切换侧边栏失败:', error);
      this.showError('无法切换侧边栏状态');
    } finally {
      this.setButtonLoading(this.toggleSidebarBtn, false);
    }
  }

  /**
   * @function handleAnalyzePage - 处理分析页面请求
   * @description 触发当前页面的智能分析
   */
  async handleAnalyzePage() {
    this.setButtonLoading(this.analyzePageBtn, true);
    
    try {
      await this.sendToBackground({ type: MESSAGE_TYPES.ANALYSIS_REQUEST });
      window.close(); // 分析请求发送后关闭弹窗
    } catch (error) {
      console.error('页面分析失败:', error);
      this.showError('分析过程出现错误');
    } finally {
      this.setButtonLoading(this.analyzePageBtn, false);
    }
  }

  /**
   * @function handleQuickReply - 处理快速回复功能
   * @description 生成智能回复建议
   */
  async handleQuickReply() {
    this.setButtonLoading(this.quickReplyBtn, true);
    
    try {
      await this.sendToBackground({ type: MESSAGE_TYPES.SMART_REPLY_GENERATE });
       window.close(); // 智能回复请求后关闭弹窗
    } catch (error) {
      console.error('生成回复建议失败:', error);
      this.showError('回复生成功能出现错误');
    } finally {
      this.setButtonLoading(this.quickReplyBtn, false);
    }
  }

  /**
   * @function handleOpenSettings - 处理打开设置
   * @description 打开设置配置页面
   */
  async handleOpenSettings() {
    try {
      await this.sendToBackground({
        type: 'OPEN_SETTINGS_PAGE'
      });
      
      // 关闭弹窗
      window.close();
    } catch (error) {
      console.error('打开设置失败:', error);
      this.showError('无法打开设置页面');
    }
  }

  /**
   * @function checkSidebarStatus - 检查侧边栏状态
   * @description 从background获取当前侧边栏显示状态
   */
  async checkSidebarStatus() {
    try {
      const response = await this.sendToBackground({ type: MESSAGE_TYPES.SIDEBAR_STATUS });
      this.sidebarVisible = response?.isOpen || false;
      this.connectionState = 'connected';
      this.updateUI();
    } catch (error) {
      console.error('检查侧边栏状态失败:', error);
      this.connectionState = 'error';
    }
  }

  /**
   * @function checkCurrentPageStatus - 检查当前页面状态
   * @description 获取当前页面的分析状态和信息
   */
  async checkCurrentPageStatus() {
    try {
      const response = await this.sendToBackground({
        type: 'GET_CURRENT_PAGE_STATUS'
      });
      
      if (response.success) {
        this.currentPageAnalyzed = response.analyzed;
        this.currentPageInfo = response.pageInfo;
      }
    } catch (error) {
      console.error('检查页面状态失败:', error);
    }
  }

  /**
   * @function updateUI - 更新界面显示
   * @description 根据当前状态更新所有UI元素
   */
  updateUI() {
    this.updateConnectionStatus();
    this.updateToggleButton();
    this.updatePageStatus();
  }

  /**
   * @function updateConnectionStatus - 更新连接状态显示
   * @description 更新状态指示器的显示状态
   */
  updateConnectionStatus() {
    if (!this.statusIndicator || !this.statusText) return;
    
    // 移除所有状态类
    this.statusIndicator.classList.remove(
      'ai-popup__status-indicator--connected',
      'ai-popup__status-indicator--disconnected',
      'ai-popup__status-indicator--connecting'
    );
    
    switch (this.connectionState) {
      case 'connected':
        this.statusIndicator.classList.add('ai-popup__status-indicator--connected');
        this.statusText.textContent = '已连接';
        break;
        
      case 'disconnected':
        this.statusIndicator.classList.add('ai-popup__status-indicator--disconnected');
        this.statusText.textContent = '未连接';
        break;
        
      case 'checking':
        this.statusIndicator.classList.add('ai-popup__status-indicator--connecting');
        this.statusText.textContent = '检查中...';
        break;
        
      default:
        this.statusIndicator.classList.add('ai-popup__status-indicator--disconnected');
        this.statusText.textContent = '错误';
    }
  }

  /**
   * @function updateToggleButton - 更新切换按钮状态
   * @description 根据侧边栏显示状态更新按钮文本
   */
  updateToggleButton() {
    if (!this.toggleSidebarBtn) return;
    
    const buttonText = this.toggleSidebarBtn.querySelector('span');
    if (buttonText) {
      buttonText.textContent = this.sidebarVisible ? '关闭侧边栏' : '打开侧边栏';
    }
  }

  /**
   * @function updatePageStatus - 更新页面状态显示
   * @description 更新当前页面的分析状态显示
   */
  updatePageStatus() {
    if (!this.pageStatus) return;
    
    const statusText = this.currentPageAnalyzed ? '当前页面：已分析' : '当前页面：未分析';
    this.pageStatus.textContent = statusText;
  }

  /**
   * @function setButtonLoading - 设置按钮加载状态
   * @param {HTMLElement} button - 按钮元素
   * @param {boolean} loading - 是否显示加载状态
   * @description 在操作进行时显示按钮加载状态
   */
  setButtonLoading(button, loading) {
    if (!button) return;
    
    button.disabled = loading;
    
    const buttonText = button.querySelector('span');
    if (buttonText) {
      if (loading) {
        button.dataset.originalText = buttonText.textContent;
        buttonText.textContent = '处理中...';
      } else {
        buttonText.textContent = button.dataset.originalText || buttonText.textContent;
      }
    }
  }

  /**
   * @function showError - 显示错误信息
   * @param {string} message - 错误消息
   * @description 显示错误提示信息
   */
  showError(message) {
    // 简单的错误显示，可以后续扩展为更复杂的通知系统
    console.error('弹窗错误:', message);
    
    // 临时更新状态指示器显示错误
    if (this.statusText) {
      const originalText = this.statusText.textContent;
      this.statusText.textContent = message;
      
      setTimeout(() => {
        this.statusText.textContent = originalText;
      }, 3000);
    }
  }

  /**
   * @function sendToBackground - 发送消息到background脚本
   * @param {Object} message - 要发送的消息对象
   * @returns {Promise<Object>} 返回响应Promise
   * @description 与background脚本进行异步通信
   */
  sendToBackground(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response || {});
        }
      });
    });
  }

  /**
   * @function handleBackgroundMessage - 处理来自background的消息
   * @param {Object} message - 接收到的消息
   * @param {Function} sendResponse - 响应函数
   * @description 处理background脚本发送的消息
   */
  handleBackgroundMessage(message, sendResponse) {
    switch (message.type) {
      case 'SIDEBAR_STATUS_CHANGED':
        this.sidebarVisible = message.visible;
        this.updateToggleButton();
        break;
        
      case 'PAGE_ANALYSIS_COMPLETED':
        this.currentPageAnalyzed = true;
        this.updatePageStatus();
        break;
        
      case 'CONNECTION_STATUS_CHANGED':
        this.connectionState = message.status;
        this.updateConnectionStatus();
        break;
        
      default:
        console.log('弹窗收到未知消息类型:', message.type);
    }
    
    sendResponse({ received: true });
  }
}

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.aiSidebarPopup = new AiSidebarPopup();
}); 