/**
 * @file Notion知识库缓存管理器
 * @description 管理Notion知识库的本地缓存，实现增量同步和快速搜索
 */

/**
 * @class AiNotionCacheManager
 * @description Notion知识库缓存管理器
 */
class AiNotionCacheManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化缓存管理器
   */
  constructor() {
    // 缓存配置
    this.config = {
      storageKey: 'ai_sidebar_notion_cache',
      metadataKey: 'ai_sidebar_notion_cache_metadata',
      maxCacheSize: 50 * 1024 * 1024, // 50MB
      cacheExpiry: 24 * 60 * 60 * 1000, // 24小时
      updateInterval: 30 * 60 * 1000, // 30分钟检查间隔
      batchSize: 20 // 每次同步的页面数量
    };
    
    // 缓存状态
    this.cacheState = {
      isInitialized: false,
      lastUpdate: null,
      totalPages: 0,
      cacheSize: 0,
      isUpdating: false
    };
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化缓存管理器
   * @description 初始化缓存系统
   */
  async init() {
    try {
      console.log('[Notion缓存] 初始化缓存管理器...');
      
      // 加载缓存元数据
      await this.loadCacheMetadata();
      
      // 检查缓存是否需要更新
      await this.checkCacheUpdate();
      
      this.cacheState.isInitialized = true;
      console.log('[Notion缓存] 缓存管理器初始化完成');
      
      // 触发初始化完成事件
      this.emit('initialized', this.cacheState);
      
    } catch (error) {
      console.error('[Notion缓存] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function loadCacheMetadata - 加载缓存元数据
   * @description 从本地存储加载缓存元数据
   */
  async loadCacheMetadata() {
    try {
      const result = await chrome.storage.local.get([this.config.metadataKey]);
      const metadata = result[this.config.metadataKey];
      
      if (metadata) {
        this.cacheState = {
          ...this.cacheState,
          ...metadata
        };
        console.log('[Notion缓存] 缓存元数据加载成功:', this.cacheState);
      } else {
        console.log('[Notion缓存] 未找到缓存元数据，将创建新缓存');
      }
    } catch (error) {
      console.error('[Notion缓存] 加载缓存元数据失败:', error);
    }
  }

  /**
   * @function saveCacheMetadata - 保存缓存元数据
   * @description 将缓存元数据保存到本地存储
   */
  async saveCacheMetadata() {
    try {
      await chrome.storage.local.set({
        [this.config.metadataKey]: {
          lastUpdate: this.cacheState.lastUpdate,
          totalPages: this.cacheState.totalPages,
          cacheSize: this.cacheState.cacheSize,
          isUpdating: this.cacheState.isUpdating
        }
      });
    } catch (error) {
      console.error('[Notion缓存] 保存缓存元数据失败:', error);
    }
  }

  /**
   * @function checkCacheUpdate - 检查缓存是否需要更新
   * @description 检查缓存是否过期或需要更新
   * @returns {Promise<boolean>} 是否需要更新
   */
  async checkCacheUpdate() {
    try {
      const now = Date.now();
      
      // 检查是否首次运行
      if (!this.cacheState.lastUpdate) {
        console.log('[Notion缓存] 首次运行，需要初始化缓存');
        return true;
      }
      
      // 检查是否过期
      const timeSinceUpdate = now - this.cacheState.lastUpdate;
      if (timeSinceUpdate > this.config.cacheExpiry) {
        console.log('[Notion缓存] 缓存已过期，需要更新');
        return true;
      }
      
      // 检查是否到了定期更新时间
      if (timeSinceUpdate > this.config.updateInterval) {
        console.log('[Notion缓存] 到达定期更新时间');
        return true;
      }
      
      console.log('[Notion缓存] 缓存仍然有效');
      return false;
    } catch (error) {
      console.error('[Notion缓存] 检查缓存更新失败:', error);
      return true; // 出错时强制更新
    }
  }

  /**
   * @function updateCache - 更新缓存
   * @description 从Notion API更新本地缓存
   * @param {Object} notionConnector - Notion连接器实例
   * @param {boolean} forceUpdate - 是否强制更新
   * @returns {Promise<Object>} 更新结果
   */
  async updateCache(notionConnector, forceUpdate = false) {
    if (this.cacheState.isUpdating && !forceUpdate) {
      console.log('[Notion缓存] 缓存正在更新中，跳过此次更新');
      return { success: false, message: '缓存正在更新中' };
    }
    
    try {
      this.cacheState.isUpdating = true;
      await this.saveCacheMetadata();
      
      console.log('[Notion缓存] 开始更新缓存...');
      this.emit('updateStarted');
      
      // 获取知识库数据库ID
      const databaseId = notionConnector.databaseMapping.knowledgeBase;
      if (!databaseId) {
        throw new Error('知识库数据库ID未配置');
      }
      
      // 获取现有缓存
      const existingCache = await this.loadCache();
      
      // 获取数据库中的所有页面（增量更新）
      const updatedPages = await this.fetchUpdatedPages(notionConnector, databaseId, existingCache);
      
      // 合并更新的页面到缓存
      const newCache = this.mergeUpdatedPages(existingCache, updatedPages);
      
      // 保存更新后的缓存
      await this.saveCache(newCache);
      
      // 更新缓存状态
      this.cacheState.lastUpdate = Date.now();
      this.cacheState.totalPages = Object.keys(newCache).length;
      this.cacheState.cacheSize = this.calculateCacheSize(newCache);
      this.cacheState.isUpdating = false;
      
      await this.saveCacheMetadata();
      
      console.log(`[Notion缓存] 缓存更新完成，共 ${this.cacheState.totalPages} 个页面`);
      this.emit('updateCompleted', {
        totalPages: this.cacheState.totalPages,
        updatedPages: updatedPages.length,
        cacheSize: this.cacheState.cacheSize
      });
      
      return {
        success: true,
        totalPages: this.cacheState.totalPages,
        updatedPages: updatedPages.length,
        cacheSize: this.cacheState.cacheSize
      };
      
    } catch (error) {
      console.error('[Notion缓存] 缓存更新失败:', error);
      this.cacheState.isUpdating = false;
      await this.saveCacheMetadata();
      
      this.emit('updateFailed', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function fetchUpdatedPages - 获取更新的页面
   * @description 从Notion API获取自上次更新以来有变化的页面
   * @param {Object} notionConnector - Notion连接器
   * @param {string} databaseId - 数据库ID
   * @param {Object} existingCache - 现有缓存
   * @returns {Promise<Array>} 更新的页面列表
   */
  async fetchUpdatedPages(notionConnector, databaseId, existingCache) {
    try {
      const updatedPages = [];
      let hasMore = true;
      let nextCursor = null;
      
      while (hasMore) {
        // 构建查询参数
        const queryPayload = {
          page_size: this.config.batchSize,
          sorts: [
            {
              property: 'last_edited_time',
              direction: 'descending'
            }
          ]
        };
        
        if (nextCursor) {
          queryPayload.start_cursor = nextCursor;
        }
        
        // 如果有上次更新时间，只获取更新的页面
        if (this.cacheState.lastUpdate && !this.isForceUpdate) {
          queryPayload.filter = {
            property: 'last_edited_time',
            date: {
              after: new Date(this.cacheState.lastUpdate).toISOString()
            }
          };
        }
        
        // 调用Notion API
        const response = await notionConnector.callNotionApi(
          `databases/${databaseId}/query`,
          'POST',
          queryPayload
        );
        
        if (!response.success) {
          throw new Error(`API调用失败: ${response.error}`);
        }
        
        const pages = response.data.results;
        
        // 处理每个页面
        for (const page of pages) {
          const pageId = page.id;
          const lastEdited = new Date(page.last_edited_time).getTime();
          
          // 检查是否需要更新此页面
          if (!existingCache[pageId] || existingCache[pageId].lastEdited < lastEdited) {
            // 获取页面内容
            const pageContent = await this.fetchPageContent(notionConnector, pageId);
            
            updatedPages.push({
              id: pageId,
              title: notionConnector.extractPageTitle(page),
              lastEdited: lastEdited,
              properties: notionConnector.extractPageProperties(page),
              content: pageContent,
              url: page.url,
              createdTime: new Date(page.created_time).getTime()
            });
          }
        }
        
        hasMore = response.data.has_more;
        nextCursor = response.data.next_cursor;
        
        // 如果是增量更新且没有更多更新的页面，停止
        if (this.cacheState.lastUpdate && pages.length === 0) {
          break;
        }
      }
      
      console.log(`[Notion缓存] 获取到 ${updatedPages.length} 个更新的页面`);
      return updatedPages;
      
    } catch (error) {
      console.error('[Notion缓存] 获取更新页面失败:', error);
      throw error;
    }
  }

  /**
   * @function fetchPageContent - 获取页面内容
   * @description 获取Notion页面的详细内容
   * @param {Object} notionConnector - Notion连接器
   * @param {string} pageId - 页面ID
   * @returns {Promise<string>} 页面内容
   */
  async fetchPageContent(notionConnector, pageId) {
    try {
      const response = await notionConnector.callNotionApi(`blocks/${pageId}/children`, 'GET');
      
      if (!response.success) {
        console.warn(`[Notion缓存] 获取页面内容失败: ${pageId}`, response.error);
        return '';
      }
      
      // 提取文本内容
      const blocks = response.data.results;
      let content = '';
      
      for (const block of blocks) {
        content += this.extractBlockText(block) + '\n';
      }
      
      return content.trim();
    } catch (error) {
      console.error(`[Notion缓存] 获取页面内容异常: ${pageId}`, error);
      return '';
    }
  }

  /**
   * @function extractBlockText - 提取块文本
   * @description 从Notion块中提取文本内容
   * @param {Object} block - Notion块对象
   * @returns {string} 提取的文本
   */
  extractBlockText(block) {
    try {
      const blockType = block.type;
      const blockData = block[blockType];
      
      if (!blockData) return '';
      
      // 处理不同类型的块
      switch (blockType) {
        case 'paragraph':
        case 'heading_1':
        case 'heading_2':
        case 'heading_3':
        case 'bulleted_list_item':
        case 'numbered_list_item':
        case 'quote':
        case 'callout':
          return this.extractRichText(blockData.rich_text || blockData.text);
        
        case 'code':
          return blockData.rich_text ? this.extractRichText(blockData.rich_text) : '';
        
        case 'to_do':
          const todoText = this.extractRichText(blockData.rich_text || []);
          return `${blockData.checked ? '✓' : '☐'} ${todoText}`;
        
        default:
          return '';
      }
    } catch (error) {
      console.warn('[Notion缓存] 提取块文本失败:', error);
      return '';
    }
  }

  /**
   * @function extractRichText - 提取富文本
   * @description 从Notion富文本数组中提取纯文本
   * @param {Array} richTextArray - 富文本数组
   * @returns {string} 纯文本
   */
  extractRichText(richTextArray) {
    if (!Array.isArray(richTextArray)) return '';
    
    return richTextArray
      .map(text => text.plain_text || text.text?.content || '')
      .join('');
  }

  /**
   * @function mergeUpdatedPages - 合并更新的页面
   * @description 将更新的页面合并到现有缓存中
   * @param {Object} existingCache - 现有缓存
   * @param {Array} updatedPages - 更新的页面
   * @returns {Object} 合并后的缓存
   */
  mergeUpdatedPages(existingCache, updatedPages) {
    const newCache = { ...existingCache };
    
    for (const page of updatedPages) {
      newCache[page.id] = page;
    }
    
    return newCache;
  }

  /**
   * @function loadCache - 加载缓存
   * @description 从本地存储加载缓存数据
   * @returns {Promise<Object>} 缓存数据
   */
  async loadCache() {
    try {
      const result = await chrome.storage.local.get([this.config.storageKey]);
      return result[this.config.storageKey] || {};
    } catch (error) {
      console.error('[Notion缓存] 加载缓存失败:', error);
      return {};
    }
  }

  /**
   * @function saveCache - 保存缓存
   * @description 将缓存数据保存到本地存储
   * @param {Object} cache - 缓存数据
   */
  async saveCache(cache) {
    try {
      // 检查缓存大小
      const cacheSize = this.calculateCacheSize(cache);
      if (cacheSize > this.config.maxCacheSize) {
        console.warn('[Notion缓存] 缓存大小超限，进行清理...');
        cache = this.cleanupCache(cache);
      }
      
      await chrome.storage.local.set({
        [this.config.storageKey]: cache
      });
      
      console.log('[Notion缓存] 缓存保存成功');
    } catch (error) {
      console.error('[Notion缓存] 保存缓存失败:', error);
      throw error;
    }
  }

  /**
   * @function calculateCacheSize - 计算缓存大小
   * @description 计算缓存数据的大小（字节）
   * @param {Object} cache - 缓存数据
   * @returns {number} 缓存大小（字节）
   */
  calculateCacheSize(cache) {
    try {
      const cacheString = JSON.stringify(cache);
      return new Blob([cacheString]).size;
    } catch (error) {
      console.error('[Notion缓存] 计算缓存大小失败:', error);
      return 0;
    }
  }

  /**
   * @function cleanupCache - 清理缓存
   * @description 清理过大的缓存，保留最近的页面
   * @param {Object} cache - 原始缓存
   * @returns {Object} 清理后的缓存
   */
  cleanupCache(cache) {
    try {
      // 按最后编辑时间排序
      const pages = Object.values(cache).sort((a, b) => b.lastEdited - a.lastEdited);
      
      // 保留最近的页面，直到缓存大小合适
      const cleanedCache = {};
      let currentSize = 0;
      
      for (const page of pages) {
        const pageSize = new Blob([JSON.stringify(page)]).size;
        
        if (currentSize + pageSize <= this.config.maxCacheSize * 0.8) { // 保留80%的空间
          cleanedCache[page.id] = page;
          currentSize += pageSize;
        } else {
          break;
        }
      }
      
      console.log(`[Notion缓存] 缓存清理完成，从 ${Object.keys(cache).length} 页面减少到 ${Object.keys(cleanedCache).length} 页面`);
      return cleanedCache;
    } catch (error) {
      console.error('[Notion缓存] 缓存清理失败:', error);
      return cache;
    }
  }

  /**
   * @function searchCache - 搜索缓存
   * @description 在本地缓存中搜索内容
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async searchCache(query, options = {}) {
    try {
      const {
        limit = 10,
        searchType = 'all', // 'title', 'content', 'all'
        sortBy = 'relevance' // 'relevance', 'lastEdited', 'created'
      } = options;
      
      const cache = await this.loadCache();
      const pages = Object.values(cache);
      
      if (pages.length === 0) {
        return [];
      }
      
      // 搜索匹配的页面
      const matches = [];
      const queryLower = query.toLowerCase();
      
      for (const page of pages) {
        let score = 0;
        let matchedFields = [];
        
        // 标题匹配
        if (searchType === 'title' || searchType === 'all') {
          if (page.title && page.title.toLowerCase().includes(queryLower)) {
            score += 10; // 标题匹配权重更高
            matchedFields.push('title');
          }
        }
        
        // 内容匹配
        if (searchType === 'content' || searchType === 'all') {
          if (page.content && page.content.toLowerCase().includes(queryLower)) {
            score += 5;
            matchedFields.push('content');
          }
        }
        
        // 属性匹配
        if (searchType === 'all' && page.properties) {
          for (const [key, value] of Object.entries(page.properties)) {
            if (typeof value === 'string' && value.toLowerCase().includes(queryLower)) {
              score += 3;
              matchedFields.push(key);
            }
          }
        }
        
        if (score > 0) {
          matches.push({
            ...page,
            searchScore: score,
            matchedFields: matchedFields
          });
        }
      }
      
      // 排序结果
      matches.sort((a, b) => {
        switch (sortBy) {
          case 'lastEdited':
            return b.lastEdited - a.lastEdited;
          case 'created':
            return b.createdTime - a.createdTime;
          case 'relevance':
          default:
            return b.searchScore - a.searchScore;
        }
      });
      
      // 限制结果数量
      const results = matches.slice(0, limit);
      
      console.log(`[Notion缓存] 搜索完成，查询: "${query}"，找到 ${results.length} 个结果`);
      
      return results;
    } catch (error) {
      console.error('[Notion缓存] 搜索缓存失败:', error);
      return [];
    }
  }

  /**
   * @function clearCache - 清除缓存
   * @description 清除所有缓存数据
   */
  async clearCache() {
    try {
      await chrome.storage.local.remove([this.config.storageKey, this.config.metadataKey]);
      
      // 重置状态
      this.cacheState = {
        isInitialized: false,
        lastUpdate: null,
        totalPages: 0,
        cacheSize: 0,
        isUpdating: false
      };
      
      console.log('[Notion缓存] 缓存已清除');
      this.emit('cacheCleared');
      
      return { success: true };
    } catch (error) {
      console.error('[Notion缓存] 清除缓存失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * @function getCacheStatus - 获取缓存状态
   * @description 获取当前缓存的状态信息
   * @returns {Object} 缓存状态
   */
  getCacheStatus() {
    return {
      ...this.cacheState,
      config: {
        maxCacheSize: this.config.maxCacheSize,
        cacheExpiry: this.config.cacheExpiry,
        updateInterval: this.config.updateInterval
      }
    };
  }

  /**
   * @function on - 添加事件监听器
   * @description 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * @function emit - 触发事件
   * @description 触发事件并调用所有监听器
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[Notion缓存] 事件监听器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理缓存管理器资源
   */
  cleanup() {
    this.eventListeners.clear();
    console.log('[Notion缓存] 缓存管理器已清理');
  }
}

// 导出缓存管理器类
export { AiNotionCacheManager };
