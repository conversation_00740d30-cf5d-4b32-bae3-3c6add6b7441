/**
 * @file AI智能内容摘要器
 * @description 提供多级别、多类型的智能内容摘要功能
 */

/**
 * @class AiContentSummarizer
 * @description 智能内容摘要器，支持多种内容类型和摘要级别
 */
class AiContentSummarizer {
  /**
   * @function constructor - 构造函数
   * @description 初始化内容摘要器
   * @param {Object} apiManager - API管理器实例
   */
  constructor(apiManager) {
    this.apiManager = apiManager;
    
    // 摘要配置
    this.config = {
      // 摘要级别配置
      levels: {
        brief: {
          maxLength: 100,
          targetSentences: 2,
          description: '简要摘要'
        },
        detailed: {
          maxLength: 300,
          targetSentences: 5,
          description: '详细摘要'
        },
        comprehensive: {
          maxLength: 600,
          targetSentences: 10,
          description: '全面摘要'
        }
      },
      
      // 内容类型配置
      contentTypes: {
        article: {
          name: '文章',
          extractors: ['title', 'paragraphs', 'headings'],
          templates: ['article_summary', 'key_points']
        },
        video: {
          name: '视频',
          extractors: ['title', 'description', 'transcript'],
          templates: ['video_summary', 'timeline']
        },
        pdf: {
          name: 'PDF文档',
          extractors: ['text', 'headings', 'tables'],
          templates: ['document_summary', 'structure']
        },
        code: {
          name: '代码',
          extractors: ['functions', 'classes', 'comments'],
          templates: ['code_summary', 'api_docs']
        },
        webpage: {
          name: '网页',
          extractors: ['content', 'links', 'metadata'],
          templates: ['page_summary', 'navigation']
        }
      },
      
      // 语言检测配置
      languages: {
        'zh': { name: '中文', prompt: 'zh-CN' },
        'en': { name: 'English', prompt: 'en-US' },
        'ja': { name: '日本語', prompt: 'ja-JP' },
        'es': { name: 'Español', prompt: 'es-ES' }
      }
    };
    
    // 摘要模板
    this.templates = new Map([
      ['article_summary', {
        name: '文章摘要',
        prompt: '请为以下文章生成{level}摘要，重点关注主要观点和结论：\n\n{content}',
        format: 'paragraph'
      }],
      ['key_points', {
        name: '要点提取',
        prompt: '请从以下内容中提取{count}个关键要点：\n\n{content}',
        format: 'list'
      }],
      ['video_summary', {
        name: '视频摘要',
        prompt: '请为以下视频内容生成摘要，包括主要内容和时间线：\n\n{content}',
        format: 'structured'
      }],
      ['document_summary', {
        name: '文档摘要',
        prompt: '请为以下文档生成结构化摘要，包括主要章节和内容：\n\n{content}',
        format: 'structured'
      }],
      ['code_summary', {
        name: '代码摘要',
        prompt: '请为以下代码生成技术摘要，包括功能、API和使用方法：\n\n{content}',
        format: 'technical'
      }]
    ]);
    
    // 内容提取器
    this.extractors = new Map();
    this.initializeExtractors();
    
    // 缓存
    this.summaryCache = new Map();
    this.maxCacheSize = 100;
    
    // 统计信息
    this.stats = {
      totalSummaries: 0,
      cacheHits: 0,
      averageTime: 0,
      byType: {},
      byLevel: {}
    };
  }

  /**
   * @function initializeExtractors - 初始化内容提取器
   * @description 初始化各种内容类型的提取器
   */
  initializeExtractors() {
    // 文章提取器
    this.extractors.set('article', {
      extract: (element) => {
        const title = element.querySelector('h1, .title, [class*="title"]')?.textContent || '';
        const paragraphs = Array.from(element.querySelectorAll('p'))
          .map(p => p.textContent.trim())
          .filter(text => text.length > 20);
        const headings = Array.from(element.querySelectorAll('h2, h3, h4'))
          .map(h => h.textContent.trim());
        
        return { title, paragraphs, headings };
      }
    });
    
    // 网页提取器
    this.extractors.set('webpage', {
      extract: (element) => {
        const content = this.extractMainContent(element);
        const links = Array.from(element.querySelectorAll('a[href]'))
          .map(a => ({ text: a.textContent.trim(), href: a.href }))
          .filter(link => link.text.length > 0);
        const metadata = this.extractMetadata(element);
        
        return { content, links, metadata };
      }
    });
    
    // 代码提取器
    this.extractors.set('code', {
      extract: (element) => {
        const codeBlocks = Array.from(element.querySelectorAll('pre, code, .highlight'))
          .map(block => block.textContent);
        const functions = this.extractFunctions(codeBlocks.join('\n'));
        const classes = this.extractClasses(codeBlocks.join('\n'));
        const comments = this.extractComments(codeBlocks.join('\n'));
        
        return { functions, classes, comments, code: codeBlocks };
      }
    });
  }

  /**
   * @function summarizeContent - 摘要内容
   * @description 对指定内容进行智能摘要
   * @param {string|HTMLElement} content - 要摘要的内容
   * @param {Object} options - 摘要选项
   * @returns {Promise<Object>} 摘要结果
   */
  async summarizeContent(content, options = {}) {
    const {
      level = 'detailed',
      contentType = 'auto',
      template = 'auto',
      language = 'auto',
      format = 'auto',
      useCache = true
    } = options;
    
    const startTime = Date.now();
    
    try {
      console.log(`[内容摘要] 🚀 开始生成${level}摘要...`);
      
      // 检测内容类型
      const detectedType = contentType === 'auto' ? 
        await this.detectContentType(content) : contentType;
      
      // 提取内容
      const extractedContent = await this.extractContent(content, detectedType);
      
      // 检测语言
      const detectedLanguage = language === 'auto' ? 
        await this.detectLanguage(extractedContent.text) : language;
      
      // 生成缓存键
      const cacheKey = this.generateCacheKey(extractedContent.text, level, detectedType, template);
      
      // 检查缓存
      if (useCache && this.summaryCache.has(cacheKey)) {
        this.stats.cacheHits++;
        console.log('[内容摘要] 🎯 使用缓存摘要');
        return this.summaryCache.get(cacheKey);
      }
      
      // 选择模板
      const selectedTemplate = template === 'auto' ? 
        this.selectTemplate(detectedType, level) : template;
      
      // 生成摘要
      const summary = await this.generateSummary(
        extractedContent, 
        level, 
        detectedType, 
        selectedTemplate, 
        detectedLanguage
      );
      
      // 后处理
      const processedSummary = await this.postProcessSummary(summary, level, format);
      
      // 构建结果
      const result = {
        summary: processedSummary,
        level: level,
        contentType: detectedType,
        language: detectedLanguage,
        template: selectedTemplate,
        metadata: {
          originalLength: extractedContent.text.length,
          summaryLength: processedSummary.length,
          compressionRatio: (processedSummary.length / extractedContent.text.length * 100).toFixed(1) + '%',
          processingTime: Date.now() - startTime,
          timestamp: new Date().toISOString()
        },
        insights: await this.extractInsights(extractedContent.text, detectedLanguage),
        sentiment: await this.analyzeSentiment(extractedContent.text, detectedLanguage)
      };
      
      // 缓存结果
      if (useCache) {
        this.cacheResult(cacheKey, result);
      }
      
      // 更新统计
      this.updateStats(result);
      
      console.log(`[内容摘要] ✅ 摘要生成完成 (${result.metadata.processingTime}ms)`);
      
      return result;
      
    } catch (error) {
      console.error('[内容摘要] ❌ 摘要生成失败:', error);
      throw new Error(`摘要生成失败: ${error.message}`);
    }
  }

  /**
   * @function detectContentType - 检测内容类型
   * @description 自动检测内容的类型
   * @param {string|HTMLElement} content - 内容
   * @returns {Promise<string>} 内容类型
   */
  async detectContentType(content) {
    if (typeof content === 'string') {
      // 检测代码
      if (this.isCode(content)) {
        return 'code';
      }
      
      // 检测URL或HTML
      if (content.includes('http') || content.includes('<')) {
        return 'webpage';
      }
      
      return 'article';
    }
    
    if (content instanceof HTMLElement) {
      // 检测视频
      if (content.querySelector('video, iframe[src*="youtube"], iframe[src*="vimeo"]')) {
        return 'video';
      }
      
      // 检测代码
      if (content.querySelector('pre, code, .highlight, .code')) {
        return 'code';
      }
      
      // 检测文章结构
      if (content.querySelector('article, .article, .post, .content')) {
        return 'article';
      }
      
      return 'webpage';
    }
    
    return 'article';
  }

  /**
   * @function extractContent - 提取内容
   * @description 根据内容类型提取相关信息
   * @param {string|HTMLElement} content - 原始内容
   * @param {string} contentType - 内容类型
   * @returns {Promise<Object>} 提取的内容
   */
  async extractContent(content, contentType) {
    let element = content;
    
    // 转换字符串为DOM元素
    if (typeof content === 'string') {
      const div = document.createElement('div');
      div.innerHTML = content;
      element = div;
    }
    
    // 使用对应的提取器
    const extractor = this.extractors.get(contentType);
    if (extractor) {
      const extracted = extractor.extract(element);
      return {
        ...extracted,
        text: this.combineExtractedText(extracted),
        type: contentType
      };
    }
    
    // 默认提取
    return {
      text: element.textContent || content.toString(),
      type: contentType
    };
  }

  /**
   * @function detectLanguage - 检测语言
   * @description 检测文本的语言
   * @param {string} text - 要检测的文本
   * @returns {Promise<string>} 语言代码
   */
  async detectLanguage(text) {
    // 简单的语言检测逻辑
    const sample = text.substring(0, 200);
    
    // 中文检测
    if (/[\u4e00-\u9fff]/.test(sample)) {
      return 'zh';
    }
    
    // 日文检测
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(sample)) {
      return 'ja';
    }
    
    // 西班牙文检测（简单）
    if (/[ñáéíóúü]/i.test(sample)) {
      return 'es';
    }
    
    // 默认英文
    return 'en';
  }

  /**
   * @function generateSummary - 生成摘要
   * @description 使用AI生成摘要
   * @param {Object} content - 提取的内容
   * @param {string} level - 摘要级别
   * @param {string} contentType - 内容类型
   * @param {string} template - 模板名称
   * @param {string} language - 语言
   * @returns {Promise<string>} 生成的摘要
   */
  async generateSummary(content, level, contentType, template, language) {
    const templateConfig = this.templates.get(template);
    const levelConfig = this.config.levels[level];
    const languageConfig = this.config.languages[language];
    
    if (!templateConfig || !levelConfig) {
      throw new Error(`无效的模板或级别: ${template}, ${level}`);
    }
    
    // 构建提示词
    let prompt = templateConfig.prompt
      .replace('{level}', levelConfig.description)
      .replace('{count}', levelConfig.targetSentences)
      .replace('{content}', content.text);
    
    // 添加语言指令
    if (languageConfig) {
      prompt = `请用${languageConfig.name}回答。\n\n${prompt}`;
    }
    
    // 添加长度限制
    prompt += `\n\n请确保摘要长度不超过${levelConfig.maxLength}字符，包含${levelConfig.targetSentences}个主要要点。`;
    
    // 调用AI API
    const response = await this.apiManager.callGeminiAPI({
      prompt: prompt,
      maxTokens: Math.ceil(levelConfig.maxLength * 1.5),
      temperature: 0.3
    });
    
    return response.text || '';
  }

  /**
   * @function postProcessSummary - 后处理摘要
   * @description 对生成的摘要进行后处理
   * @param {string} summary - 原始摘要
   * @param {string} level - 摘要级别
   * @param {string} format - 输出格式
   * @returns {Promise<string>} 处理后的摘要
   */
  async postProcessSummary(summary, level, format) {
    let processed = summary.trim();
    
    // 移除多余的空行
    processed = processed.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // 格式化处理
    if (format === 'list' || format === 'auto') {
      // 如果内容适合列表格式，转换为列表
      if (this.shouldFormatAsList(processed)) {
        processed = this.formatAsList(processed);
      }
    }
    
    // 长度检查和截断
    const maxLength = this.config.levels[level].maxLength;
    if (processed.length > maxLength) {
      processed = this.truncateToSentence(processed, maxLength);
    }
    
    return processed;
  }

  /**
   * @function extractInsights - 提取洞察
   * @description 从内容中提取关键洞察
   * @param {string} text - 文本内容
   * @param {string} language - 语言
   * @returns {Promise<Array>} 洞察数组
   */
  async extractInsights(text, language) {
    try {
      const prompt = `请从以下内容中提取3-5个关键洞察或要点，每个洞察用一句话概括：\n\n${text.substring(0, 1000)}`;
      
      const response = await this.apiManager.callGeminiAPI({
        prompt: prompt,
        maxTokens: 200,
        temperature: 0.2
      });
      
      // 解析洞察
      const insights = response.text
        .split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .slice(0, 5);
      
      return insights;
    } catch (error) {
      console.warn('[内容摘要] 洞察提取失败:', error);
      return [];
    }
  }

  /**
   * @function analyzeSentiment - 分析情感
   * @description 分析内容的情感倾向
   * @param {string} text - 文本内容
   * @param {string} language - 语言
   * @returns {Promise<Object>} 情感分析结果
   */
  async analyzeSentiment(text, language) {
    try {
      const prompt = `请分析以下内容的情感倾向，返回积极、中性或消极，并给出0-1的置信度：\n\n${text.substring(0, 500)}`;
      
      const response = await this.apiManager.callGeminiAPI({
        prompt: prompt,
        maxTokens: 50,
        temperature: 0.1
      });
      
      // 简单解析情感
      const result = response.text.toLowerCase();
      let sentiment = 'neutral';
      let confidence = 0.5;
      
      if (result.includes('积极') || result.includes('positive')) {
        sentiment = 'positive';
        confidence = 0.7;
      } else if (result.includes('消极') || result.includes('negative')) {
        sentiment = 'negative';
        confidence = 0.7;
      }
      
      return { sentiment, confidence };
    } catch (error) {
      console.warn('[内容摘要] 情感分析失败:', error);
      return { sentiment: 'neutral', confidence: 0.5 };
    }
  }

  /**
   * @function extractMainContent - 提取主要内容
   * @description 从网页中提取主要内容
   * @param {HTMLElement} element - DOM元素
   * @returns {string} 主要内容
   */
  extractMainContent(element) {
    // 尝试找到主要内容区域
    const mainSelectors = [
      'main', 'article', '.content', '.post', '.entry',
      '#content', '#main', '.main-content'
    ];
    
    for (const selector of mainSelectors) {
      const main = element.querySelector(selector);
      if (main) {
        return main.textContent.trim();
      }
    }
    
    // 移除导航、侧边栏等
    const excludeSelectors = [
      'nav', 'header', 'footer', 'aside', '.sidebar',
      '.navigation', '.menu', '.ads', '.advertisement'
    ];
    
    const clone = element.cloneNode(true);
    excludeSelectors.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
    
    return clone.textContent.trim();
  }

  /**
   * @function extractMetadata - 提取元数据
   * @description 从网页中提取元数据
   * @param {HTMLElement} element - DOM元素
   * @returns {Object} 元数据
   */
  extractMetadata(element) {
    const metadata = {};
    
    // 提取标题
    const title = element.querySelector('title, h1')?.textContent?.trim();
    if (title) metadata.title = title;
    
    // 提取描述
    const description = element.querySelector('meta[name="description"]')?.content;
    if (description) metadata.description = description;
    
    // 提取关键词
    const keywords = element.querySelector('meta[name="keywords"]')?.content;
    if (keywords) metadata.keywords = keywords.split(',').map(k => k.trim());
    
    // 提取作者
    const author = element.querySelector('meta[name="author"], .author')?.textContent?.trim();
    if (author) metadata.author = author;
    
    return metadata;
  }

  /**
   * @function isCode - 检测是否为代码
   * @description 检测文本是否为代码
   * @param {string} text - 文本
   * @returns {boolean} 是否为代码
   */
  isCode(text) {
    const codeIndicators = [
      'function', 'class', 'import', 'export', 'const', 'let', 'var',
      'def', 'public', 'private', 'protected', 'static',
      '{', '}', '()', '=>', '==', '!=', '&&', '||'
    ];
    
    const sample = text.substring(0, 200);
    const indicatorCount = codeIndicators.filter(indicator => 
      sample.includes(indicator)
    ).length;
    
    return indicatorCount >= 3;
  }

  /**
   * @function combineExtractedText - 合并提取的文本
   * @description 将提取的各部分文本合并
   * @param {Object} extracted - 提取的内容
   * @returns {string} 合并的文本
   */
  combineExtractedText(extracted) {
    const parts = [];
    
    if (extracted.title) parts.push(extracted.title);
    if (extracted.paragraphs) parts.push(...extracted.paragraphs);
    if (extracted.headings) parts.push(...extracted.headings);
    if (extracted.content) parts.push(extracted.content);
    if (extracted.text) parts.push(extracted.text);
    
    return parts.join('\n\n');
  }

  /**
   * @function selectTemplate - 选择模板
   * @description 根据内容类型和级别选择合适的模板
   * @param {string} contentType - 内容类型
   * @param {string} level - 摘要级别
   * @returns {string} 模板名称
   */
  selectTemplate(contentType, level) {
    const typeConfig = this.config.contentTypes[contentType];
    if (typeConfig && typeConfig.templates.length > 0) {
      return typeConfig.templates[0];
    }
    return 'article_summary';
  }

  /**
   * @function generateCacheKey - 生成缓存键
   * @description 为摘要生成缓存键
   * @param {string} text - 文本内容
   * @param {string} level - 摘要级别
   * @param {string} type - 内容类型
   * @param {string} template - 模板
   * @returns {string} 缓存键
   */
  generateCacheKey(text, level, type, template) {
    const hash = this.simpleHash(text);
    return `${type}-${level}-${template}-${hash}`;
  }

  /**
   * @function simpleHash - 简单哈希
   * @description 为字符串生成简单哈希
   * @param {string} str - 输入字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * @function cacheResult - 缓存结果
   * @description 将摘要结果缓存
   * @param {string} key - 缓存键
   * @param {Object} result - 摘要结果
   */
  cacheResult(key, result) {
    if (this.summaryCache.size >= this.maxCacheSize) {
      const firstKey = this.summaryCache.keys().next().value;
      this.summaryCache.delete(firstKey);
    }
    
    this.summaryCache.set(key, result);
  }

  /**
   * @function updateStats - 更新统计信息
   * @description 更新摘要统计信息
   * @param {Object} result - 摘要结果
   */
  updateStats(result) {
    this.stats.totalSummaries++;
    
    // 更新平均时间
    const totalTime = this.stats.averageTime * (this.stats.totalSummaries - 1) + result.metadata.processingTime;
    this.stats.averageTime = totalTime / this.stats.totalSummaries;
    
    // 按类型统计
    if (!this.stats.byType[result.contentType]) {
      this.stats.byType[result.contentType] = 0;
    }
    this.stats.byType[result.contentType]++;
    
    // 按级别统计
    if (!this.stats.byLevel[result.level]) {
      this.stats.byLevel[result.level] = 0;
    }
    this.stats.byLevel[result.level]++;
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取摘要器的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      cacheSize: this.summaryCache.size,
      cacheHitRate: this.stats.totalSummaries > 0 ? 
        (this.stats.cacheHits / this.stats.totalSummaries * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function clearCache - 清空缓存
   * @description 清空摘要缓存
   */
  clearCache() {
    this.summaryCache.clear();
    console.log('[内容摘要] 缓存已清空');
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理摘要器使用的资源
   */
  cleanup() {
    this.summaryCache.clear();
    this.extractors.clear();
    console.log('[内容摘要] 内容摘要器已清理');
  }
}

// 导出内容摘要器类
export { AiContentSummarizer };
