/**
 * @file AI侧边栏内存管理器
 * @description 管理Chrome扩展的内存使用，包括自动清理、监控和垃圾回收
 */

/**
 * @class AiMemoryManager
 * @description 内存管理器，负责监控和优化内存使用
 */
class AiMemoryManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化内存管理器
   */
  constructor() {
    // 内存配置
    this.config = {
      // 内存限制 (Chrome扩展通常限制在100MB左右)
      maxMemoryUsage: 80 * 1024 * 1024, // 80MB
      warningThreshold: 60 * 1024 * 1024, // 60MB
      criticalThreshold: 70 * 1024 * 1024, // 70MB
      
      // 清理配置
      autoCleanupInterval: 5 * 60 * 1000, // 5分钟
      maxChatHistoryItems: 100, // 最大聊天记录数
      maxAnalysisResults: 50, // 最大分析结果数
      maxCacheAge: 24 * 60 * 60 * 1000, // 24小时缓存过期
      
      // 监控配置
      monitoringInterval: 30 * 1000, // 30秒监控间隔
      memoryCheckInterval: 60 * 1000 // 1分钟内存检查间隔
    };
    
    // 内存状态
    this.memoryState = {
      currentUsage: 0,
      peakUsage: 0,
      lastCleanup: Date.now(),
      cleanupCount: 0,
      warningCount: 0
    };
    
    // 监控定时器
    this.monitoringTimer = null;
    this.cleanupTimer = null;
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 内存使用记录
    this.memoryHistory = [];
    
    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化内存管理器
   * @description 启动内存监控和自动清理
   */
  async init() {
    try {
      console.log('[内存管理] 初始化内存管理器...');
      
      // 启动内存监控
      this.startMemoryMonitoring();
      
      // 启动自动清理
      this.startAutoCleanup();
      
      // 注册页面卸载事件
      this.registerUnloadHandlers();
      
      // 初始内存检查
      await this.checkMemoryUsage();
      
      console.log('[内存管理] 内存管理器初始化完成');
      this.emit('initialized', this.memoryState);
      
    } catch (error) {
      console.error('[内存管理] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function startMemoryMonitoring - 启动内存监控
   * @description 定期监控内存使用情况
   */
  startMemoryMonitoring() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    
    this.monitoringTimer = setInterval(async () => {
      try {
        await this.checkMemoryUsage();
      } catch (error) {
        console.error('[内存管理] 内存监控错误:', error);
      }
    }, this.config.monitoringInterval);
    
    console.log('[内存管理] 内存监控已启动');
  }

  /**
   * @function startAutoCleanup - 启动自动清理
   * @description 定期执行自动清理任务
   */
  startAutoCleanup() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.cleanupTimer = setInterval(async () => {
      try {
        await this.performAutoCleanup();
      } catch (error) {
        console.error('[内存管理] 自动清理错误:', error);
      }
    }, this.config.autoCleanupInterval);
    
    console.log('[内存管理] 自动清理已启动');
  }

  /**
   * @function checkMemoryUsage - 检查内存使用情况
   * @description 获取当前内存使用情况并触发相应操作
   */
  async checkMemoryUsage() {
    try {
      // 获取内存使用情况
      const memoryInfo = await this.getMemoryInfo();
      
      // 更新内存状态
      this.memoryState.currentUsage = memoryInfo.usedJSHeapSize;
      this.memoryState.peakUsage = Math.max(this.memoryState.peakUsage, memoryInfo.usedJSHeapSize);
      
      // 记录内存历史
      this.memoryHistory.push({
        timestamp: Date.now(),
        usage: memoryInfo.usedJSHeapSize,
        limit: memoryInfo.jsHeapSizeLimit
      });
      
      // 保持历史记录在合理范围内
      if (this.memoryHistory.length > 100) {
        this.memoryHistory = this.memoryHistory.slice(-50);
      }
      
      // 检查内存阈值
      await this.checkMemoryThresholds(memoryInfo);
      
      // 触发内存更新事件
      this.emit('memoryUpdated', {
        current: memoryInfo.usedJSHeapSize,
        limit: memoryInfo.jsHeapSizeLimit,
        percentage: (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100
      });
      
    } catch (error) {
      console.error('[内存管理] 内存检查失败:', error);
    }
  }

  /**
   * @function getMemoryInfo - 获取内存信息
   * @description 获取当前的内存使用信息
   * @returns {Object} 内存信息对象
   */
  async getMemoryInfo() {
    // 使用performance.memory API（如果可用）
    if (performance.memory) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      };
    }
    
    // 回退方案：估算内存使用
    const estimatedUsage = await this.estimateMemoryUsage();
    return {
      usedJSHeapSize: estimatedUsage,
      totalJSHeapSize: estimatedUsage * 1.2,
      jsHeapSizeLimit: this.config.maxMemoryUsage
    };
  }

  /**
   * @function estimateMemoryUsage - 估算内存使用
   * @description 通过存储大小估算内存使用情况
   * @returns {Promise<number>} 估算的内存使用量（字节）
   */
  async estimateMemoryUsage() {
    try {
      // 获取Chrome存储使用情况
      const storageUsage = await this.getStorageUsage();
      
      // 估算DOM和JavaScript对象的内存使用
      const domElements = document.querySelectorAll('*').length;
      const estimatedDomMemory = domElements * 100; // 每个元素约100字节
      
      // 总估算
      return storageUsage + estimatedDomMemory;
    } catch (error) {
      console.warn('[内存管理] 内存估算失败:', error);
      return 10 * 1024 * 1024; // 默认10MB
    }
  }

  /**
   * @function getStorageUsage - 获取存储使用情况
   * @description 计算Chrome存储的使用量
   * @returns {Promise<number>} 存储使用量（字节）
   */
  async getStorageUsage() {
    try {
      let totalSize = 0;
      
      // 检查local storage
      const localData = await chrome.storage.local.get(null);
      totalSize += this.calculateObjectSize(localData);
      
      // 检查sync storage
      const syncData = await chrome.storage.sync.get(null);
      totalSize += this.calculateObjectSize(syncData);
      
      return totalSize;
    } catch (error) {
      console.warn('[内存管理] 存储使用量计算失败:', error);
      return 0;
    }
  }

  /**
   * @function calculateObjectSize - 计算对象大小
   * @description 估算JavaScript对象的内存大小
   * @param {Object} obj - 要计算的对象
   * @returns {number} 对象大小（字节）
   */
  calculateObjectSize(obj) {
    try {
      const jsonString = JSON.stringify(obj);
      return new Blob([jsonString]).size;
    } catch (error) {
      console.warn('[内存管理] 对象大小计算失败:', error);
      return 0;
    }
  }

  /**
   * @function checkMemoryThresholds - 检查内存阈值
   * @description 检查内存使用是否超过阈值并采取相应行动
   * @param {Object} memoryInfo - 内存信息
   */
  async checkMemoryThresholds(memoryInfo) {
    const usage = memoryInfo.usedJSHeapSize;
    
    if (usage > this.config.criticalThreshold) {
      console.warn('[内存管理] 🔴 内存使用达到临界值:', this.formatBytes(usage));
      this.emit('memoryAlert', { level: 'critical', usage: usage });
      
      // 立即执行紧急清理
      await this.performEmergencyCleanup();
      
    } else if (usage > this.config.warningThreshold) {
      console.warn('[内存管理] 🟡 内存使用达到警告值:', this.formatBytes(usage));
      this.memoryState.warningCount++;
      this.emit('memoryAlert', { level: 'warning', usage: usage });
      
      // 执行预防性清理
      if (this.memoryState.warningCount >= 3) {
        await this.performPreventiveCleanup();
        this.memoryState.warningCount = 0;
      }
    }
  }

  /**
   * @function performAutoCleanup - 执行自动清理
   * @description 定期执行的自动清理任务
   */
  async performAutoCleanup() {
    console.log('[内存管理] 🧹 执行自动清理...');
    
    const cleanupTasks = [
      () => this.cleanupChatHistory(),
      () => this.cleanupAnalysisResults(),
      () => this.cleanupExpiredCache(),
      () => this.cleanupDOMElements(),
      () => this.cleanupEventListeners()
    ];
    
    let cleanedItems = 0;
    
    for (const task of cleanupTasks) {
      try {
        const result = await task();
        cleanedItems += result || 0;
      } catch (error) {
        console.warn('[内存管理] 清理任务失败:', error);
      }
    }
    
    this.memoryState.lastCleanup = Date.now();
    this.memoryState.cleanupCount++;
    
    console.log(`[内存管理] ✅ 自动清理完成，清理了 ${cleanedItems} 个项目`);
    this.emit('cleanupCompleted', { type: 'auto', itemsCleaned: cleanedItems });
  }



  /**
   * @function cleanupChatHistory - 清理聊天历史
   * @description 清理过多的聊天历史记录
   * @param {number} ratio - 清理比例 (0-1)
   * @returns {Promise<number>} 清理的项目数
   */
  async cleanupChatHistory(ratio = 0.3) {
    try {
      const chatData = await chrome.storage.local.get(['ai_chat_history']);
      const chatHistory = chatData.ai_chat_history || [];
      
      if (chatHistory.length <= this.config.maxChatHistoryItems) {
        return 0;
      }
      
      const itemsToRemove = Math.floor(chatHistory.length * ratio);
      const cleanedHistory = chatHistory.slice(itemsToRemove);
      
      await chrome.storage.local.set({ ai_chat_history: cleanedHistory });
      
      console.log(`[内存管理] 清理了 ${itemsToRemove} 条聊天记录`);
      return itemsToRemove;
    } catch (error) {
      console.error('[内存管理] 聊天历史清理失败:', error);
      return 0;
    }
  }

  /**
   * @function cleanupAnalysisResults - 清理分析结果
   * @description 清理过多的分析结果
   * @param {number} ratio - 清理比例 (0-1)
   * @returns {Promise<number>} 清理的项目数
   */
  async cleanupAnalysisResults(ratio = 0.3) {
    try {
      const analysisData = await chrome.storage.local.get(['ai_analysis_results']);
      const analysisResults = analysisData.ai_analysis_results || [];
      
      if (analysisResults.length <= this.config.maxAnalysisResults) {
        return 0;
      }
      
      const itemsToRemove = Math.floor(analysisResults.length * ratio);
      const cleanedResults = analysisResults.slice(itemsToRemove);
      
      await chrome.storage.local.set({ ai_analysis_results: cleanedResults });
      
      console.log(`[内存管理] 清理了 ${itemsToRemove} 个分析结果`);
      return itemsToRemove;
    } catch (error) {
      console.error('[内存管理] 分析结果清理失败:', error);
      return 0;
    }
  }

  /**
   * @function cleanupExpiredCache - 清理过期缓存
   * @description 清理过期的缓存数据
   * @param {number} ageRatio - 年龄比例 (0-1, 0表示清理所有)
   * @returns {Promise<number>} 清理的项目数
   */
  async cleanupExpiredCache(ageRatio = 1) {
    try {
      const now = Date.now();
      const maxAge = this.config.maxCacheAge * ageRatio;
      
      // 清理Notion缓存
      const notionCache = await chrome.storage.local.get(['ai_sidebar_notion_cache']);
      let cleanedItems = 0;
      
      if (notionCache.ai_sidebar_notion_cache) {
        const cache = notionCache.ai_sidebar_notion_cache;
        const cleanedCache = {};
        
        for (const [key, value] of Object.entries(cache)) {
          if (value.lastEdited && (now - value.lastEdited) < maxAge) {
            cleanedCache[key] = value;
          } else {
            cleanedItems++;
          }
        }
        
        await chrome.storage.local.set({ ai_sidebar_notion_cache: cleanedCache });
      }
      
      console.log(`[内存管理] 清理了 ${cleanedItems} 个过期缓存项`);
      return cleanedItems;
    } catch (error) {
      console.error('[内存管理] 缓存清理失败:', error);
      return 0;
    }
  }

  /**
   * @function cleanupDOMElements - 清理DOM元素
   * @description 清理不必要的DOM元素和事件监听器
   * @returns {Promise<number>} 清理的项目数
   */
  async cleanupDOMElements() {
    try {
      let cleanedItems = 0;
      
      // 清理隐藏的元素
      const hiddenElements = document.querySelectorAll('[style*="display: none"]');
      hiddenElements.forEach(element => {
        if (element.dataset.keepHidden !== 'true') {
          element.remove();
          cleanedItems++;
        }
      });
      
      // 清理空的容器
      const emptyContainers = document.querySelectorAll('div:empty, span:empty');
      emptyContainers.forEach(element => {
        if (!element.hasAttribute('data-keep-empty')) {
          element.remove();
          cleanedItems++;
        }
      });
      
      // DOM元素清理完成
      return cleanedItems;
    } catch (error) {
      console.error('[内存管理] DOM清理失败:', error);
      return 0;
    }
  }

  /**
   * @function cleanupEventListeners - 清理事件监听器
   * @description 清理未使用的事件监听器
   * @returns {Promise<number>} 清理的项目数
   */
  async cleanupEventListeners() {
    try {
      let cleanedItems = 0;
      
      // 清理内部事件监听器
      for (const [event, listeners] of this.eventListeners.entries()) {
        const activeListeners = listeners.filter(listener => {
          // 检查监听器是否仍然有效
          return typeof listener === 'function';
        });
        
        if (activeListeners.length !== listeners.length) {
          this.eventListeners.set(event, activeListeners);
          cleanedItems += listeners.length - activeListeners.length;
        }
      }
      
      console.log(`[内存管理] 清理了 ${cleanedItems} 个事件监听器`);
      return cleanedItems;
    } catch (error) {
      console.error('[内存管理] 事件监听器清理失败:', error);
      return 0;
    }
  }

  /**
   * @function forceGarbageCollection - 强制垃圾回收
   * @description 尝试触发垃圾回收（如果可能）
   * @returns {Promise<number>} 总是返回1表示执行了操作
   */
  async forceGarbageCollection() {
    try {
      // 创建大量临时对象然后释放，希望触发GC
      const tempObjects = [];
      for (let i = 0; i < 1000; i++) {
        tempObjects.push(new Array(1000).fill(Math.random()));
      }
      
      // 清空数组，释放内存
      tempObjects.length = 0;
      
      // 如果可用，调用gc()
      if (typeof gc === 'function') {
        gc();
      }
      
      // 垃圾回收触发完成
      return 1;
    } catch (error) {
      console.warn('[内存管理] 垃圾回收触发失败:', error);
      return 0;
    }
  }

  /**
   * @function registerUnloadHandlers - 注册页面卸载处理器
   * @description 在页面卸载时清理资源
   */
  registerUnloadHandlers() {
    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
    
    // 扩展卸载时清理
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onSuspend?.addListener(() => {
        this.cleanup();
      });
    }
  }

  /**
   * @function getMemoryStatus - 获取内存状态
   * @description 获取当前内存管理状态
   * @returns {Object} 内存状态信息
   */
  getMemoryStatus() {
    return {
      ...this.memoryState,
      config: this.config,
      history: this.memoryHistory.slice(-10), // 最近10条记录
      thresholds: {
        warning: this.config.warningThreshold,
        critical: this.config.criticalThreshold,
        max: this.config.maxMemoryUsage
      }
    };
  }

  /**
   * @function formatBytes - 格式化字节数
   * @description 将字节数格式化为可读的字符串
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的字符串
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * @function on - 添加事件监听器
   * @description 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * @function emit - 触发事件
   * @description 触发事件并调用所有监听器
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[内存管理] 事件监听器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * @function performEmergencyCleanup - 执行紧急清理
   * @description 在内存使用达到临界值时执行紧急清理
   * @returns {Promise<Object>} 清理结果
   */
  async performEmergencyCleanup() {
    console.warn('[内存管理] 🚨 执行紧急内存清理...');

    const cleanupResult = {
      freedMemory: 0,
      cleanedItems: 0,
      actions: []
    };

    try {
      // 强制垃圾回收（如果可用）
      if (typeof window !== 'undefined' && window.gc) {
        window.gc();
        cleanupResult.actions.push('强制垃圾回收');
      }

      // 清空所有缓存
      const cacheSize = this.memoryCache?.size || 0;
      if (this.memoryCache) {
        this.memoryCache.clear();
        cleanupResult.cleanedItems += cacheSize;
        cleanupResult.actions.push(`清空缓存 (${cacheSize} 项)`);
      }

      // 清理DOM元素
      const domCleaned = this.cleanupDOMElements(true);
      cleanupResult.cleanedItems += domCleaned;
      if (domCleaned > 0) {
        cleanupResult.actions.push(`清理DOM元素 (${domCleaned} 个)`);
      }

      // 清理事件监听器
      const listenersCleaned = this.cleanupEventListeners(true);
      cleanupResult.cleanedItems += listenersCleaned;
      if (listenersCleaned > 0) {
        cleanupResult.actions.push(`清理事件监听器 (${listenersCleaned} 个)`);
      }

      // 清理性能历史
      if (this.memoryHistory) {
        this.memoryHistory = [];
        cleanupResult.actions.push('清空性能历史');
      }

      // 更新统计
      this.stats.emergencyCleanups = (this.stats.emergencyCleanups || 0) + 1;

      console.warn(`[内存管理] 🚨 紧急清理完成: ${cleanupResult.actions.join(', ')}`);

      return cleanupResult;
    } catch (error) {
      console.error('[内存管理] 紧急清理失败:', error);
      throw error;
    }
  }

  /**
   * @function performPreventiveCleanup - 执行预防性清理
   * @description 执行预防性内存清理，避免内存使用过高
   * @returns {Promise<Object>} 清理结果
   */
  async performPreventiveCleanup() {
    console.log('[内存管理] 🛡️ 执行预防性内存清理...');

    const cleanupResult = {
      freedMemory: 0,
      cleanedItems: 0,
      actions: []
    };

    try {
      // 清理过期缓存
      const expiredItems = this.cleanupExpiredCache();
      cleanupResult.cleanedItems += expiredItems;
      if (expiredItems > 0) {
        cleanupResult.actions.push(`清理过期缓存 (${expiredItems} 项)`);
      }

      // 清理未使用的DOM元素
      const domCleaned = this.cleanupDOMElements(false);
      cleanupResult.cleanedItems += domCleaned;
      if (domCleaned > 0) {
        cleanupResult.actions.push(`清理DOM元素 (${domCleaned} 个)`);
      }

      // 清理无效的事件监听器
      const listenersCleaned = this.cleanupEventListeners(false);
      cleanupResult.cleanedItems += listenersCleaned;
      if (listenersCleaned > 0) {
        cleanupResult.actions.push(`清理事件监听器 (${listenersCleaned} 个)`);
      }

      // 限制性能历史大小
      if (this.memoryHistory && this.memoryHistory.length > 50) {
        const removed = this.memoryHistory.length - 50;
        this.memoryHistory = this.memoryHistory.slice(-50);
        cleanupResult.cleanedItems += removed;
        cleanupResult.actions.push(`限制性能历史 (移除${removed}项)`);
      }

      // 更新统计
      this.stats.preventiveCleanups = (this.stats.preventiveCleanups || 0) + 1;

      if (cleanupResult.actions.length > 0) {
        console.log(`[内存管理] 🛡️ 预防性清理完成: ${cleanupResult.actions.join(', ')}`);
      }

      return cleanupResult;
    } catch (error) {
      console.error('[内存管理] 预防性清理失败:', error);
      throw error;
    }
  }

  /**
   * @function cleanupExpiredCache - 清理过期缓存
   * @description 清理过期的缓存项
   * @returns {number} 清理的项目数
   */
  cleanupExpiredCache() {
    if (!this.memoryCache) return 0;

    let cleanedCount = 0;
    const now = Date.now();

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.expiry && now > item.expiry) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理内存管理器使用的资源
   */
  cleanup() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    this.eventListeners.clear();
    this.memoryHistory = [];

    console.log('[内存管理] 内存管理器已清理');
  }
}

// 导出内存管理器类
export { AiMemoryManager };
