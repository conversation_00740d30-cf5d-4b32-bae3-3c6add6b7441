/**
 * @fileoverview 内容捕获脚本，负责从页面提取信息并发送到后台
 * @version 2.0
 * <AUTHOR>
 * @date 2024-07-31
 * @description 在网页中注入的内容脚本，负责捕获页面内容、监听变化并与后台服务通信
 */

// #region 全局变量和常量定义
/**
 * 内容捕获引擎实例
 * @type {AiContentCaptureEngine}
 */
let contentCaptureEngine = null;

/**
 * 光标增强器实例
 * @type {AiCursorEnhancer}
 */
let cursorEnhancerInstance = null;

/**
 * 页面观察器实例
 * @type {MutationObserver}
 */
let pageObserver = null;

/**
 * 捕获配置常量
 */
const AI_CONTENT_CAPTURE_CONFIG = {
  // 内容捕获延迟时间 (毫秒)
  CAPTURE_DELAY: 1000,
  // 最大内容长度
  MAX_CONTENT_LENGTH: 50000,
  // 最小有效文本长度
  MIN_TEXT_LENGTH: 10,
  // 忽略的标签
  IGNORED_TAGS: ['SCRIPT', 'STYLE', 'NOSCRIPT', 'META', 'LINK'],
  // 重要内容选择器
  IMPORTANT_SELECTORS: [
    'article', 'main', '.content', '.post', '.article',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'p', 'blockquote', 'table', 'ul', 'ol', 'dl'
  ]
};
// #endregion

// #region 核心内容捕获引擎类
/**
 * @class AiContentCaptureEngine - AI内容捕获引擎
 * @description 负责智能捕获和分析网页内容
 */
class AiContentCaptureEngine {
  /**
   * @function constructor - 构造函数
   * @description 初始化内容捕获引擎
   */
  constructor() {
    this.lastCaptureTime = 0;
    this.captureTimeout = null;
    this.isCapturing = false;
    this.lastContentHash = null;
    
    // 绑定方法上下文
    this.handlePageChange = this.handlePageChange.bind(this);
    this.performCapture = this.performCapture.bind(this);
  }

  /**
   * @function init - 初始化捕获引擎
   * @description 设置页面监听器和初始内容捕获
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[AI内容捕获] 初始化内容捕获引擎...');
      
      // 设置页面变化监听器
      this.setupPageObserver();
      
      // 监听页面加载完成
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', this.handlePageChange);
      } else {
        // 页面已加载完成，直接捕获
        this.scheduleCapture();
      }
      
      // 监听页面可见性变化
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          this.scheduleCapture();
        }
      });
      
      console.log('[AI内容捕获] 内容捕获引擎初始化完成');
    } catch (error) {
      console.error('[AI内容捕获] 初始化失败:', error);
    }
  }

  /**
   * @function setupPageObserver - 设置页面变化观察器
   * @description 监听DOM变化，智能检测内容更新
   */
  setupPageObserver() {
    if (pageObserver) {
      pageObserver.disconnect();
    }

    pageObserver = new MutationObserver((mutations) => {
      let shouldCapture = false;
      
      for (const mutation of mutations) {
        if (this.isSignificantChange(mutation)) {
          shouldCapture = true;
          break;
        }
      }
      
      if (shouldCapture) {
        this.scheduleCapture();
      }
    });

    // 开始观察页面变化
    pageObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      attributeOldValue: false,
      characterData: true,
      characterDataOldValue: false
    });
  }

  /**
   * @function scheduleCapture - 安排内容捕获
   * @description 使用防抖机制安排内容捕获，避免频繁触发
   */
  scheduleCapture() {
    if (this.captureTimeout) {
      clearTimeout(this.captureTimeout);
    }
    
    this.captureTimeout = setTimeout(() => {
      this.performCapture();
    }, AI_CONTENT_CAPTURE_CONFIG.CAPTURE_DELAY);
  }

  /**
   * @function performCapture - 执行内容捕获
   * @description 捕获当前页面的结构化内容
   * @returns {Promise<void>}
   */
  async performCapture() {
    if (this.isCapturing) {
      return;
    }
    
    this.isCapturing = true;
    
    try {
      const captureStartTime = performance.now();
      console.log('[AI内容捕获] 开始捕获页面内容...');
      
      // 捕获基础页面信息
      const pageInfo = this.capturePageInfo();
      
      // 捕获文本内容
      const textContent = this.captureTextContent();
      
      // 构建完整的内容对象
      const capturedContent = {
        timestamp: Date.now(),
        url: window.location.href,
        pageInfo,
        textContent,
        captureTime: performance.now() - captureStartTime
      };
      
      // 检查内容是否有变化
      const contentHash = this.generateContentHash(capturedContent);
      if (contentHash === this.lastContentHash) {
        console.log('[AI内容捕获] 内容无变化，跳过发送');
        return;
      }
      
      this.lastContentHash = contentHash;
      
      // 发送内容到后台服务
      await this.sendContentToBackground(capturedContent);
      
      console.log(`[AI内容捕获] 内容捕获完成，耗时: ${capturedContent.captureTime.toFixed(2)}ms`);
      
    } catch (error) {
      console.error('[AI内容捕获] 内容捕获失败:', error);
    } finally {
      this.isCapturing = false;
      this.lastCaptureTime = Date.now();
    }
  }

  /**
   * @function capturePageInfo - 捕获页面基础信息
   * @description 获取页面标题、描述、关键词等元信息
   * @returns {Object} 页面信息对象
   */
  capturePageInfo() {
    return {
      title: document.title || '',
      description: this.getMetaContent('description') || '',
      language: document.documentElement.lang || 'zh-CN'
    };
  }

  /**
   * @function getMetaContent - 获取meta标签内容
   * @description 根据name或property获取meta标签的content值
   * @param {string} name - meta标签的name或property值
   * @returns {string} meta标签的content值
   */
  getMetaContent(name) {
    const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
    return meta ? meta.getAttribute('content') : '';
  }

  /**
   * @function captureTextContent - 捕获文本内容
   * @description 智能提取页面的主要文本内容
   * @returns {Object} 文本内容对象
   */
  captureTextContent() {
    const mainContent = this.extractMainContent();
    
    return {
      mainContent: mainContent.substring(0, AI_CONTENT_CAPTURE_CONFIG.MAX_CONTENT_LENGTH)
    };
  }

  /**
   * @function extractMainContent - 提取主要内容
   * @description 使用多种策略提取页面的主要文本内容
   * @returns {string} 主要内容文本
   */
  extractMainContent() {
    // 尝试多种主内容选择器
    const contentSelectors = [
      'article',
      'main',
      '[role="main"]',
      '.content',
      '.post'
    ];
    
    for (const selector of contentSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return this.extractTextFromElement(element);
      }
    }
    
    // 如果没有找到特定的内容容器，使用body
    return this.extractTextFromElement(document.body);
  }

  /**
   * @function extractTextFromElement - 从元素提取文本
   * @description 递归提取元素及其子元素的文本内容，过滤噪声
   * @param {Element} element - DOM元素
   * @returns {string} 提取的文本内容
   */
  extractTextFromElement(element) {
    let text = '';
    
    for (const node of element.childNodes) {
      if (node.nodeType === Node.TEXT_NODE) {
        const nodeText = node.textContent.trim();
        if (nodeText) {
          text += nodeText + ' ';
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName;
        
        // 跳过忽略的标签
        if (AI_CONTENT_CAPTURE_CONFIG.IGNORED_TAGS.includes(tagName)) {
          continue;
        }
        
        // 递归处理子元素
        const childText = this.extractTextFromElement(node);
        if (childText.trim()) {
          text += childText + ' ';
        }
      }
    }
    
    return text.trim();
  }

  /**
   * @function generateContentHash - 生成内容哈希
   * @description 为内容生成简单的哈希值，用于检测变化
   * @param {Object} content - 内容对象
   * @returns {string} 内容哈希值
   */
  generateContentHash(content) {
    const key = `${content.pageInfo.title}_${content.textContent.mainContent.substring(0, 1000)}`;
    let hash = 0;
    
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return hash.toString();
  }

  /**
   * @function sendContentToBackground - 发送内容到后台服务
   * @description 将捕获的内容发送到background script
   * @param {Object} content - 捕获的内容
   * @returns {Promise<void>}
   */
  async sendContentToBackground(content) {
    // 防御性编程：确保content和textContent的类型安全
    if (!content ||
        !content.textContent ||
        !content.textContent.mainContent ||
        typeof content.textContent.mainContent !== 'string' ||
        !content.textContent.mainContent.trim()) {
      console.log('[AI内容捕获] 内容为空或格式无效，跳过发送');
      return;
    }

    try {
      // 使用全局MESSAGE_TYPES常量，支持多种访问方式
      const messageType = window.MESSAGE_TYPES?.CONTENT_CAPTURED ||
                         globalThis.MESSAGE_TYPES?.CONTENT_CAPTURED ||
                         'ai:content:captured'; // 降级到硬编码字符串

      const response = await chrome.runtime.sendMessage({
        type: messageType,
        payload: content,
      });
      console.log('后台响应:', response);

      if (response && response.success) {
        console.log('[AI内容捕获] 内容发送成功');
      } else {
        console.error('[AI内容捕获] 内容发送失败:', response?.error);
      }
    } catch (error) {
      console.error('[AI内容捕获] 发送内容到后台失败:', error);
    }
  }

  /**
   * @function handlePageChange - 处理页面变化
   * @description 页面加载或变化时的处理函数
   */
  handlePageChange() {
    console.log('[AI内容捕获] 检测到页面变化');
    this.scheduleCapture();
  }

  /**
   * @function isSignificantChange - 判断是否为重要变化
   * @description 检查DOM变化是否值得重新捕获内容
   * @param {MutationRecord} mutation - DOM变化记录
   * @returns {boolean} 是否为重要变化
   */
  isSignificantChange(mutation) {
    // 检查节点类型
    if (mutation.type === 'childList') {
      for (const node of mutation.addedNodes) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const tagName = node.tagName;
          // 忽略脚本和样式变化
          if (AI_CONTENT_CAPTURE_CONFIG.IGNORED_TAGS.includes(tagName)) {
            continue;
          }
          
          // 检查是否包含重要文本
          if (this.hasSignificantText(node)) {
            return true;
          }
        }
      }
    }
    
    return false;
  }

  /**
   * @function hasSignificantText - 检查元素是否包含重要文本
   * @description 判断元素及其子元素是否包含足够长度的文本内容
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否包含重要文本
   */
  hasSignificantText(element) {
    const textContent = element.textContent || '';
    return textContent.trim().length > AI_CONTENT_CAPTURE_CONFIG.MIN_TEXT_LENGTH;
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理监听器和定时器
   */
  cleanup() {
    if (this.captureTimeout) {
      clearTimeout(this.captureTimeout);
    }
    
    if (pageObserver) {
      pageObserver.disconnect();
    }
    
    document.removeEventListener('DOMContentLoaded', this.handlePageChange);
    document.removeEventListener('visibilitychange', this.handlePageChange);
  }
}
// #endregion

// #region 脚本初始化和事件处理
/**
 * @function initContentCaptureScript - 初始化内容捕获脚本
 * @description 创建并启动内容捕获引擎和光标增强器
 */
async function initContentCaptureScript() {
  try {
    console.log('[AI内容捕获] 初始化内容捕获脚本...');
    
    // 创建内容捕获引擎实例
    contentCaptureEngine = new AiContentCaptureEngine();
    
    // 初始化捕获引擎
    await contentCaptureEngine.init();
    
    console.log('[AI内容捕获] 内容捕获引擎初始化完成');
    
    // 初始化光标增强器
    await initCursorEnhancer();
    
    console.log('[AI内容捕获] 内容捕获脚本完全初始化完成');
  } catch (error) {
    console.error('[AI内容捕获] 脚本初始化失败:', error);
  }
}

/**
 * @function initCursorEnhancer - 初始化光标增强器
 * @description 在Content Script中创建并初始化光标增强器
 */
async function initCursorEnhancer() {
  try {
    console.log('[光标增强] 开始初始化光标增强器...');
    
    // 动态导入光标增强器类
    // 注意：这需要光标增强器被正确导出为ES模块
    try {
      // 如果AiCursorEnhancer类已经可用，直接使用
      if (typeof AiCursorEnhancer !== 'undefined') {
        cursorEnhancerInstance = new AiCursorEnhancer(null, null);
        await cursorEnhancerInstance.init();
        console.log('[光标增强] 光标增强器初始化完成');
      } else {
        console.log('[光标增强] AiCursorEnhancer类不可用，跳过初始化');
      }
    } catch (error) {
      console.error('[光标增强] 初始化失败:', error);
      // 不抛出错误，允许Content Script继续运行
    }
  } catch (error) {
    console.error('[光标增强] 初始化过程失败:', error);
  }
}

// 监听来自background的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理内容捕获消息
  if (message.type === 'ai:capture:manual') {
    // 手动触发内容捕获
    if (contentCaptureEngine) {
      contentCaptureEngine.performCapture()
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
    } else {
      sendResponse({ success: false, error: '内容捕获引擎未初始化' });
    }
    return true; // 保持消息通道开放
  }
  
  // 处理光标增强器转发消息
  if (message.type === 'ai:cursor:forward') {
    handleCursorEnhancementMessage(message, sendResponse);
    return true; // 保持消息通道开放
  }
});

/**
 * @function handleCursorEnhancementMessage - 处理光标增强器消息
 * @description 处理从Background转发来的光标增强器操作消息
 * @param {Object} message - 消息对象
 * @param {Function} sendResponse - 响应函数
 */
async function handleCursorEnhancementMessage(message, sendResponse) {
  try {
    const { originalType, data } = message;
    
    if (!cursorEnhancerInstance) {
      console.warn('[光标增强] 光标增强器未初始化，尝试重新初始化...');
      await initCursorEnhancer();
      
      if (!cursorEnhancerInstance) {
        sendResponse({ 
          success: false, 
          error: '光标增强器初始化失败' 
        });
        return;
      }
    }
    
    let result;
    switch (originalType) {
      case 'ai:cursor:enhance':
        result = await cursorEnhancerInstance.enableEnhancement(data?.options);
        break;
        
      case 'ai:cursor:predict':
        result = await cursorEnhancerInstance.generateSuggestions(
          data?.inputText, 
          data?.context, 
          data?.element
        );
        break;
        
      case 'ai:cursor:disable':
        result = await cursorEnhancerInstance.disableEnhancement();
        break;
        
      case 'ai:cursor:config':
        result = await cursorEnhancerInstance.updateConfiguration(data?.config);
        break;
        
      default:
        throw new Error(`不支持的光标增强操作: ${originalType}`);
    }
    
    sendResponse({ success: true, data: result });
  } catch (error) {
    console.error('[光标增强] 操作失败:', error);
    sendResponse({ 
      success: false, 
      error: error.message 
    });
  }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  if (contentCaptureEngine) {
    contentCaptureEngine.cleanup();
  }
  
  if (cursorEnhancerInstance) {
    try {
      cursorEnhancerInstance.cleanup();
    } catch (error) {
      console.error('[光标增强] 清理资源失败:', error);
    }
  }
});
// #endregion

// 启动内容捕获脚本
initContentCaptureScript().catch(error => {
  console.error('[AI内容捕获] 启动失败:', error);
}); 