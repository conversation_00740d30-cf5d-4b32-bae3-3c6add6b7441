<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI侧边栏助手</title>
  <link rel="stylesheet" href="aiSidebarStyles.css">
</head>
<body>
  <!-- #region 主容器 - 简化版本 -->
  <div id="ai-sidebar-container" class="ai-sidebar">

    <!-- #region 精简顶部栏 -->
    <header class="ai-sidebar__header">
      <div class="ai-sidebar__logo">
        <div class="ai-sidebar__logo-icon">🤖</div>
        <span class="ai-sidebar__logo-text">AI助手</span>
      </div>
      <div class="ai-sidebar__header-actions">
        <button type="button" id="ai-sidebar-settings-btn" class="ai-sidebar__action-btn" title="统一设置">
          <span class="ai-icon">⚙️</span>
        </button>
      </div>
    </header>
    <!-- #endregion -->

    <!-- #region 快捷模板系统 - 优化版本 -->
    <div class="ai-templates-bar">
      <div class="ai-templates-scroll">
        <button type="button" class="ai-template-item" data-template="summary" title="页面总结">
          <span class="ai-template-icon">📝</span>
          <span class="ai-template-name">总结</span>
        </button>
        <button type="button" class="ai-template-item" data-template="translate" title="内容翻译">
          <span class="ai-template-icon">🌐</span>
          <span class="ai-template-name">翻译</span>
        </button>
        <button type="button" class="ai-template-item" data-template="analyze" title="深度分析">
          <span class="ai-template-icon">🔍</span>
          <span class="ai-template-name">分析</span>
        </button>
        <button type="button" class="ai-template-item" data-template="reply" title="智能回复">
          <span class="ai-template-icon">💬</span>
          <span class="ai-template-name">回复</span>
        </button>
        <button type="button" class="ai-template-item" data-template="mindmap" title="思维导图">
          <span class="ai-template-icon">🧠</span>
          <span class="ai-template-name">导图</span>
        </button>
      </div>
      <button type="button" id="ai-templates-more-btn" class="ai-templates-more" title="模板管理">
        <span class="ai-icon">⋯</span>
      </button>
    </div>
    <!-- #endregion -->

    <!-- #region 主对话区域 - 对话中心化设计 -->
    <main class="ai-sidebar__main">
      <div class="ai-chat">
        <!-- 对话消息容器 - 占据主要空间 -->
        <div id="ai-chat-messages" class="ai-chat__messages">
          <!-- 欢迎消息 - 带悬浮操作菜单 -->
          <div class="ai-chat__message ai-chat__message--assistant">
            <div class="ai-chat__bubble">
              <div class="ai-chat__content">
                <div class="ai-chat__text">
                  你好！我是你的AI助手。我可以帮你分析网页内容、生成摘要、创建思维导图等。请告诉我需要什么帮助？
                </div>
              </div>
              <!-- 悬浮操作菜单 - 鼠标悬停显示 -->
              <div class="ai-chat__hover-actions">
                <button type="button" class="ai-action-btn" data-action="copy" title="复制内容">
                  <span class="ai-icon">📋</span>
                </button>
                <button type="button" class="ai-action-btn" data-action="translate" title="翻译内容">
                  <span class="ai-icon">🌐</span>
                </button>
                <button type="button" class="ai-action-btn" data-action="notion" title="保存到Notion">
                  <span class="ai-icon">📝</span>
                </button>
              </div>
            </div>
            <div class="ai-chat__time">刚刚</div>
          </div>
        </div>

        <!-- 输入区域 - 优化布局 -->
        <div class="ai-chat__input-area">
          <!-- 多风格回复选择器 - 紧凑设计 -->
          <div class="ai-reply-style-selector">
            <select id="ai-reply-style" class="ai-reply-style-select" title="选择回复风格">
              <option value="professional">专业客服</option>
              <option value="friendly">友好礼貌</option>
              <option value="detailed">详细解答</option>
              <option value="concise">简洁明了</option>
              <option value="creative">创意回复</option>
            </select>
            <select id="ai-language" class="ai-language-select" title="选择语言">
              <option value="zh">中文</option>
              <option value="en">English</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
            </select>
          </div>

          <!-- 输入框容器 - 移除发送按钮，Enter键发送 -->
          <div class="ai-chat__input-container">
            <textarea
              id="ai-chat-input"
              class="ai-chat__input"
              placeholder="输入你的问题或需求... (按Enter发送，Shift+Enter换行)"
              rows="1"
            ></textarea>
          </div>
        </div>
      </div>
    </main>
    <!-- #endregion -->

    <!-- #region 统一设置模态框 -->
    <div id="ai-settings-modal" class="ai-modal hidden">
      <div class="ai-modal__overlay"></div>
      <div class="ai-modal__content">
        <div class="ai-modal__header">
          <h3 class="ai-modal__title">统一设置</h3>
          <button type="button" id="ai-settings-close-btn" class="ai-modal__close">
            <span class="ai-icon">✕</span>
          </button>
        </div>
        <div class="ai-modal__body">
          <div class="ai-settings-content">

            <!-- API配置状态 -->
            <div class="ai-setting-section">
              <h4 class="ai-setting-section__title">AI接口状态</h4>
              <div class="ai-setting-item">
                <span class="ai-setting-item__label">Gemini API</span>
                <div class="ai-status-indicator" id="ai-gemini-status">
                  <span class="ai-status-dot ai-status-dot--loading"></span>
                  <span class="ai-status-text">检查中...</span>
                </div>
              </div>
              <div class="ai-setting-item">
                <span class="ai-setting-item__label">配置来源</span>
                <span class="ai-config-source">config.js</span>
              </div>
            </div>

            <!-- Notion集成状态 -->
            <div class="ai-setting-section">
              <h4 class="ai-setting-section__title">Notion云端同步状态</h4>
              <div class="ai-setting-item">
                <span class="ai-setting-item__label">连接状态</span>
                <div class="ai-status-indicator" id="ai-notion-status">
                  <span class="ai-status-dot ai-status-dot--loading"></span>
                  <span class="ai-status-text">检查中...</span>
                </div>
              </div>
              <div class="ai-setting-item">
                <span class="ai-setting-item__label">知识库状态</span>
                <div class="ai-status-indicator" id="ai-knowledge-status">
                  <span class="ai-status-dot ai-status-dot--loading"></span>
                  <span class="ai-status-text">检查中...</span>
                </div>
              </div>
              <div class="ai-setting-item">
                <span class="ai-setting-item__label">配置来源</span>
                <span class="ai-config-source">config.js</span>
              </div>
            </div>

            <!-- 功能开关 -->
            <div class="ai-setting-section">
              <h4 class="ai-setting-section__title">功能开关</h4>
              <div class="ai-setting-item">
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-auto-analysis" checked>
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">自动页面分析（默认开启）</span>
                </label>
              </div>
              <div class="ai-setting-item">
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-cursor-enhance" checked>
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">光标增强功能（默认开启）</span>
                </label>
              </div>
              <div class="ai-setting-item">
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-notion-sync" checked>
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">自动同步到Notion</span>
                </label>
              </div>
            </div>

          </div>
        </div>
        <div class="ai-modal__footer">
          <button type="button" id="ai-settings-reset-btn" class="ai-btn">重置默认</button>
          <button type="button" id="ai-settings-save-btn" class="ai-btn ai-btn--primary">保存设置</button>
        </div>
      </div>
    </div>
    <!-- #endregion -->

  </div>
  <!-- #endregion -->

  <!-- JavaScript模块 -->
  <script type="module" src="aiSidebarPanel.js"></script>
</body>
</html>
