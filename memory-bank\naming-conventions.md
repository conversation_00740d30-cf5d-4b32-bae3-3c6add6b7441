# 命名规范 - Naming Conventions

## 命名基本原则
1. **语义明确性**: 名称必须清晰表达其功能和用途
2. **业务前缀**: 所有通用功能必须加业务前缀 `ai` 或 `sidebar`
3. **唯一性保证**: 同一概念在整个项目中只能有一个实现和命名
4. **一致性要求**: 相同类型的实体使用相同的命名模式

## 核心命名表

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `aiSidebarCore` | 模块 | 核心业务逻辑管理 | `/src/core/` | 基础依赖 |
| `aiContentCapture` | 模块 | 网页内容捕获引擎 | `/src/content/` | aiSidebarCore |
| `aiChatInterface` | 模块 | AI对话交互界面 | `/src/ui/chat/` | aiSidebarCore |
| `aiTemplateManager` | 模块 | 模板管理系统 | `/src/templates/` | aiSidebarCore |
| `aiNotionConnector` | 模块 | Notion API集成 | `/src/integrations/` | aiSidebarCore |
| `aiSecurityManager` | 模块 | 安全管理模块 | `/src/security/` | 独立模块 |
| `aiPerformanceMonitor` | 模块 | 性能监控模块 | `/src/performance/` | 独立模块 |

## 文件命名规范

### 1. 目录结构命名
```
src/
├── core/                     # 核心业务逻辑
├── content/                  # 内容捕获相关
├── ui/                       # 用户界面组件
│   ├── chat/                # 聊天界面
│   ├── sidebar/             # 侧边栏界面
│   ├── popup/               # 弹窗界面
│   └── components/          # 通用组件
├── integrations/            # 第三方集成
├── security/                # 安全相关
├── performance/             # 性能监控
├── utils/                   # 工具函数
└── types/                   # TypeScript类型定义
```

### 2. JavaScript/TypeScript文件命名
- **模块主文件**: `{模块名}.js` 例如：`aiSidebarCore.js`
- **组件文件**: `{组件名}Component.js` 例如：`aiChatComponent.js`
- **工具文件**: `{功能名}Utils.js` 例如：`aiContentUtils.js`
- **类型文件**: `{模块名}Types.ts` 例如：`aiCoreTypes.ts`
- **配置文件**: `{功能名}Config.js` 例如：`aiApiConfig.js`

### 3. CSS文件命名
- **全局样式**: `aiSidebarGlobal.css`
- **模块样式**: `{模块名}Styles.css` 例如：`aiChatStyles.css`
- **组件样式**: `{组件名}Component.css` 例如：`aiButtonComponent.css`

## 变量和函数命名规范

### 1. 变量命名
- **常量**: `AI_SIDEBAR_MAX_CONTENT_LENGTH`
- **配置对象**: `aiSidebarConfig`
- **状态变量**: `aiChatCurrentState`
- **DOM元素**: `aiSidebarContainer`
- **事件名称**: `aiContentCaptured`

### 2. 函数命名
- **初始化函数**: `initAiSidebar()`
- **事件处理**: `handleAiChatMessage()`
- **数据处理**: `processAiContent()`
- **API调用**: `callAiApiEndpoint()`
- **工具函数**: `formatAiResponse()`

### 3. 类和构造函数命名
- **核心类**: `AiSidebarCore`
- **组件类**: `AiChatInterfaceComponent`
- **管理器类**: `AiTemplateManager`
- **连接器类**: `AiNotionConnector`

## CSS类命名规范 (BEM方法论)

### 1. 基础命名模式
```css
.ai-sidebar                   /* 块(Block) */
.ai-sidebar__header          /* 元素(Element) */
.ai-sidebar__header--active  /* 修饰符(Modifier) */
```

### 2. 组件级别命名
```css
.ai-chat                     /* 聊天组件主容器 */
.ai-chat__message           /* 消息元素 */
.ai-chat__message--user     /* 用户消息修饰符 */
.ai-chat__message--bot      /* AI消息修饰符 */
.ai-chat__input             /* 输入框元素 */
.ai-chat__button            /* 按钮元素 */
.ai-chat__button--send      /* 发送按钮修饰符 */
```

## API和事件命名规范

### 1. API端点命名
- **获取内容**: `getAiPageContent`
- **分析内容**: `analyzeAiContent`
- **生成回复**: `generateAiResponse`
- **保存模板**: `saveAiTemplate`
- **同步数据**: `syncAiNotionData`

### 2. 事件命名
- **内容捕获**: `ai:content:captured`
- **分析完成**: `ai:analysis:completed`
- **消息发送**: `ai:chat:message:sent`
- **模板更新**: `ai:template:updated`
- **错误发生**: `ai:error:occurred`

### 3. 存储键名命名
- **用户配置**: `ai_sidebar_user_config`
- **聊天历史**: `ai_chat_history`
- **模板数据**: `ai_template_data`
- **缓存内容**: `ai_content_cache`

## 命名避免清单

### 禁止使用的通用词汇
❌ `helper`, `utils`, `common`, `shared`
❌ `manager`, `service`, `handler` (除非明确指定业务含义)
❌ `data`, `info`, `item`, `object`
❌ `temp`, `tmp`, `test`, `demo`

### 推荐替代方案
✅ `aiContentProcessor` 替代 `contentHelper`
✅ `aiChatMessageFormatter` 替代 `messageUtils`
✅ `aiSidebarStateController` 替代 `stateManager`
✅ `aiNotionDataSynchronizer` 替代 `dataService`

## 版本控制约定
- **分支命名**: `feature/ai-{功能描述}`
- **提交信息**: `[AI侧边栏] {模块名}: {具体改动}`
- **标签命名**: `v{主版本}.{次版本}.{修订版}-ai-sidebar` 