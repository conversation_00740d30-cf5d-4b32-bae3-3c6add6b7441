/**
 * @file AI接口管理模块
 * @description 管理与各种AI服务提供商的API集成，重点实现Gemini API
 */

import { CONFIG } from '../../config.js';

/**
 * @class AiApiManager - AI接口管理器
 * @description 提供统一的AI服务接口，支持多种AI模型和流式响应
 */
export class AiApiManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化AI接口管理器
   * @param {AiSecurityManager} securityManager - 安全管理器实例
   */
  constructor(securityManager) {
    this.securityManager = securityManager;
    this.isInitialized = false;
    
    // API配置常量
    this.API_PROVIDERS = {
      GEMINI: 'gemini',
      OPENAI: 'openai',
      CLAUDE: 'claude'
    };
    
    this.GEMINI_CONFIG = {
      BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
      MODEL: 'gemini-2.5-flash-lite-preview-06-17',
      MAX_TOKENS: 8192,
      TEMPERATURE: 0.7,
      // 从配置文件获取API密钥
      API_KEY: CONFIG.GEMINI_API_KEY,
      SAFETY_SETTINGS: [
        {
          category: 'HARM_CATEGORY_HARASSMENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        },
        {
          category: 'HARM_CATEGORY_HATE_SPEECH',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        },
        {
          category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        },
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        }
      ]
    };
    
    // 请求配置
    this.REQUEST_CONFIG = {
      TIMEOUT: 30000,
      MAX_RETRIES: 3,
      RETRY_DELAY: 1000
    };
    
    // 缓存和限流
    this.responseCache = new Map();
    this.requestQueue = [];
    this.isProcessingQueue = false;
  }

  /**
   * @function init - 初始化API管理器
   * @description 设置API密钥和配置
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[AI接口管理] 初始化API管理器...');
      
      if (!this.securityManager || !this.securityManager.isInitialized) {
        throw new Error('安全管理器未正确初始化');
      }
      
      // 验证API密钥
      await this.validateApiKeys();
      
      // 启动请求队列处理
      this.startQueueProcessor();
      
      this.isInitialized = true;
      console.log('[AI接口管理] API管理器初始化完成');
    } catch (error) {
      console.error('[AI接口管理] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function validateApiKeys - 验证API密钥
   * @description 检查硬编码的API密钥是否已配置
   * @returns {Promise<void>}
   */
  async validateApiKeys() {
    if (!this.GEMINI_CONFIG.API_KEY || this.GEMINI_CONFIG.API_KEY === 'YOUR_GEMINI_API_KEY_HERE') {
      console.warn('[AI接口管理] 请在config.js文件中配置实际的Gemini API密钥');
    } else {
      console.log('[AI接口管理] Gemini API密钥验证通过');
    }
  }

  /**
   * @function sendChatMessage - 发送聊天消息
   * @description 发送消息到AI模型并获取回复
   * @param {Object} messageData - 消息数据
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} AI回复结果
   */
  async sendChatMessage(messageData, tabId) {
    try {
      const { message, history = [], context = null } = messageData;
      
      // 构建请求载荷
      const requestPayload = this.buildChatRequest(message, history, context);
      
      // 发送到Gemini API
      const response = await this.callGeminiApi('generateContent', requestPayload);
      
      return {
        success: true,
        reply: response.candidates[0]?.content?.parts[0]?.text || '抱歉，我无法生成回复。',
        model: this.GEMINI_CONFIG.MODEL,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[AI接口管理] 聊天消息发送失败:', error);
      return {
        success: false,
        error: error.message,
        fallbackReply: '抱歉，当前AI服务不可用，请稍后再试。'
      };
    }
  }

  /**
   * @function sendStreamingMessage - 发送流式聊天消息
   * @description 发送消息并通过回调函数处理流式响应
   * @param {Object} messageData - 消息数据
   * @param {Function} onChunk - 流式数据回调函数
   * @returns {Promise<void>}
   */
  async sendStreamingMessage(messageData, onChunk) {
    try {
      const { message, history = [], context = null } = messageData;
      
      // 构建流式请求
      const requestPayload = this.buildChatRequest(message, history, context);
      requestPayload.stream = true;
      
      // 发送流式请求
      await this.callGeminiStreamApi('streamGenerateContent', requestPayload, onChunk);
    } catch (error) {
      console.error('[AI接口管理] 流式消息发送失败:', error);
      onChunk({ error: error.message });
    }
  }

  /**
   * @function analyzeContent - 分析内容
   * @description 使用AI对页面内容进行分析
   * @param {Object} contentData - 内容数据
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} 分析结果
   */
  async analyzeContent(contentData, tabId) {
    try {
      const { textContent, pageInfo } = contentData;
      
      // 构建分析请求
      const analysisPrompt = this.buildAnalysisPrompt(textContent, pageInfo);
      const requestPayload = this.buildChatRequest(analysisPrompt);
      
      // 发送分析请求
      const response = await this.callGeminiApi('generateContent', requestPayload);
      const analysisText = response.candidates[0]?.content?.parts[0]?.text;
      
      // 解析分析结果
      const analysis = this.parseAnalysisResponse(analysisText);
      
      return {
        success: true,
        analysis: {
          title: pageInfo.title,
          summary: analysis.summary,
          keyPoints: analysis.keyPoints,
          contentType: analysis.contentType,
          wordCount: this.countWords(textContent.mainContent),
          timestamp: Date.now()
        }
      };
    } catch (error) {
      console.error('[AI接口管理] 内容分析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function generateSmartReply - 生成智能回复建议
   * @description 基于页面内容生成回复建议
   * @param {Object} contextData - 上下文数据
   * @returns {Promise<Object>} 回复建议结果
   */
  async generateSmartReply(contextData) {
    try {
      const { pageContent, userInput = '' } = contextData;
      
      const replyPrompt = this.buildReplyPrompt(pageContent, userInput);
      const requestPayload = this.buildChatRequest(replyPrompt);
      
      const response = await this.callGeminiApi('generateContent', requestPayload);
      const replyText = response.candidates[0]?.content?.parts[0]?.text;
      
      const suggestions = this.parseReplySuggestions(replyText);
      
      return {
        success: true,
        suggestions: suggestions,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[AI接口管理] 智能回复生成失败:', error);
      return {
        success: false,
        error: error.message,
        suggestions: []
      };
    }
  }

  /**
   * @function buildChatRequest - 构建聊天请求
   * @description 构建标准的Gemini API聊天请求格式
   * @param {string} message - 用户消息
   * @param {Array} history - 对话历史
   * @param {Object} context - 额外上下文
   * @returns {Object} 请求载荷
   */
  buildChatRequest(message, history = [], context = null) {
    const contents = [];
    
    // 添加历史对话
    history.forEach(item => {
      contents.push({
        role: item.type === 'user' ? 'user' : 'model',
        parts: [{ text: item.text }]
      });
    });
    
    // 添加当前消息
    contents.push({
      role: 'user',
      parts: [{ text: message }]
    });
    
    // 如果有上下文，添加到系统消息中
    if (context) {
      contents.unshift({
        role: 'user',
        parts: [{ text: `页面上下文信息：\n${JSON.stringify(context, null, 2)}` }]
      });
    }
    
    return {
      contents: contents,
      generationConfig: {
        temperature: this.GEMINI_CONFIG.TEMPERATURE,
        maxOutputTokens: this.GEMINI_CONFIG.MAX_TOKENS
      },
      safetySettings: this.GEMINI_CONFIG.SAFETY_SETTINGS
    };
  }

  /**
   * @function buildAnalysisPrompt - 构建分析提示词
   * @description 为内容分析构建专门的提示词
   * @param {Object} textContent - 文本内容
   * @param {Object} pageInfo - 页面信息
   * @returns {string} 分析提示词
   */
  buildAnalysisPrompt(textContent, pageInfo) {
    return `请分析以下网页内容，并以JSON格式返回分析结果：

页面标题：${pageInfo.title || '未知'}
页面内容：
${textContent.mainContent || '无内容'}

请提供以下分析：
1. summary: 内容摘要（100-200字）
2. keyPoints: 关键信息点（数组，3-5个要点）
3. contentType: 内容类型（如：新闻、文档、博客、产品页面等）

请严格按照JSON格式返回，不要包含其他说明文字：
{
  "summary": "内容摘要",
  "keyPoints": ["关键点1", "关键点2", "关键点3"],
  "contentType": "内容类型"
}`;
  }

  /**
   * @function buildReplyPrompt - 构建回复提示词
   * @description 为智能回复生成构建提示词
   * @param {Object} pageContent - 页面内容
   * @param {string} userInput - 用户输入
   * @returns {string} 回复提示词
   */
  buildReplyPrompt(pageContent, userInput) {
    return `基于以下页面内容，生成3个不同风格的回复建议：

页面内容摘要：${pageContent.summary || ''}
用户输入：${userInput || '无特定输入'}

请生成以下三种风格的回复：
1. 专业正式的回复
2. 友好亲切的回复  
3. 解决问题导向的回复

请以JSON数组格式返回：
["专业回复", "友好回复", "解决方案回复"]`;
  }

  /**
   * @function callGeminiApi - 调用Gemini API
   * @description 发送请求到Gemini API并处理响应
   * @param {string} endpoint - API端点
   * @param {Object} payload - 请求载荷
   * @returns {Promise<Object>} API响应
   */
  async callGeminiApi(endpoint, payload) {
    const apiKey = this.GEMINI_CONFIG.API_KEY;
    
    if (!apiKey || apiKey === 'YOUR_GEMINI_API_KEY_HERE') {
      throw new Error('Gemini API密钥未配置，请在config.js文件中设置实际的API密钥');
    }
    
    const url = `${this.GEMINI_CONFIG.BASE_URL}/models/${this.GEMINI_CONFIG.MODEL}:${endpoint}?key=${apiKey}`;
    
    const response = await this.makeHttpRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API请求失败: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * @function callGeminiStreamApi - 调用Gemini流式API
   * @description 发送流式请求到Gemini API
   * @param {string} endpoint - API端点
   * @param {Object} payload - 请求载荷
   * @param {Function} onChunk - 数据块回调函数
   * @returns {Promise<void>}
   */
  async callGeminiStreamApi(endpoint, payload, onChunk) {
    const apiKey = this.GEMINI_CONFIG.API_KEY;
    
    if (!apiKey || apiKey === 'YOUR_GEMINI_API_KEY_HERE') {
      throw new Error('Gemini API密钥未配置，请在config.js文件中设置实际的API密钥');
    }
    
    const url = `${this.GEMINI_CONFIG.BASE_URL}/models/${this.GEMINI_CONFIG.MODEL}:${endpoint}?key=${apiKey}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`流式API请求失败: ${response.status}`);
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              const text = data.candidates?.[0]?.content?.parts?.[0]?.text;
              if (text) {
                onChunk({ text });
              }
            } catch (e) {
              console.warn('[AI接口管理] 流式数据解析失败:', e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * @function makeHttpRequest - 发送HTTP请求
   * @description 带重试机制的HTTP请求发送器
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Response>} HTTP响应
   */
  async makeHttpRequest(url, options) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.REQUEST_CONFIG.MAX_RETRIES; attempt++) {
      try {
        const response = await Promise.race([
          fetch(url, options),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('请求超时')), this.REQUEST_CONFIG.TIMEOUT)
          )
        ]);
        
        return response;
      } catch (error) {
        lastError = error;
        console.warn(`[AI接口管理] 请求失败，尝试 ${attempt}/${this.REQUEST_CONFIG.MAX_RETRIES}:`, error.message);
        
        if (attempt < this.REQUEST_CONFIG.MAX_RETRIES) {
          await this.delay(this.REQUEST_CONFIG.RETRY_DELAY * attempt);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * @function parseAnalysisResponse - 解析分析响应
   * @description 解析AI返回的分析结果
   * @param {string} responseText - AI响应文本
   * @returns {Object} 解析后的分析结果
   */
  parseAnalysisResponse(responseText) {
    try {
      // 尝试提取JSON部分
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // 如果无法解析JSON，返回默认结构
      return {
        summary: responseText.substring(0, 200) + '...',
        keyPoints: ['分析结果解析失败'],
        contentType: '未知'
      };
    } catch (error) {
      console.error('[AI接口管理] 分析结果解析失败:', error);
      return {
        summary: '内容分析完成，但结果格式异常',
        keyPoints: ['请重新尝试分析'],
        contentType: '未知'
      };
    }
  }

  /**
   * @function parseReplySuggestions - 解析回复建议
   * @description 解析AI返回的回复建议
   * @param {string} responseText - AI响应文本
   * @returns {Array} 回复建议数组
   */
  parseReplySuggestions(responseText) {
    try {
      // 尝试提取JSON数组
      const jsonMatch = responseText.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0]);
        return Array.isArray(suggestions) ? suggestions : [];
      }
      
      // 如果无法解析JSON，尝试按行分割
      const lines = responseText.split('\n').filter(line => line.trim());
      return lines.slice(0, 3); // 最多返回3个建议
    } catch (error) {
      console.error('[AI接口管理] 回复建议解析失败:', error);
      return ['感谢您的留言，我会尽快回复。'];
    }
  }

  /**
   * @function countWords - 统计字数
   * @description 统计文本内容的字数
   * @param {string} text - 文本内容
   * @returns {number} 字数统计
   */
  countWords(text) {
    if (!text || typeof text !== 'string') return 0;
    
    // 中文字符统计
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    // 英文单词统计
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    
    return chineseChars + englishWords;
  }

  /**
   * @function startQueueProcessor - 启动队列处理器
   * @description 启动请求队列处理，避免API调用过于频繁
   */
  startQueueProcessor() {
    if (this.isProcessingQueue) return;
    
    this.isProcessingQueue = true;
    
    const processQueue = async () => {
      while (this.requestQueue.length > 0) {
        const request = this.requestQueue.shift();
        try {
          await request();
        } catch (error) {
          console.error('[AI接口管理] 队列请求处理失败:', error);
        }
        
        // 避免请求过于频繁
        await this.delay(100);
      }
      
      this.isProcessingQueue = false;
    };
    
    processQueue();
  }

  /**
   * @function delay - 延迟函数
   * @description 异步延迟指定毫秒数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise<void>}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * @function cleanup - 清理API管理器
   * @description 清理资源和缓存
   */
  cleanup() {
    this.responseCache.clear();
    this.requestQueue.length = 0;
    this.isProcessingQueue = false;
    this.isInitialized = false;
    console.log('[AI接口管理] API管理器已清理');
  }
} 