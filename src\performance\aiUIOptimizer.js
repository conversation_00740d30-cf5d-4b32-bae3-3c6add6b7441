/**
 * @file AI侧边栏UI性能优化器
 * @description 优化UI渲染性能，包括虚拟滚动、防抖、DOM优化等
 */

/**
 * @class AiUIOptimizer
 * @description UI性能优化器，负责优化界面渲染和交互性能
 */
class AiUIOptimizer {
  /**
   * @function constructor - 构造函数
   * @description 初始化UI优化器
   */
  constructor() {
    // 优化配置
    this.config = {
      // 虚拟滚动配置
      virtualScroll: {
        itemHeight: 60, // 默认项目高度
        bufferSize: 5, // 缓冲区大小
        threshold: 100 // 启用虚拟滚动的最小项目数
      },
      
      // 防抖配置
      debounce: {
        search: 300, // 搜索防抖延迟
        input: 150, // 输入防抖延迟
        resize: 100, // 窗口大小调整防抖延迟
        scroll: 16 // 滚动防抖延迟（约60fps）
      },
      
      // DOM优化配置
      dom: {
        batchSize: 50, // DOM批处理大小
        fragmentThreshold: 10, // 使用DocumentFragment的阈值
        recycleThreshold: 100 // DOM元素回收阈值
      },
      
      // 动画配置
      animation: {
        duration: 200, // 默认动画时长
        easing: 'ease-out', // 默认缓动函数
        fps: 60 // 目标帧率
      }
    };
    
    // 内部状态
    this.virtualScrollers = new Map();
    this.debounceTimers = new Map();
    this.domElementPool = new Map();
    this.animationFrames = new Set();
    this.observedElements = new Set();
    
    // 性能监控
    this.performanceMetrics = {
      renderTime: [],
      scrollPerformance: [],
      animationFrames: 0,
      droppedFrames: 0
    };
    
    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化UI优化器
   * @description 初始化优化器并设置全局优化
   */
  init() {
    console.log('[UI优化] 初始化UI性能优化器...');
    
    // 设置全局CSS优化
    this.setupGlobalOptimizations();
    
    // 初始化Intersection Observer
    this.setupIntersectionObserver();
    
    // 设置性能监控
    this.setupPerformanceMonitoring();
    
    console.log('[UI优化] UI性能优化器初始化完成');
  }

  /**
   * @function setupGlobalOptimizations - 设置全局优化
   * @description 设置全局CSS和DOM优化
   */
  setupGlobalOptimizations() {
    // 添加性能优化CSS
    const optimizationStyles = document.createElement('style');
    optimizationStyles.id = 'ai-ui-optimizations';
    optimizationStyles.textContent = `
      /* GPU加速 */
      .ai-gpu-accelerated {
        transform: translateZ(0);
        will-change: transform;
      }
      
      /* 平滑滚动 */
      .ai-smooth-scroll {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
      }
      
      /* 优化动画 */
      .ai-optimized-animation {
        will-change: transform, opacity;
        backface-visibility: hidden;
        perspective: 1000px;
      }
      
      /* 虚拟滚动容器 */
      .ai-virtual-scroll-container {
        overflow: auto;
        position: relative;
      }
      
      .ai-virtual-scroll-content {
        position: relative;
      }
      
      .ai-virtual-scroll-item {
        position: absolute;
        left: 0;
        right: 0;
      }
      
      /* 加载状态 */
      .ai-loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: ai-skeleton-loading 1.5s infinite;
      }
      
      @keyframes ai-skeleton-loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }
    `;
    
    if (!document.getElementById('ai-ui-optimizations')) {
      document.head.appendChild(optimizationStyles);
    }
  }

  /**
   * @function setupIntersectionObserver - 设置交叉观察器
   * @description 设置用于懒加载和可见性检测的观察器
   */
  setupIntersectionObserver() {
    // 创建交叉观察器
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const element = entry.target;
        
        if (entry.isIntersecting) {
          // 元素进入视口
          element.classList.add('ai-visible');
          this.handleElementVisible(element);
        } else {
          // 元素离开视口
          element.classList.remove('ai-visible');
          this.handleElementHidden(element);
        }
      });
    }, {
      rootMargin: '50px',
      threshold: [0, 0.1, 0.5, 1]
    });
  }

  /**
   * @function setupPerformanceMonitoring - 设置性能监控
   * @description 设置FPS和渲染性能监控
   */
  setupPerformanceMonitoring() {
    let lastTime = performance.now();
    let frameCount = 0;
    
    const monitorFrame = (currentTime) => {
      frameCount++;
      const deltaTime = currentTime - lastTime;
      
      if (deltaTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / deltaTime);
        this.performanceMetrics.animationFrames = fps;
        
        if (fps < 50) {
          this.performanceMetrics.droppedFrames++;
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(monitorFrame);
    };
    
    requestAnimationFrame(monitorFrame);
  }

  /**
   * @function createVirtualScroller - 创建虚拟滚动器
   * @description 为长列表创建虚拟滚动器
   * @param {HTMLElement} container - 容器元素
   * @param {Array} items - 数据项数组
   * @param {Function} renderItem - 渲染项目的函数
   * @param {Object} options - 配置选项
   * @returns {Object} 虚拟滚动器实例
   */
  createVirtualScroller(container, items, renderItem, options = {}) {
    const config = {
      itemHeight: options.itemHeight || this.config.virtualScroll.itemHeight,
      bufferSize: options.bufferSize || this.config.virtualScroll.bufferSize,
      ...options
    };
    
    // 检查是否需要虚拟滚动
    if (items.length < this.config.virtualScroll.threshold) {
      return this.createSimpleList(container, items, renderItem);
    }
    
    const scroller = {
      container,
      items,
      renderItem,
      config,
      visibleStart: 0,
      visibleEnd: 0,
      renderedElements: new Map(),
      scrollTop: 0
    };
    
    // 设置容器
    this.setupVirtualScrollContainer(scroller);
    
    // 初始渲染
    this.updateVirtualScroll(scroller);
    
    // 绑定滚动事件
    this.bindVirtualScrollEvents(scroller);
    
    // 缓存滚动器
    const scrollerId = `scroller-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.virtualScrollers.set(scrollerId, scroller);
    
    console.log(`[UI优化] 🚀 虚拟滚动器已创建: ${items.length} 项目`);
    
    return {
      id: scrollerId,
      update: (newItems) => this.updateVirtualScrollItems(scrollerId, newItems),
      scrollTo: (index) => this.scrollVirtualScrollTo(scrollerId, index),
      destroy: () => this.destroyVirtualScroller(scrollerId)
    };
  }

  /**
   * @function setupVirtualScrollContainer - 设置虚拟滚动容器
   * @description 设置虚拟滚动的容器结构
   * @param {Object} scroller - 滚动器对象
   */
  setupVirtualScrollContainer(scroller) {
    const { container, items, config } = scroller;
    
    // 添加CSS类
    container.classList.add('ai-virtual-scroll-container');
    
    // 创建内容容器
    const content = document.createElement('div');
    content.className = 'ai-virtual-scroll-content';
    content.style.height = `${items.length * config.itemHeight}px`;
    
    // 清空容器并添加内容
    container.innerHTML = '';
    container.appendChild(content);
    
    scroller.content = content;
  }

  /**
   * @function updateVirtualScroll - 更新虚拟滚动
   * @description 更新虚拟滚动的可见项目
   * @param {Object} scroller - 滚动器对象
   */
  updateVirtualScroll(scroller) {
    const { container, items, config, content } = scroller;
    const scrollTop = container.scrollTop;
    const containerHeight = container.clientHeight;
    
    // 计算可见范围
    const startIndex = Math.floor(scrollTop / config.itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / config.itemHeight) + config.bufferSize,
      items.length
    );
    
    const visibleStart = Math.max(0, startIndex - config.bufferSize);
    const visibleEnd = endIndex;
    
    // 移除不再可见的元素
    for (const [index, element] of scroller.renderedElements.entries()) {
      if (index < visibleStart || index >= visibleEnd) {
        this.recycleElement(element);
        scroller.renderedElements.delete(index);
      }
    }
    
    // 渲染新的可见元素
    for (let i = visibleStart; i < visibleEnd; i++) {
      if (!scroller.renderedElements.has(i) && items[i]) {
        const element = this.renderVirtualScrollItem(scroller, i);
        scroller.renderedElements.set(i, element);
        content.appendChild(element);
      }
    }
    
    scroller.visibleStart = visibleStart;
    scroller.visibleEnd = visibleEnd;
    scroller.scrollTop = scrollTop;
  }

  /**
   * @function renderVirtualScrollItem - 渲染虚拟滚动项目
   * @description 渲染单个虚拟滚动项目
   * @param {Object} scroller - 滚动器对象
   * @param {number} index - 项目索引
   * @returns {HTMLElement} 渲染的元素
   */
  renderVirtualScrollItem(scroller, index) {
    const { items, renderItem, config } = scroller;
    const item = items[index];
    
    // 获取或创建元素
    const element = this.getPooledElement('virtual-scroll-item') || document.createElement('div');
    element.className = 'ai-virtual-scroll-item';
    element.style.top = `${index * config.itemHeight}px`;
    element.style.height = `${config.itemHeight}px`;
    
    // 渲染内容
    const content = renderItem(item, index);
    if (typeof content === 'string') {
      element.innerHTML = content;
    } else if (content instanceof HTMLElement) {
      element.innerHTML = '';
      element.appendChild(content);
    }
    
    return element;
  }

  /**
   * @function bindVirtualScrollEvents - 绑定虚拟滚动事件
   * @description 绑定虚拟滚动的事件处理
   * @param {Object} scroller - 滚动器对象
   */
  bindVirtualScrollEvents(scroller) {
    const { container } = scroller;
    
    // 防抖滚动处理
    const debouncedScroll = this.debounce(() => {
      this.updateVirtualScroll(scroller);
    }, this.config.debounce.scroll);
    
    container.addEventListener('scroll', debouncedScroll, { passive: true });
  }

  /**
   * @function createSimpleList - 创建简单列表
   * @description 为小数据集创建简单列表（不使用虚拟滚动）
   * @param {HTMLElement} container - 容器元素
   * @param {Array} items - 数据项数组
   * @param {Function} renderItem - 渲染项目的函数
   * @returns {Object} 简单列表实例
   */
  createSimpleList(container, items, renderItem) {
    // 使用DocumentFragment优化DOM操作
    const fragment = document.createDocumentFragment();
    
    items.forEach((item, index) => {
      const element = document.createElement('div');
      element.className = 'ai-list-item';
      
      const content = renderItem(item, index);
      if (typeof content === 'string') {
        element.innerHTML = content;
      } else if (content instanceof HTMLElement) {
        element.appendChild(content);
      }
      
      fragment.appendChild(element);
    });
    
    container.innerHTML = '';
    container.appendChild(fragment);
    
    console.log(`[UI优化] 📝 简单列表已创建: ${items.length} 项目`);
    
    return {
      update: (newItems) => this.createSimpleList(container, newItems, renderItem),
      destroy: () => { container.innerHTML = ''; }
    };
  }

  /**
   * @function debounce - 防抖函数
   * @description 创建防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @param {string} key - 防抖键（可选）
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay, key = null) {
    return (...args) => {
      const timerKey = key || func.toString();
      
      if (this.debounceTimers.has(timerKey)) {
        clearTimeout(this.debounceTimers.get(timerKey));
      }
      
      const timer = setTimeout(() => {
        func.apply(this, args);
        this.debounceTimers.delete(timerKey);
      }, delay);
      
      this.debounceTimers.set(timerKey, timer);
    };
  }

  /**
   * @function throttle - 节流函数
   * @description 创建节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 节流后的函数
   */
  throttle(func, delay) {
    let lastCall = 0;
    return (...args) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }

  /**
   * @function batchDOMUpdates - 批量DOM更新
   * @description 批量执行DOM更新操作
   * @param {Array} operations - DOM操作数组
   */
  batchDOMUpdates(operations) {
    const startTime = performance.now();
    
    // 使用DocumentFragment进行批量操作
    const fragment = document.createDocumentFragment();
    const elementsToAppend = [];
    
    operations.forEach(operation => {
      try {
        switch (operation.type) {
          case 'create':
            const element = document.createElement(operation.tag || 'div');
            if (operation.className) element.className = operation.className;
            if (operation.innerHTML) element.innerHTML = operation.innerHTML;
            if (operation.parent) {
              elementsToAppend.push({ element, parent: operation.parent });
            } else {
              fragment.appendChild(element);
            }
            break;
            
          case 'update':
            if (operation.element) {
              if (operation.innerHTML !== undefined) {
                operation.element.innerHTML = operation.innerHTML;
              }
              if (operation.className !== undefined) {
                operation.element.className = operation.className;
              }
            }
            break;
            
          case 'remove':
            if (operation.element && operation.element.parentNode) {
              this.recycleElement(operation.element);
            }
            break;
        }
      } catch (error) {
        console.warn('[UI优化] DOM操作失败:', error);
      }
    });
    
    // 批量添加元素
    elementsToAppend.forEach(({ element, parent }) => {
      parent.appendChild(element);
    });
    
    const endTime = performance.now();
    this.performanceMetrics.renderTime.push(endTime - startTime);
    
    console.log(`[UI优化] 🔄 批量DOM更新完成: ${operations.length} 个操作, ${(endTime - startTime).toFixed(2)}ms`);
  }

  /**
   * @function getPooledElement - 获取池化元素
   * @description 从元素池中获取可复用的元素
   * @param {string} type - 元素类型
   * @returns {HTMLElement|null} 池化元素或null
   */
  getPooledElement(type) {
    const pool = this.domElementPool.get(type);
    if (pool && pool.length > 0) {
      return pool.pop();
    }
    return null;
  }

  /**
   * @function recycleElement - 回收元素
   * @description 将元素回收到元素池中
   * @param {HTMLElement} element - 要回收的元素
   */
  recycleElement(element) {
    if (!element || !element.parentNode) return;
    
    // 移除元素
    element.parentNode.removeChild(element);
    
    // 清理元素
    element.innerHTML = '';
    element.className = '';
    element.removeAttribute('style');
    
    // 添加到池中
    const type = element.dataset.poolType || 'generic';
    if (!this.domElementPool.has(type)) {
      this.domElementPool.set(type, []);
    }
    
    const pool = this.domElementPool.get(type);
    if (pool.length < this.config.dom.recycleThreshold) {
      pool.push(element);
    }
  }

  /**
   * @function optimizeAnimation - 优化动画
   * @description 优化CSS动画和过渡
   * @param {HTMLElement} element - 要动画的元素
   * @param {Object} options - 动画选项
   * @returns {Promise<void>} 动画完成Promise
   */
  optimizeAnimation(element, options = {}) {
    const {
      duration = this.config.animation.duration,
      easing = this.config.animation.easing,
      transform = null,
      opacity = null,
      useGPU = true
    } = options;
    
    return new Promise((resolve) => {
      // 启用GPU加速
      if (useGPU) {
        element.classList.add('ai-gpu-accelerated');
      }
      
      // 设置will-change属性
      const willChangeProps = [];
      if (transform) willChangeProps.push('transform');
      if (opacity !== null) willChangeProps.push('opacity');
      element.style.willChange = willChangeProps.join(', ');
      
      // 应用动画
      element.style.transition = `all ${duration}ms ${easing}`;
      
      if (transform) {
        element.style.transform = transform;
      }
      if (opacity !== null) {
        element.style.opacity = opacity;
      }
      
      // 动画结束后清理
      const cleanup = () => {
        element.style.transition = '';
        element.style.willChange = '';
        element.classList.remove('ai-gpu-accelerated');
        element.removeEventListener('transitionend', cleanup);
        resolve();
      };
      
      element.addEventListener('transitionend', cleanup, { once: true });
      
      // 备用超时
      setTimeout(cleanup, duration + 100);
    });
  }

  /**
   * @function observeElement - 观察元素
   * @description 使用Intersection Observer观察元素
   * @param {HTMLElement} element - 要观察的元素
   */
  observeElement(element) {
    if (this.intersectionObserver && !this.observedElements.has(element)) {
      this.intersectionObserver.observe(element);
      this.observedElements.add(element);
    }
  }

  /**
   * @function unobserveElement - 停止观察元素
   * @description 停止观察指定元素
   * @param {HTMLElement} element - 要停止观察的元素
   */
  unobserveElement(element) {
    if (this.intersectionObserver && this.observedElements.has(element)) {
      this.intersectionObserver.unobserve(element);
      this.observedElements.delete(element);
    }
  }

  /**
   * @function handleElementVisible - 处理元素可见
   * @description 处理元素进入视口的逻辑
   * @param {HTMLElement} element - 可见的元素
   */
  handleElementVisible(element) {
    // 懒加载图片
    const lazyImages = element.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => {
      img.src = img.dataset.src;
      img.removeAttribute('data-src');
    });
    
    // 启动动画
    if (element.dataset.animation) {
      element.classList.add('ai-animate-in');
    }
  }

  /**
   * @function handleElementHidden - 处理元素隐藏
   * @description 处理元素离开视口的逻辑
   * @param {HTMLElement} element - 隐藏的元素
   */
  handleElementHidden(element) {
    // 暂停视频
    const videos = element.querySelectorAll('video');
    videos.forEach(video => {
      if (!video.paused) {
        video.pause();
      }
    });
  }

  /**
   * @function getPerformanceMetrics - 获取性能指标
   * @description 获取UI性能指标
   * @returns {Object} 性能指标
   */
  getPerformanceMetrics() {
    const renderTimes = this.performanceMetrics.renderTime;
    const avgRenderTime = renderTimes.length > 0 ? 
      renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length : 0;
    
    return {
      averageRenderTime: avgRenderTime.toFixed(2) + 'ms',
      currentFPS: this.performanceMetrics.animationFrames,
      droppedFrames: this.performanceMetrics.droppedFrames,
      virtualScrollers: this.virtualScrollers.size,
      observedElements: this.observedElements.size,
      pooledElements: Array.from(this.domElementPool.values())
        .reduce((total, pool) => total + pool.length, 0)
    };
  }

  /**
   * @function cleanupExpiredCache - 清理过期缓存
   * @description 清理UI相关的过期缓存数据
   */
  cleanupExpiredCache() {
    try {
      let cleanedItems = 0;

      // 清理虚拟滚动缓存
      for (const [scrollerId, scroller] of this.virtualScrollers.entries()) {
        if (scroller.lastUsed && Date.now() - scroller.lastUsed > 30 * 60 * 1000) {
          // 30分钟未使用的虚拟滚动器
          if (scroller.content) {
            scroller.content.innerHTML = '';
          }
          this.virtualScrollers.delete(scrollerId);
          cleanedItems++;
        }
      }

      // 清理防抖定时器缓存
      const now = Date.now();
      for (const [key, timer] of this.debounceTimers.entries()) {
        if (timer.created && now - timer.created > 5 * 60 * 1000) {
          // 5分钟前创建的定时器
          clearTimeout(timer);
          this.debounceTimers.delete(key);
          cleanedItems++;
        }
      }

      // 清理元素池中的过期元素
      for (const [type, pool] of this.domElementPool.entries()) {
        if (pool.length > 20) {
          // 保留最近的20个元素
          const excessElements = pool.splice(20);
          cleanedItems += excessElements.length;
        }
      }

      // 清理性能指标历史
      if (this.performanceMetrics.renderTime.length > 50) {
        this.performanceMetrics.renderTime = this.performanceMetrics.renderTime.slice(-25);
        cleanedItems++;
      }

      if (cleanedItems > 0) {
        console.log(`[UI优化] 🧹 清理了 ${cleanedItems} 个过期缓存项`);
      }

      return cleanedItems;
    } catch (error) {
      console.error('[UI优化] 缓存清理失败:', error);
      return 0;
    }
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理UI优化器使用的资源
   */
  cleanup() {
    // 清理定时器
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();

    // 清理观察器
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    this.observedElements.clear();

    // 清理虚拟滚动器
    for (const scroller of this.virtualScrollers.values()) {
      if (scroller.content) {
        scroller.content.innerHTML = '';
      }
    }
    this.virtualScrollers.clear();

    // 清理元素池
    this.domElementPool.clear();

    // 清理动画帧
    for (const frameId of this.animationFrames) {
      cancelAnimationFrame(frameId);
    }
    this.animationFrames.clear();

    console.log('[UI优化] UI性能优化器已清理');
  }
}

// 导出UI优化器类
export { AiUIOptimizer };
