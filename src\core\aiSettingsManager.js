/**
 * @file AI设置管理器
 * @description 负责用户设置的管理、API配置、功能开关控制等功能
 */

/**
 * @class AiSettingsManager - AI设置管理器
 * @description 提供完整的用户设置管理功能
 */
export class AiSettingsManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化设置管理器
   * @param {AiSecurityManager} securityManager - 安全管理器实例
   */
  constructor(securityManager) {
    this.securityManager = securityManager;
    this.isInitialized = false;
    
    // 默认设置配置
    this.defaultSettings = {
      // API配置（API密钥已硬编码）
      api: {
        notionClientId: '',
        customApiEndpoint: '',
        requestTimeout: 30000,
        maxRetries: 3,
        enableStreamResponse: true
      },
      
      // 功能开关
      features: {
        enableNotionSync: false,
        enableCursorEnhancement: true,
        enableAutoAnalysis: true,
        enableSmartTemplates: true,
        enablePageCapture: true,
        enableChatHistory: true,
        enableOfflineMode: false
      },
      
      // 界面设置
      ui: {
        theme: 'light', // light, dark, auto
        language: 'zh-CN',
        sidebarPosition: 'right', // left, right
        sidebarWidth: 400,
        fontSize: 'medium', // small, medium, large
        showWelcomeMessage: true,
        compactMode: false,
        enableAnimations: true
      },
      
      // 聊天设置
      chat: {
        maxHistoryLength: 100,
        autoSaveInterval: 60000, // 1分钟
        enableContextMemory: true,
        defaultModel: 'gemini-2.5-flash-lite-preview-06-17',
        temperature: 0.7,
        maxTokens: 8192,
        enableTypingIndicator: true
      },
      
      // 分析设置
      analysis: {
        autoAnalyzePages: true,
        analysisDepth: 'standard', // basic, standard, detailed
        enableSentimentAnalysis: true,
        enableKeywordExtraction: true,
        enableSummaryGeneration: true,
        maxContentLength: 50000
      },
      
      // 模板设置
      templates: {
        enableSmartSuggestions: true,
        autoLoadTemplates: true,
        customTemplatesPath: '',
        templateSyncInterval: 300000, // 5分钟
        maxTemplateHistory: 50
      },
      
      // 同步设置
      sync: {
        enableAutoSync: false,
        syncInterval: 300000, // 5分钟
        syncOnClose: false,
        cloudProvider: 'notion', // notion, google, custom
        enableConflictResolution: true
      },
      
      // 隐私和安全
      privacy: {
        enableDataEncryption: true,
        autoDeleteHistory: false,
        historyRetentionDays: 30,
        enableAnalytics: false,
        shareUsageData: false,
        enableSecureMode: true
      },
      
      // 性能设置
      performance: {
        enableCache: true,
        cacheSize: 100, // MB
        enableLazyLoading: true,
        enableCompression: true,
        maxConcurrentRequests: 3,
        enablePreloading: false
      },
      
      // 快捷键设置
      shortcuts: {
        toggleSidebar: 'Alt+S',
        quickAnalyze: 'Alt+A',
        newChat: 'Alt+N',
        clearHistory: 'Alt+C',
        openSettings: 'Alt+,',
        enableGlobalShortcuts: true
      },
      
      // 通知设置
      notifications: {
        enableNotifications: true,
        notifyOnAnalysisComplete: true,
        notifyOnSyncComplete: false,
        notifyOnErrors: true,
        soundEnabled: false,
        position: 'top-right' // top-left, top-right, bottom-left, bottom-right
      }
    };
    
    // 当前设置
    this.currentSettings = {};
    
    // 设置监听器
    this.listeners = new Map();
    
    // 设置变更历史
    this.changeHistory = [];
    
    // 验证规则
    this.validationRules = this.initValidationRules();
  }

  /**
   * @function init - 初始化设置管理器
   * @description 加载设置并初始化管理器
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[设置管理] 初始化设置管理器...');
      
      if (!this.securityManager || !this.securityManager.isInitialized) {
        throw new Error('安全管理器未正确初始化');
      }
      
      // 加载保存的设置
      await this.loadSettings();
      
      // 验证设置完整性
      this.validateSettings();
      
      // 应用设置
      await this.applySettings();
      
      this.isInitialized = true;
      console.log('[设置管理] 设置管理器初始化完成');
    } catch (error) {
      console.error('[设置管理] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function loadSettings - 加载设置
   * @description 从存储中加载用户设置
   * @returns {Promise<void>}
   */
  async loadSettings() {
    try {
      // 从安全存储加载设置
      const savedSettings = await this.securityManager.getSecureData('user_settings');
      
      if (savedSettings) {
        // 合并默认设置和保存的设置
        this.currentSettings = this.mergeSettings(this.defaultSettings, savedSettings);
      } else {
        // 使用默认设置
        this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
      }
      
      console.log('[设置管理] 设置加载完成');
    } catch (error) {
      console.error('[设置管理] 加载设置失败:', error);
      // 使用默认设置作为后备
      this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
    }
  }

  /**
   * @function saveSettings - 保存设置
   * @description 将当前设置保存到存储
   * @returns {Promise<void>}
   */
  async saveSettings() {
    try {
      await this.securityManager.storeSecureData('user_settings', this.currentSettings);
      console.log('[设置管理] 设置保存成功');
    } catch (error) {
      console.error('[设置管理] 保存设置失败:', error);
      throw error;
    }
  }

  /**
   * @function getSetting - 获取设置值
   * @description 获取指定路径的设置值
   * @param {string} path - 设置路径（如 'ui.theme'）
   * @param {any} defaultValue - 默认值
   * @returns {any} 设置值
   */
  getSetting(path, defaultValue = null) {
    try {
      const keys = path.split('.');
      let value = this.currentSettings;
      
      for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return defaultValue;
        }
      }
      
      return value;
    } catch (error) {
      console.error('[设置管理] 获取设置失败:', error);
      return defaultValue;
    }
  }

  /**
   * @function setSetting - 设置值
   * @description 设置指定路径的值
   * @param {string} path - 设置路径
   * @param {any} value - 设置值
   * @param {boolean} autoSave - 是否自动保存
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setSetting(path, value, autoSave = true) {
    try {
      // 验证设置值
      if (!this.validateSettingValue(path, value)) {
        throw new Error(`设置值验证失败: ${path} = ${value}`);
      }
      
      // 记录变更历史
      const oldValue = this.getSetting(path);
      this.recordChange(path, oldValue, value);
      
      // 设置值
      const keys = path.split('.');
      let target = this.currentSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!target[key] || typeof target[key] !== 'object') {
          target[key] = {};
        }
        target = target[key];
      }
      
      target[keys[keys.length - 1]] = value;
      
      // 触发监听器
      this.notifyListeners(path, value, oldValue);
      
      // 自动保存
      if (autoSave) {
        await this.saveSettings();
      }
      
      console.log(`[设置管理] 设置已更新: ${path} = ${value}`);
      return true;
    } catch (error) {
      console.error('[设置管理] 设置值失败:', error);
      return false;
    }
  }

  /**
   * @function updateSettings - 批量更新设置
   * @description 批量更新多个设置项
   * @param {Object} updates - 更新对象
   * @param {boolean} autoSave - 是否自动保存
   * @returns {Promise<boolean>} 更新是否成功
   */
  async updateSettings(updates, autoSave = true) {
    try {
      const updatePromises = Object.entries(updates).map(([path, value]) =>
        this.setSetting(path, value, false)
      );
      
      const results = await Promise.all(updatePromises);
      const allSuccess = results.every(result => result === true);
      
      if (allSuccess && autoSave) {
        await this.saveSettings();
      }
      
      return allSuccess;
    } catch (error) {
      console.error('[设置管理] 批量更新设置失败:', error);
      return false;
    }
  }

  /**
   * @function resetSettings - 重置设置
   * @description 重置设置到默认值
   * @param {string} section - 要重置的部分（可选）
   * @returns {Promise<void>}
   */
  async resetSettings(section = null) {
    try {
      if (section) {
        // 重置特定部分
        if (this.defaultSettings[section]) {
          this.currentSettings[section] = JSON.parse(JSON.stringify(this.defaultSettings[section]));
        }
      } else {
        // 重置所有设置
        this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
      }
      
      await this.saveSettings();
      
      // 触发重置事件
      this.notifyListeners('reset', section || 'all');
      
      console.log(`[设置管理] 设置已重置: ${section || '全部'}`);
    } catch (error) {
      console.error('[设置管理] 重置设置失败:', error);
      throw error;
    }
  }

  /**
   * @function exportSettings - 导出设置
   * @description 导出当前设置配置
   * @param {boolean} includeSecrets - 是否包含敏感信息
   * @returns {Object} 设置配置
   */
  exportSettings(includeSecrets = false) {
    try {
      const settings = JSON.parse(JSON.stringify(this.currentSettings));
      
      if (!includeSecrets) {
        // 移除敏感信息
        if (settings.api) {
          delete settings.api.geminiApiKey;
          delete settings.api.notionClientId;
        }
      }
      
      return {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        settings: settings
      };
    } catch (error) {
      console.error('[设置管理] 导出设置失败:', error);
      return null;
    }
  }

  /**
   * @function importSettings - 导入设置
   * @description 导入设置配置
   * @param {Object} settingsData - 设置数据
   * @param {boolean} merge - 是否合并现有设置
   * @returns {Promise<boolean>} 导入是否成功
   */
  async importSettings(settingsData, merge = true) {
    try {
      if (!settingsData || !settingsData.settings) {
        throw new Error('无效的设置数据');
      }
      
      const importedSettings = settingsData.settings;
      
      if (merge) {
        // 合并设置
        this.currentSettings = this.mergeSettings(this.currentSettings, importedSettings);
      } else {
        // 替换设置
        this.currentSettings = this.mergeSettings(this.defaultSettings, importedSettings);
      }
      
      // 验证设置
      this.validateSettings();
      
      // 保存设置
      await this.saveSettings();
      
      // 应用设置
      await this.applySettings();
      
      console.log('[设置管理] 设置导入成功');
      return true;
    } catch (error) {
      console.error('[设置管理] 导入设置失败:', error);
      return false;
    }
  }

  /**
   * @function addListener - 添加设置监听器
   * @description 添加设置变更监听器
   * @param {string} path - 监听路径
   * @param {Function} callback - 回调函数
   * @returns {string} 监听器ID
   */
  addListener(path, callback) {
    const listenerId = `${path}_${Date.now()}_${Math.random()}`;
    
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Map());
    }
    
    this.listeners.get(path).set(listenerId, callback);
    
    return listenerId;
  }

  /**
   * @function removeListener - 移除监听器
   * @description 移除指定的设置监听器
   * @param {string} listenerId - 监听器ID
   * @returns {boolean} 是否成功移除
   */
  removeListener(listenerId) {
    for (const [path, pathListeners] of this.listeners) {
      if (pathListeners.has(listenerId)) {
        pathListeners.delete(listenerId);
        if (pathListeners.size === 0) {
          this.listeners.delete(path);
        }
        return true;
      }
    }
    return false;
  }

  /**
   * @function notifyListeners - 通知监听器
   * @description 通知相关路径的监听器
   * @param {string} path - 变更路径
   * @param {any} newValue - 新值
   * @param {any} oldValue - 旧值
   */
  notifyListeners(path, newValue, oldValue = null) {
    try {
      // 通知精确路径监听器
      if (this.listeners.has(path)) {
        this.listeners.get(path).forEach(callback => {
          try {
            callback(newValue, oldValue, path);
          } catch (error) {
            console.error('[设置管理] 监听器回调失败:', error);
          }
        });
      }
      
      // 通知通配符监听器
      const pathParts = path.split('.');
      for (let i = 0; i < pathParts.length; i++) {
        const parentPath = pathParts.slice(0, i + 1).join('.');
        const wildcardPath = parentPath + '.*';
        
        if (this.listeners.has(wildcardPath)) {
          this.listeners.get(wildcardPath).forEach(callback => {
            try {
              callback(newValue, oldValue, path);
            } catch (error) {
              console.error('[设置管理] 通配符监听器回调失败:', error);
            }
          });
        }
      }
    } catch (error) {
      console.error('[设置管理] 通知监听器失败:', error);
    }
  }

  /**
   * @function validateSettings - 验证设置
   * @description 验证当前设置的完整性和有效性
   * @returns {boolean} 验证是否通过
   */
  validateSettings() {
    try {
      const errors = [];
      
      // 验证各个设置项
      for (const [section, rules] of Object.entries(this.validationRules)) {
        if (this.currentSettings[section]) {
          for (const [key, rule] of Object.entries(rules)) {
            const value = this.currentSettings[section][key];
            if (!this.validateValue(value, rule)) {
              errors.push(`${section}.${key}: 值 ${value} 不符合规则 ${JSON.stringify(rule)}`);
            }
          }
        }
      }
      
      if (errors.length > 0) {
        console.warn('[设置管理] 设置验证发现问题:', errors);
        // 修复无效设置
        this.fixInvalidSettings();
      }
      
      return errors.length === 0;
    } catch (error) {
      console.error('[设置管理] 验证设置失败:', error);
      return false;
    }
  }

  /**
   * @function validateSettingValue - 验证单个设置值
   * @description 验证单个设置值是否有效
   * @param {string} path - 设置路径
   * @param {any} value - 设置值
   * @returns {boolean} 是否有效
   */
  validateSettingValue(path, value) {
    try {
      const pathParts = path.split('.');
      if (pathParts.length < 2) return false;
      
      const section = pathParts[0];
      const key = pathParts[1];
      
      if (!this.validationRules[section] || !this.validationRules[section][key]) {
        return true; // 没有验证规则，默认通过
      }
      
      const rule = this.validationRules[section][key];
      return this.validateValue(value, rule);
    } catch (error) {
      console.error('[设置管理] 验证设置值失败:', error);
      return false;
    }
  }

  /**
   * @function validateValue - 验证值
   * @description 根据规则验证值
   * @param {any} value - 要验证的值
   * @param {Object} rule - 验证规则
   * @returns {boolean} 是否有效
   */
  validateValue(value, rule) {
    try {
      // 类型验证
      if (rule.type && typeof value !== rule.type) {
        return false;
      }
      
      // 枚举值验证
      if (rule.enum && !rule.enum.includes(value)) {
        return false;
      }
      
      // 范围验证（数字）
      if (rule.min !== undefined && value < rule.min) {
        return false;
      }
      if (rule.max !== undefined && value > rule.max) {
        return false;
      }
      
      // 长度验证（字符串）
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        return false;
      }
      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        return false;
      }
      
      // 正则表达式验证
      if (rule.pattern && !rule.pattern.test(value)) {
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('[设置管理] 值验证失败:', error);
      return false;
    }
  }

  /**
   * @function mergeSettings - 合并设置
   * @description 深度合并两个设置对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  mergeSettings(target, source) {
    const result = JSON.parse(JSON.stringify(target));
    
    const merge = (obj1, obj2) => {
      for (const key in obj2) {
        if (obj2.hasOwnProperty(key)) {
          if (obj2[key] && typeof obj2[key] === 'object' && !Array.isArray(obj2[key])) {
            obj1[key] = obj1[key] || {};
            merge(obj1[key], obj2[key]);
          } else {
            obj1[key] = obj2[key];
          }
        }
      }
    };
    
    merge(result, source);
    return result;
  }

  /**
   * @function recordChange - 记录变更
   * @description 记录设置变更历史
   * @param {string} path - 变更路径
   * @param {any} oldValue - 旧值
   * @param {any} newValue - 新值
   */
  recordChange(path, oldValue, newValue) {
    this.changeHistory.push({
      path: path,
      oldValue: oldValue,
      newValue: newValue,
      timestamp: Date.now()
    });
    
    // 限制历史记录数量
    if (this.changeHistory.length > 100) {
      this.changeHistory = this.changeHistory.slice(-50);
    }
  }

  /**
   * @function initValidationRules - 初始化验证规则
   * @description 初始化各个设置项的验证规则
   * @returns {Object} 验证规则对象
   */
  initValidationRules() {
    return {
      api: {
        requestTimeout: { type: 'number', min: 1000, max: 300000 },
        maxRetries: { type: 'number', min: 0, max: 10 },
        enableStreamResponse: { type: 'boolean' }
      },
      ui: {
        theme: { type: 'string', enum: ['light', 'dark', 'auto'] },
        language: { type: 'string', enum: ['zh-CN', 'en-US'] },
        sidebarPosition: { type: 'string', enum: ['left', 'right'] },
        sidebarWidth: { type: 'number', min: 300, max: 800 },
        fontSize: { type: 'string', enum: ['small', 'medium', 'large'] }
      },
      chat: {
        maxHistoryLength: { type: 'number', min: 10, max: 1000 },
        autoSaveInterval: { type: 'number', min: 10000, max: 600000 },
        temperature: { type: 'number', min: 0, max: 2 },
        maxTokens: { type: 'number', min: 100, max: 8192 }
      },
      analysis: {
        analysisDepth: { type: 'string', enum: ['basic', 'standard', 'detailed'] },
        maxContentLength: { type: 'number', min: 1000, max: 100000 }
      },
      performance: {
        cacheSize: { type: 'number', min: 10, max: 1000 },
        maxConcurrentRequests: { type: 'number', min: 1, max: 10 }
      },
      privacy: {
        historyRetentionDays: { type: 'number', min: 1, max: 365 }
      }
    };
  }

  /**
   * @function fixInvalidSettings - 修复无效设置
   * @description 修复检测到的无效设置值
   */
  fixInvalidSettings() {
    try {
      for (const [section, rules] of Object.entries(this.validationRules)) {
        if (this.currentSettings[section]) {
          for (const [key, rule] of Object.entries(rules)) {
            const value = this.currentSettings[section][key];
            if (!this.validateValue(value, rule)) {
              // 使用默认值修复
              if (this.defaultSettings[section] && this.defaultSettings[section][key] !== undefined) {
                this.currentSettings[section][key] = this.defaultSettings[section][key];
                console.log(`[设置管理] 已修复无效设置: ${section}.${key}`);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('[设置管理] 修复无效设置失败:', error);
    }
  }

  /**
   * @function applySettings - 应用设置
   * @description 应用当前设置到系统
   * @returns {Promise<void>}
   */
  async applySettings() {
    try {
      // 应用主题设置
      this.applyThemeSettings();
      
      // 应用语言设置
      this.applyLanguageSettings();
      
      // 应用性能设置
      this.applyPerformanceSettings();
      
      console.log('[设置管理] 设置应用完成');
    } catch (error) {
      console.error('[设置管理] 应用设置失败:', error);
    }
  }

  /**
   * @function applyThemeSettings - 应用主题设置
   * @description 应用主题相关设置（在Background Service Worker中只发送消息）
   */
  applyThemeSettings() {
    try {
      const theme = this.getSetting('ui.theme', 'light');
      const fontSize = this.getSetting('ui.fontSize', 'medium');
      
      // 在Background Service Worker中，通过消息系统通知UI更新
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
          type: 'ai:settings:theme-update',
          data: { theme, fontSize }
        }).catch(() => {
          // 忽略没有接收者的错误
        });
      }
      
      console.log('[设置管理] 主题设置已发送更新通知');
    } catch (error) {
      console.error('[设置管理] 应用主题设置失败:', error);
    }
  }

  /**
   * @function applyLanguageSettings - 应用语言设置
   * @description 应用语言相关设置（在Background Service Worker中只发送消息）
   */
  applyLanguageSettings() {
    try {
      const language = this.getSetting('ui.language', 'zh-CN');
      
      // 在Background Service Worker中，通过消息系统通知UI更新
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
          type: 'ai:settings:language-update',
          data: { language }
        }).catch(() => {
          // 忽略没有接收者的错误
        });
      }
      
      console.log('[设置管理] 语言设置已发送更新通知');
    } catch (error) {
      console.error('[设置管理] 应用语言设置失败:', error);
    }
  }

  /**
   * @function applyPerformanceSettings - 应用性能设置
   * @description 应用性能相关设置（在Background Service Worker中只发送消息）
   */
  applyPerformanceSettings() {
    try {
      const enableAnimations = this.getSetting('ui.enableAnimations', true);
      const compactMode = this.getSetting('ui.compactMode', false);
      
      // 在Background Service Worker中，通过消息系统通知UI更新
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
          type: 'ai:settings:performance-update',
          data: { enableAnimations, compactMode }
        }).catch(() => {
          // 忽略没有接收者的错误
        });
      }
      
      console.log('[设置管理] 性能设置已发送更新通知');
    } catch (error) {
      console.error('[设置管理] 应用性能设置失败:', error);
    }
  }

  /**
   * @function getChangeHistory - 获取变更历史
   * @description 获取设置变更历史记录
   * @param {number} limit - 限制数量
   * @returns {Array} 变更历史
   */
  getChangeHistory(limit = 50) {
    return this.changeHistory.slice(-limit);
  }

  /**
   * @function getAllSettings - 获取所有设置
   * @description 获取当前所有设置
   * @returns {Object} 所有设置
   */
  getAllSettings() {
    return JSON.parse(JSON.stringify(this.currentSettings));
  }

  /**
   * @function cleanup - 清理设置管理器
   * @description 清理资源和监听器
   */
  cleanup() {
    this.listeners.clear();
    this.changeHistory.length = 0;
    this.isInitialized = false;
    
    console.log('[设置管理] 设置管理器已清理');
  }
}