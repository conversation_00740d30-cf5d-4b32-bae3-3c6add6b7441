/**
 * @file AI模板管理系统
 * @description 负责模板管理、智能回复建议、创作增强等功能
 */

/**
 * @class AiTemplateManager - AI模板管理器
 * @description 提供智能模板管理和创作增强功能
 */
export class AiTemplateManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化模板管理器
   * @param {AiApiManager} apiManager - API管理器实例
   * @param {AiSecurityManager} securityManager - 安全管理器实例
   */
  constructor(apiManager, securityManager) {
    this.apiManager = apiManager;
    this.securityManager = securityManager;
    this.isInitialized = false;
    
    // 模板类型常量
    this.TEMPLATE_TYPES = {
      CUSTOMER_SERVICE: 'customer_service',
      EMAIL: 'email',
      SOCIAL_MEDIA: 'social_media',
      DOCUMENT: 'document',
      MARKETING: 'marketing',
      SUPPORT: 'support',
      SALES: 'sales',
      CUSTOM: 'custom'
    };
    
    // 模板分类
    this.TEMPLATE_CATEGORIES = {
      GREETING: 'greeting',
      RESPONSE: 'response',
      CLOSING: 'closing',
      APOLOGY: 'apology',
      EXPLANATION: 'explanation',
      INSTRUCTION: 'instruction'
    };
    
    // 存储键
    this.STORAGE_KEYS = {
      TEMPLATES: 'ai_sidebar_templates',
      USER_PREFERENCES: 'ai_sidebar_template_preferences',
      USAGE_STATS: 'ai_sidebar_template_stats'
    };
    
    // 模板缓存
    this.templateCache = new Map();
    this.userTemplates = new Map();
    this.defaultTemplates = new Map();
    
    // 使用统计
    this.usageStats = {
      totalUsage: 0,
      templateUsage: {},
      popularTemplates: []
    };
  }

  /**
   * @function init - 初始化模板管理器
   * @description 初始化模板系统，加载默认模板和用户模板
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[模板管理] 开始初始化模板管理器...');
      
      // 加载用户使用统计
      await this.loadUsageStats();
      
      // 加载用户模板
      await this.loadUserTemplates();
      
      // 初始化默认模板
      await this.initializeDefaultTemplates();
      
      this.isInitialized = true;
      console.log('[模板管理] 模板管理器初始化完成');
    } catch (error) {
      console.error('[模板管理] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function generateSmartReply - 生成智能回复
   * @description 基于上下文生成智能回复建议
   * @param {Object} contextData - 上下文数据
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 回复建议结果
   */
  async generateSmartReply(contextData, options = {}) {
    try {
      console.log('[模板管理] 生成智能回复建议...');
      
      const { 
        pageContent, 
        userInput = '', 
        tone = 'professional',
        length = 'medium',
        templateType = null 
      } = contextData;
      
      // 分析上下文并选择合适的模板
      const suggestedTemplates = await this.suggestTemplates(contextData);
      
      // 生成个性化回复
      const smartReplies = await this.generateContextualReplies(contextData, options);
      
      // 合并模板建议和智能回复
      const result = {
        success: true,
        timestamp: Date.now(),
        suggestions: {
          smart_replies: smartReplies,
          templates: suggestedTemplates,
          quick_actions: this.generateQuickActions(contextData)
        },
        context: {
          detected_intent: this.detectIntent(userInput),
          suggested_tone: this.suggestTone(pageContent),
          content_type: this.detectContentType(pageContent)
        }
      };
      
      // 更新使用统计
      this.updateUsageStats('smart_reply_generation');
      
      return result;
    } catch (error) {
      console.error('[模板管理] 智能回复生成失败:', error);
      return {
        success: false,
        error: error.message,
        fallback_suggestions: this.getFallbackSuggestions()
      };
    }
  }

  /**
   * @function suggestTemplates - 建议模板
   * @description 根据上下文建议合适的模板
   * @param {Object} contextData - 上下文数据
   * @returns {Promise<Array>} 建议的模板列表
   */
  async suggestTemplates(contextData) {
    try {
      const { pageContent, userInput = '' } = contextData;
      
      // 检测意图
      const intent = this.detectIntent(userInput);
      
      // 获取相关模板
      const relevantTemplates = this.getTemplatesByIntent(intent);
      
      // 根据页面内容进一步筛选
      const contextualTemplates = this.filterTemplatesByContext(relevantTemplates, pageContent);
      
      // 排序并返回前5个最相关的模板
      return contextualTemplates
        .sort((a, b) => b.relevance_score - a.relevance_score)
        .slice(0, 5)
        .map(template => ({
          id: template.id,
          title: template.title,
          content: template.content,
          category: template.category,
          relevance_score: template.relevance_score,
          preview: this.generateTemplatePreview(template, contextData)
        }));
    } catch (error) {
      console.error('[模板管理] 模板建议失败:', error);
      return this.getDefaultTemplates().slice(0, 3);
    }
  }

  /**
   * @function generateContextualReplies - 生成上下文回复
   * @description 使用AI生成基于上下文的个性化回复
   * @param {Object} contextData - 上下文数据
   * @param {Object} options - 生成选项
   * @returns {Promise<Array>} 生成的回复列表
   */
  async generateContextualReplies(contextData, options) {
    try {
      const response = await this.apiManager.generateSmartReply(contextData);
      
      if (response.success) {
        return response.suggestions.map((suggestion, index) => ({
          id: `smart_reply_${index}`,
          content: suggestion,
          type: 'ai_generated',
          confidence: 0.8,
          tone: this.analyzeTone(suggestion)
        }));
      } else {
        throw new Error(response.error || 'AI回复生成失败');
      }
    } catch (error) {
      console.error('[模板管理] 上下文回复生成失败:', error);
      
      // 回退到本地生成
      return this.generateFallbackReplies(contextData);
    }
  }

  /**
   * @function createTemplate - 创建模板
   * @description 创建新的用户模板
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} 创建结果
   */
  async createTemplate(templateData) {
    try {
      const {
        title,
        content,
        category,
        type = this.TEMPLATE_TYPES.CUSTOM,
        tags = [],
        isPublic = false
      } = templateData;
      
      // 验证模板数据
      this.validateTemplateData(templateData);
      
      // 生成模板ID
      const templateId = this.securityManager.generateSecureId();
      
      // 创建模板对象
      const template = {
        id: templateId,
        title: this.securityManager.sanitizeUserInput(title),
        content: this.securityManager.sanitizeUserInput(content),
        category: category,
        type: type,
        tags: tags.map(tag => this.securityManager.sanitizeUserInput(tag)),
        isPublic: isPublic,
        created_at: Date.now(),
        updated_at: Date.now(),
        usage_count: 0,
        author: 'user'
      };
      
      // 保存到用户模板
      this.userTemplates.set(templateId, template);
      
      // 持久化存储
      await this.saveUserTemplates();
      
      console.log(`[模板管理] 模板创建成功: ${title}`);
      
      return {
        success: true,
        template_id: templateId,
        template: template
      };
    } catch (error) {
      console.error('[模板管理] 模板创建失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function updateTemplate - 更新模板
   * @description 更新现有模板
   * @param {string} templateId - 模板ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateTemplate(templateId, updateData) {
    try {
      const template = this.userTemplates.get(templateId);
      if (!template) {
        throw new Error('模板不存在');
      }
      
      // 验证更新数据
      this.validateTemplateData(updateData, false);
      
      // 更新模板
      const updatedTemplate = {
        ...template,
        ...updateData,
        updated_at: Date.now()
      };
      
      // 清理数据
      if (updatedTemplate.title) {
        updatedTemplate.title = this.securityManager.sanitizeUserInput(updatedTemplate.title);
      }
      if (updatedTemplate.content) {
        updatedTemplate.content = this.securityManager.sanitizeUserInput(updatedTemplate.content);
      }
      
      this.userTemplates.set(templateId, updatedTemplate);
      
      // 持久化存储
      await this.saveUserTemplates();
      
      console.log(`[模板管理] 模板更新成功: ${templateId}`);
      
      return {
        success: true,
        template: updatedTemplate
      };
    } catch (error) {
      console.error('[模板管理] 模板更新失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function deleteTemplate - 删除模板
   * @description 删除用户模板
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteTemplate(templateId) {
    try {
      if (!this.userTemplates.has(templateId)) {
        throw new Error('模板不存在');
      }
      
      this.userTemplates.delete(templateId);
      
      // 清理缓存
      this.templateCache.delete(templateId);
      
      // 持久化存储
      await this.saveUserTemplates();
      
      console.log(`[模板管理] 模板删除成功: ${templateId}`);
      
      return {
        success: true,
        deleted_id: templateId
      };
    } catch (error) {
      console.error('[模板管理] 模板删除失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function getAllTemplates - 获取所有模板
   * @description 获取用户的所有模板
   * @param {Object} filters - 过滤条件
   * @returns {Array} 模板列表
   */
  getAllTemplates(filters = {}) {
    const { category, type, search } = filters;
    
    let templates = Array.from(this.userTemplates.values());
    
    // 添加默认模板
    templates = templates.concat(this.defaultTemplates);
    
    // 应用过滤器
    if (category) {
      templates = templates.filter(t => t.category === category);
    }
    
    if (type) {
      templates = templates.filter(t => t.type === type);
    }
    
    if (search) {
      const searchLower = search.toLowerCase();
      templates = templates.filter(t => 
        t.title.toLowerCase().includes(searchLower) ||
        t.content.toLowerCase().includes(searchLower) ||
        t.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // 按使用频率和更新时间排序
    return templates.sort((a, b) => {
      const scoreA = (a.usage_count || 0) * 0.7 + (a.updated_at || 0) * 0.3;
      const scoreB = (b.usage_count || 0) * 0.7 + (b.updated_at || 0) * 0.3;
      return scoreB - scoreA;
    });
  }

  /**
   * @function useTemplate - 使用模板
   * @description 使用指定模板并更新统计
   * @param {string} templateId - 模板ID
   * @param {Object} variables - 模板变量
   * @returns {Object} 使用结果
   */
  async useTemplate(templateId, variables = {}) {
    try {
      let template = this.userTemplates.get(templateId);
      
      // 如果不是用户模板，检查默认模板
      if (!template) {
        template = this.defaultTemplates.find(t => t.id === templateId);
      }
      
      if (!template) {
        throw new Error('模板不存在');
      }
      
      // 处理模板变量
      const processedContent = this.processTemplateVariables(template.content, variables);
      
      // 更新使用统计
      await this.updateTemplateUsage(templateId);
      
      return {
        success: true,
        content: processedContent,
        template: template,
        processed_variables: variables
      };
    } catch (error) {
      console.error('[模板管理] 模板使用失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function initializeDefaultTemplates - 初始化默认模板
   * @description 加载系统预设的默认模板
   * @returns {Promise<void>}
   */
  async initializeDefaultTemplates() {
    try {
      console.log('[模板管理] 初始化默认模板...');
      
      const defaultTemplates = [
        {
          id: 'default_greeting_professional',
          title: '专业问候',
          content: '您好！感谢您的咨询。我是{{name}}，很高兴为您服务。请问有什么可以帮助您的吗？',
          category: this.TEMPLATE_CATEGORIES.GREETING,
          type: this.TEMPLATE_TYPES.CUSTOMER_SERVICE,
          tags: ['问候', '专业', '客服'],
          isDefault: true,
          usage_count: 0
        },
        {
          id: 'default_apology_standard',
          title: '标准道歉',
          content: '非常抱歉给您带来了不便。我们会立即处理这个问题，并确保类似情况不再发生。请您耐心等待，我们会尽快给您满意的解决方案。',
          category: this.TEMPLATE_CATEGORIES.APOLOGY,
          type: this.TEMPLATE_TYPES.CUSTOMER_SERVICE,
          tags: ['道歉', '客服', '标准'],
          isDefault: true,
          usage_count: 0
        },
        {
          id: 'default_closing_polite',
          title: '礼貌结束',
          content: '如果您还有其他问题，请随时联系我们。感谢您的理解与支持，祝您生活愉快！',
          category: this.TEMPLATE_CATEGORIES.CLOSING,
          type: this.TEMPLATE_TYPES.CUSTOMER_SERVICE,
          tags: ['结束', '礼貌', '感谢'],
          isDefault: true,
          usage_count: 0
        },
        {
          id: 'default_explanation_technical',
          title: '技术说明',
          content: '关于{{issue}}，这通常是由{{cause}}引起的。解决步骤如下：\n1. {{step1}}\n2. {{step2}}\n3. {{step3}}\n\n如果问题仍然存在，请提供更多详细信息以便我们进一步协助。',
          category: this.TEMPLATE_CATEGORIES.EXPLANATION,
          type: this.TEMPLATE_TYPES.SUPPORT,
          tags: ['技术', '说明', '步骤'],
          isDefault: true,
          usage_count: 0
        },
        {
          id: 'default_email_formal',
          title: '正式邮件',
          content: '尊敬的{{recipient}}：\n\n希望这封邮件能够找到您身体健康、工作顺利。\n\n{{main_content}}\n\n如有任何疑问，请随时与我联系。期待您的回复。\n\n此致\n敬礼！\n\n{{sender}}\n{{date}}',
          category: this.TEMPLATE_CATEGORIES.RESPONSE,
          type: this.TEMPLATE_TYPES.EMAIL,
          tags: ['邮件', '正式', '商务'],
          isDefault: true,
          usage_count: 0
        }
      ];
      
      // 将默认模板存储到实例属性中
      this.defaultTemplates.clear();
      defaultTemplates.forEach(template => {
        this.defaultTemplates.set(template.id, template);
      });
      
      console.log(`[模板管理] 已加载 ${this.defaultTemplates.size} 个默认模板`);
    } catch (error) {
      console.error('[模板管理] 默认模板初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function detectIntent - 检测意图
   * @description 分析用户输入的意图
   * @param {string} userInput - 用户输入
   * @returns {string} 检测到的意图
   */
  detectIntent(userInput) {
    if (!userInput) return 'general';
    
    const input = userInput.toLowerCase();
    
    // 意图模式匹配
    const intentPatterns = {
      'complaint': /投诉|抱怨|不满|问题|故障|bug|错误/,
      'inquiry': /咨询|询问|请问|了解|知道/,
      'request': /申请|要求|需要|希望|想要/,
      'greeting': /你好|您好|hello|hi/,
      'closing': /谢谢|感谢|再见|bye/,
      'apology': /抱歉|对不起|sorry/,
      'praise': /很好|不错|满意|赞/
    };
    
    for (const [intent, pattern] of Object.entries(intentPatterns)) {
      if (pattern.test(input)) {
        return intent;
      }
    }
    
    return 'general';
  }

  /**
   * @function suggestTone - 建议语调
   * @description 根据页面内容建议合适的语调
   * @param {Object} pageContent - 页面内容
   * @returns {string} 建议的语调
   */
  suggestTone(pageContent) {
    if (!pageContent || !pageContent.summary) return 'professional';
    
    const content = pageContent.summary.toLowerCase();
    
    if (/商务|商业|公司|企业/.test(content)) return 'professional';
    if (/技术|开发|代码|API/.test(content)) return 'technical';
    if (/客服|服务|帮助/.test(content)) return 'friendly';
    if (/紧急|重要|严重/.test(content)) return 'urgent';
    
    return 'professional';
  }

  /**
   * @function detectContentType - 检测内容类型
   * @description 检测页面内容类型
   * @param {Object} pageContent - 页面内容
   * @returns {string} 内容类型
   */
  detectContentType(pageContent) {
    if (!pageContent) return 'general';
    
    const summary = pageContent.summary || '';
    const contentType = pageContent.contentType || '';
    
    if (contentType === 'product' || /产品|商品|购买/.test(summary)) return 'ecommerce';
    if (contentType === 'news' || /新闻|资讯/.test(summary)) return 'news';
    if (/客服|支持|帮助/.test(summary)) return 'support';
    if (/博客|文章|post/.test(summary)) return 'blog';
    
    return 'general';
  }

  /**
   * @function processTemplateVariables - 处理模板变量
   * @description 替换模板中的变量占位符
   * @param {string} template - 模板内容
   * @param {Object} variables - 变量对象
   * @returns {string} 处理后的内容
   */
  processTemplateVariables(template, variables) {
    let processed = template;
    
    // 替换变量占位符 {{variable}}
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      processed = processed.replace(placeholder, value || '');
    });
    
    // 处理未替换的占位符
    processed = processed.replace(/\{\{[^}]+\}\}/g, '[请填写]');
    
    return processed;
  }

  /**
   * @function validateTemplateData - 验证模板数据
   * @description 验证模板数据的有效性
   * @param {Object} templateData - 模板数据
   * @param {boolean} isCreate - 是否为创建操作
   */
  validateTemplateData(templateData, isCreate = true) {
    if (isCreate) {
      if (!templateData.title || templateData.title.trim().length === 0) {
        throw new Error('模板标题不能为空');
      }
      
      if (!templateData.content || templateData.content.trim().length === 0) {
        throw new Error('模板内容不能为空');
      }
    }
    
    if (templateData.title && templateData.title.length > 100) {
      throw new Error('模板标题不能超过100个字符');
    }
    
    if (templateData.content && templateData.content.length > 5000) {
      throw new Error('模板内容不能超过5000个字符');
    }
    
    if (templateData.tags && templateData.tags.length > 10) {
      throw new Error('标签数量不能超过10个');
    }
  }

  /**
   * @function loadUserTemplates - 加载用户模板
   * @description 从存储中加载用户模板
   * @returns {Promise<void>}
   */
  async loadUserTemplates() {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEYS.TEMPLATES]);
      const templatesData = result[this.STORAGE_KEYS.TEMPLATES] || {};
      
      this.userTemplates.clear();
      Object.entries(templatesData).forEach(([id, template]) => {
        this.userTemplates.set(id, template);
      });
      
      console.log(`[模板管理] 加载了 ${this.userTemplates.size} 个用户模板`);
    } catch (error) {
      console.error('[模板管理] 用户模板加载失败:', error);
    }
  }

  /**
   * @function saveUserTemplates - 保存用户模板
   * @description 将用户模板保存到存储
   * @returns {Promise<void>}
   */
  async saveUserTemplates() {
    try {
      const templatesData = {};
      this.userTemplates.forEach((template, id) => {
        templatesData[id] = template;
      });
      
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.TEMPLATES]: templatesData
      });
    } catch (error) {
      console.error('[模板管理] 用户模板保存失败:', error);
    }
  }

  /**
   * @function loadUsageStats - 加载使用统计
   * @description 从存储中加载模板使用统计数据
   * @returns {Promise<void>}
   */
  async loadUsageStats() {
    try {
      const result = await chrome.storage.local.get(['template_usage_stats']);
      this.usageStats = result.template_usage_stats || {};
      console.log('[模板管理] 使用统计数据已加载');
    } catch (error) {
      console.error('[模板管理] 使用统计加载失败:', error);
      this.usageStats = {};
    }
  }

  /**
   * @function updateUsageStats - 更新使用统计
   * @description 更新模板使用统计数据
   * @param {string} action - 操作类型
   * @param {string} templateId - 模板ID（可选）
   */
  async updateUsageStats(action, templateId = null) {
    try {
      if (!this.usageStats) {
        this.usageStats = {};
      }

      // 更新操作计数
      if (!this.usageStats[action]) {
        this.usageStats[action] = 0;
      }
      this.usageStats[action]++;

      // 如果有模板ID，更新特定模板的使用次数
      if (templateId) {
        const statsKey = `template_${templateId}`;
        if (!this.usageStats[statsKey]) {
          this.usageStats[statsKey] = 0;
        }
        this.usageStats[statsKey]++;
      }

      // 保存到存储
      await chrome.storage.local.set({
        template_usage_stats: this.usageStats
      });

      console.log(`[模板管理] 更新统计: ${action}`, templateId ? `模板: ${templateId}` : '');
    } catch (error) {
      console.error('[模板管理] 统计更新失败:', error);
    }
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理模板管理器占用的资源
   */
  cleanup() {
    this.templateCache.clear();
    this.userTemplates.clear();
    this.defaultTemplates.clear();
    this.isInitialized = false;
    console.log('[模板管理] 模板管理器已清理');
  }

  /**
   * @function getTemplatesByIntent - 根据意图获取模板
   * @description 根据检测到的意图获取相关模板
   * @param {string} intent - 用户意图
   * @returns {Array} 相关模板列表
   */
  getTemplatesByIntent(intent) {
    const allTemplates = [...this.defaultTemplates.values(), ...this.userTemplates.values()];
    
    if (intent === 'general') {
      return allTemplates;
    }
    
    return allTemplates.filter(template => {
      const category = template.category.toLowerCase();
      const tags = template.tags.map(tag => tag.toLowerCase());
      
      switch (intent) {
        case 'greeting':
          return category === 'greeting' || tags.includes('问候');
        case 'apology':
          return category === 'apology' || tags.includes('道歉');
        case 'complaint':
          return category === 'apology' || category === 'explanation';
        case 'inquiry':
          return category === 'response' || category === 'explanation';
        case 'closing':
          return category === 'closing' || tags.includes('结束');
        default:
          return true;
      }
    });
  }

  /**
   * @function filterTemplatesByContext - 根据上下文过滤模板
   * @description 根据页面内容上下文进一步过滤模板
   * @param {Array} templates - 模板列表
   * @param {Object} pageContent - 页面内容
   * @returns {Array} 过滤后的模板列表
   */
  filterTemplatesByContext(templates, pageContent) {
    return templates.map(template => {
      let relevanceScore = 0.5; // 基础分数
      
      if (pageContent && pageContent.summary) {
        const content = pageContent.summary.toLowerCase();
        const templateContent = template.content.toLowerCase();
        const templateTags = template.tags.map(tag => tag.toLowerCase());
        
        // 内容匹配度
        if (templateContent.includes('客服') && /客服|服务|支持/.test(content)) {
          relevanceScore += 0.3;
        }
        
        if (templateContent.includes('技术') && /技术|开发|代码/.test(content)) {
          relevanceScore += 0.3;
        }
        
        // 标签匹配度
        templateTags.forEach(tag => {
          if (content.includes(tag)) {
            relevanceScore += 0.1;
          }
        });
      }
      
      return {
        ...template,
        relevance_score: Math.min(relevanceScore, 1.0)
      };
    });
  }

  /**
   * @function generateQuickActions - 生成快速操作
   * @description 基于上下文生成快速操作建议
   * @param {Object} contextData - 上下文数据
   * @returns {Array} 快速操作列表
   */
  generateQuickActions(contextData) {
    const quickActions = [
      {
        id: 'action_copy_content',
        title: '复制内容',
        icon: 'copy',
        action: 'copy_content'
      },
      {
        id: 'action_save_template',
        title: '保存为模板',
        icon: 'save',
        action: 'save_template'
      },
      {
        id: 'action_translate',
        title: '翻译内容',
        icon: 'translate',
        action: 'translate'
      }
    ];
    
    // 根据页面内容类型添加特定操作
    if (contextData.pageContent) {
      const contentType = this.detectContentType(contextData.pageContent);
      
      if (contentType === 'ecommerce') {
        quickActions.push({
          id: 'action_product_inquiry',
          title: '产品咨询',
          icon: 'shopping',
          action: 'product_inquiry'
        });
      }
      
      if (contentType === 'support') {
        quickActions.push({
          id: 'action_escalate',
          title: '升级处理',
          icon: 'escalate',
          action: 'escalate_issue'
        });
      }
    }
    
    return quickActions;
  }

  /**
   * @function getDefaultTemplates - 获取默认模板
   * @description 获取系统默认模板列表
   * @returns {Array} 默认模板数组
   */
  getDefaultTemplates() {
    return Array.from(this.defaultTemplates.values());
  }

  /**
   * @function generateTemplatePreview - 生成模板预览
   * @description 生成模板的预览内容
   * @param {Object} template - 模板对象
   * @param {Object} contextData - 上下文数据
   * @returns {string} 预览内容
   */
  generateTemplatePreview(template, contextData) {
    let preview = template.content;
    
    // 使用上下文数据填充部分变量
    const autoVariables = {
      name: '助手',
      recipient: '用户',
      sender: '客服',
      date: new Date().toLocaleDateString('zh-CN')
    };
    
    // 如果有页面内容，尝试提取一些信息
    if (contextData.pageContent) {
      autoVariables.issue = '您的问题';
      autoVariables.cause = '系统原因';
    }
    
    preview = this.processTemplateVariables(preview, autoVariables);
    
    // 限制预览长度
    if (preview.length > 100) {
      preview = preview.substring(0, 100) + '...';
    }
    
    return preview;
  }

  /**
   * @function analyzeTone - 分析语调
   * @description 分析文本的语调风格
   * @param {string} text - 要分析的文本
   * @returns {string} 语调类型
   */
  analyzeTone(text) {
    if (!text) return 'neutral';
    
    const lowercaseText = text.toLowerCase();
    
    if (/谢谢|感谢|非常|很|特别/.test(lowercaseText)) {
      return 'appreciative';
    }
    
    if (/抱歉|对不起|不好意思/.test(lowercaseText)) {
      return 'apologetic';
    }
    
    if (/您好|请问|麻烦|劳烦/.test(lowercaseText)) {
      return 'polite';
    }
    
    if (/技术|系统|配置|设置/.test(lowercaseText)) {
      return 'technical';
    }
    
    return 'professional';
  }

  /**
   * @function generateFallbackReplies - 生成后备回复
   * @description 当AI生成失败时，提供后备回复
   * @param {Object} contextData - 上下文数据
   * @returns {Array} 后备回复列表
   */
  generateFallbackReplies(contextData) {
    const fallbackReplies = [
      {
        id: 'fallback_1',
        content: '感谢您的咨询，我正在为您查询相关信息，请稍等片刻。',
        type: 'fallback',
        confidence: 0.6,
        tone: 'professional'
      },
      {
        id: 'fallback_2',
        content: '我理解您的需求，让我为您提供详细的解答。',
        type: 'fallback',
        confidence: 0.6,
        tone: 'helpful'
      },
      {
        id: 'fallback_3',
        content: '这是一个很好的问题，我会尽力为您提供满意的答案。',
        type: 'fallback',
        confidence: 0.6,
        tone: 'encouraging'
      }
    ];
    
    // 根据上下文调整后备回复
    if (contextData.userInput) {
      const intent = this.detectIntent(contextData.userInput);
      
      if (intent === 'complaint') {
        fallbackReplies.unshift({
          id: 'fallback_complaint',
          content: '非常抱歉给您带来了不便，我会立即为您处理这个问题。',
          type: 'fallback',
          confidence: 0.7,
          tone: 'apologetic'
        });
      }
    }
    
    return fallbackReplies;
  }

  /**
   * @function getFallbackSuggestions - 获取后备建议
   * @description 获取系统后备建议
   * @returns {Array} 后备建议列表
   */
  getFallbackSuggestions() {
    return [
      {
        id: 'suggest_greeting',
        title: '友好问候',
        content: '您好！有什么可以帮助您的吗？',
        category: 'greeting'
      },
      {
        id: 'suggest_thanks',
        title: '感谢回复',
        content: '感谢您的耐心，我会尽力为您解决问题。',
        category: 'response'
      },
      {
        id: 'suggest_closing',
        title: '礼貌结束',
        content: '如有其他问题，请随时联系我们。祝您生活愉快！',
        category: 'closing'
      }
    ];
  }
} 