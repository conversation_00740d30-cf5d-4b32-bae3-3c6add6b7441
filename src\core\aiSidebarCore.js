/**
 * @file AI侧边栏核心模块
 * @description 协调所有子模块，提供统一的业务逻辑接口
 */

// 导入依赖模块
import { AiSecurityManager } from '../security/aiSecurityManager.js';
import { AiApiManager } from '../integrations/aiApiManager.js';
import { AiContentAnalyzer } from '../analysis/aiContentAnalyzer.js';
import { AiTemplateManager } from '../templates/aiTemplateManager.js';

/**
 * @class AiSidebarCore - AI侧边栏核心类
 * @description 核心业务逻辑协调器，管理所有AI功能模块
 */
export class AiSidebarCore {
  /**
   * @function constructor - 构造函数
   * @description 初始化核心模块和所有子模块
   */
  constructor() {
    this.isInitialized = false;
    this.initializationPromise = null;
    
    // 核心配置
    this.config = {
      version: '2.0.0',
      enableAnalytics: true,
      autoSave: true,
      maxConcurrentRequests: 3
    };
    
    // 子模块实例
    this.securityManager = null;
    this.apiManager = null;
    this.contentAnalyzer = null;
    this.templateManager = null;
    
    // 状态管理
    this.state = {
      currentTab: null,
      activeAnalysis: null,
      chatHistory: new Map(),
      userPreferences: {},
      connectionStatus: 'disconnected'
    };
    
    // 事件系统
    this.eventListeners = new Map();
    this.messageQueue = [];
    this.isProcessingQueue = false;
    
    // 性能监控
    this.performance = {
      startTime: Date.now(),
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0
    };
  }

  /**
   * @function init - 初始化核心模块
   * @description 按顺序初始化所有子模块
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isInitialized) {
      return;
    }
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  /**
   * @function _performInitialization - 执行初始化
   * @description 内部初始化方法
   * @returns {Promise<void>}
   */
  async _performInitialization() {
    try {
      console.log('[AI核心] 开始初始化AI侧边栏核心模块...');
      
      // 1. 初始化安全管理器
      console.log('[AI核心] 初始化安全管理器...');
      this.securityManager = new AiSecurityManager();
      await this.securityManager.init();
      
      // 2. 初始化API管理器
      console.log('[AI核心] 初始化API管理器...');
      this.apiManager = new AiApiManager(this.securityManager);
      await this.apiManager.init();
      
      // 3. 初始化内容分析器
      console.log('[AI核心] 初始化内容分析器...');
      this.contentAnalyzer = new AiContentAnalyzer(this.apiManager);
      await this.contentAnalyzer.init();
      
      // 4. 初始化模板管理器
      console.log('[AI核心] 初始化模板管理器...');
      this.templateManager = new AiTemplateManager(this.apiManager, this.securityManager);
      await this.templateManager.init();
      
      // 5. 加载用户偏好
      await this.loadUserPreferences();
      
      // 6. 设置事件监听
      this.setupEventListeners();
      
      // 7. 启动消息队列处理
      this.startMessageQueueProcessor();
      
      this.isInitialized = true;
      this.state.connectionStatus = 'connected';
      
      console.log('[AI核心] AI侧边栏核心模块初始化完成');
      
      // 触发初始化完成事件
      this.emit('core:initialized', {
        version: this.config.version,
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error('[AI核心] 初始化失败:', error);
      this.state.connectionStatus = 'error';
      throw error;
    }
  }

  /**
   * @function handleChatMessage - 处理聊天消息
   * @description 处理用户聊天消息并返回AI回复
   * @param {Object} messageData - 消息数据
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} 处理结果
   */
  async handleChatMessage(messageData, tabId) {
    const startTime = Date.now();
    
    try {
      console.log('[AI核心] 处理聊天消息...');
      
      if (!this.isInitialized) {
        throw new Error('核心模块未初始化');
      }
      
      const { message, isStreaming = false, context = null } = messageData;
      
      // 安全验证
      const sanitizedMessage = this.securityManager.sanitizeUserInput(message);
      
      // 获取或创建聊天历史
      const chatHistory = this.getChatHistory(tabId);
      
      // 添加用户消息到历史
      chatHistory.push({
        type: 'user',
        text: sanitizedMessage,
        timestamp: Date.now()
      });
      
      // 构建请求数据
      const requestData = {
        message: sanitizedMessage,
        history: chatHistory.slice(-10), // 最近10条消息
        context: context
      };
      
      let response;
      
      if (isStreaming) {
        // 流式响应
        response = await this.handleStreamingChat(requestData, tabId);
      } else {
        // 常规响应
        response = await this.apiManager.sendChatMessage(requestData, tabId);
      }
      
      if (response.success) {
        // 添加AI回复到历史
        chatHistory.push({
          type: 'assistant',
          text: response.reply,
          timestamp: Date.now(),
          model: response.model
        });
        
        // 保存聊天历史
        this.setChatHistory(tabId, chatHistory);
        
        // 更新性能统计
        this.updatePerformanceStats(true, Date.now() - startTime);
        
        return {
          success: true,
          reply: response.reply,
          model: response.model,
          processingTime: Date.now() - startTime
        };
      } else {
        throw new Error(response.error || '聊天处理失败');
      }
      
    } catch (error) {
      console.error('[AI核心] 聊天消息处理失败:', error);
      this.updatePerformanceStats(false, Date.now() - startTime);
      
      return {
        success: false,
        error: error.message,
        fallbackReply: this.getFallbackReply(messageData)
      };
    }
  }

  /**
   * @function handleStreamingChat - 处理流式聊天
   * @description 处理流式聊天响应
   * @param {Object} requestData - 请求数据
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} 处理结果
   */
  async handleStreamingChat(requestData, tabId) {
    return new Promise((resolve, reject) => {
      let fullResponse = '';
      let isComplete = false;
      
      const onChunk = (chunk) => {
        if (chunk.error) {
          reject(new Error(chunk.error));
          return;
        }
        
        if (chunk.text) {
          fullResponse += chunk.text;
          
          // 实时发送流式数据到UI
          this.emit('chat:streaming', {
            tabId: tabId,
            chunk: chunk.text,
            fullText: fullResponse,
            isComplete: false
          });
        }
        
        if (chunk.done) {
          isComplete = true;
          
          // 发送完成信号
          this.emit('chat:streaming', {
            tabId: tabId,
            chunk: '',
            fullText: fullResponse,
            isComplete: true
          });
          
          resolve({
            success: true,
            reply: fullResponse,
            model: 'gemini-streaming'
          });
        }
      };
      
      // 发送流式请求
      this.apiManager.sendStreamingMessage(requestData, onChunk)
        .catch(error => {
          if (!isComplete) {
            reject(error);
          }
        });
    });
  }

  /**
   * @function analyzePageContent - 分析页面内容
   * @description 分析当前页面内容并返回结果
   * @param {Object} pageData - 页面数据
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 分析结果
   */
  async analyzePageContent(pageData, options = {}) {
    try {
      console.log('[AI核心] 分析页面内容...');
      
      if (!this.isInitialized) {
        throw new Error('核心模块未初始化');
      }
      
      // 执行内容分析
      const analysisResult = await this.contentAnalyzer.analyzePageContent(pageData, options);
      
      if (analysisResult.success) {
        // 保存分析结果到状态
        this.state.activeAnalysis = {
          tabId: pageData.tabId,
          result: analysisResult,
          timestamp: Date.now()
        };
        
        // 触发分析完成事件
        this.emit('analysis:completed', {
          tabId: pageData.tabId,
          result: analysisResult
        });
        
        return analysisResult;
      } else {
        throw new Error(analysisResult.error || '页面分析失败');
      }
      
    } catch (error) {
      console.error('[AI核心] 页面内容分析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function generateSmartReply - 生成智能回复
   * @description 生成基于上下文的智能回复建议
   * @param {Object} contextData - 上下文数据
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 回复建议结果
   */
  async generateSmartReply(contextData, options = {}) {
    try {
      console.log('[AI核心] 生成智能回复建议...');
      
      if (!this.isInitialized) {
        throw new Error('核心模块未初始化');
      }
      
      // 使用模板管理器生成智能回复
      const replyResult = await this.templateManager.generateSmartReply(contextData, options);
      
      if (replyResult.success) {
        // 触发回复生成完成事件
        this.emit('reply:generated', {
          suggestions: replyResult.suggestions,
          context: replyResult.context
        });
        
        return replyResult;
      } else {
        throw new Error(replyResult.error || '智能回复生成失败');
      }
      
    } catch (error) {
      console.error('[AI核心] 智能回复生成失败:', error);
      return {
        success: false,
        error: error.message,
        fallback_suggestions: this.templateManager.getFallbackSuggestions()
      };
    }
  }

  /**
   * @function manageTemplate - 管理模板
   * @description 统一的模板管理接口
   * @param {string} action - 操作类型 (create|update|delete|get|list)
   * @param {string} templateId - 模板ID（可选）
   * @param {Object} data - 操作数据（可选）
   * @returns {Promise<Object>} 操作结果
   */
  async manageTemplate(action, templateId = null, data = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('核心模块未初始化');
      }
      
      let result;
      
      switch (action) {
        case 'create':
          result = await this.templateManager.createTemplate(data);
          break;
          
        case 'update':
          result = await this.templateManager.updateTemplate(templateId, data);
          break;
          
        case 'delete':
          result = await this.templateManager.deleteTemplate(templateId);
          break;
          
        case 'get':
          result = await this.templateManager.useTemplate(templateId, data);
          break;
          
        case 'list':
          result = {
            success: true,
            templates: this.templateManager.getAllTemplates(data)
          };
          break;
          
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
      
      // 触发模板操作事件
      this.emit('template:operation', {
        action: action,
        templateId: templateId,
        result: result
      });
      
      return result;
      
    } catch (error) {
      console.error(`[AI核心] 模板${action}操作失败:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function configureApiKey - 配置API密钥
   * @description 安全配置AI服务API密钥
   * @param {string} provider - 服务提供商
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 配置结果
   */
  async configureApiKey(provider, apiKey) {
    try {
      if (!this.isInitialized) {
        throw new Error('核心模块未初始化');
      }
      
      // 验证API密钥格式
      if (!apiKey || apiKey.trim().length === 0) {
        throw new Error('API密钥不能为空');
      }
      
      // 使用安全管理器存储密钥
      await this.securityManager.storeApiKey(provider, apiKey);
      
      // 重新验证API管理器
      await this.apiManager.validateApiKeys();
      
      console.log(`[AI核心] ${provider} API密钥配置成功`);
      
      // 触发配置完成事件
      this.emit('api:configured', {
        provider: provider,
        timestamp: Date.now()
      });
      
      return {
        success: true,
        provider: provider
      };
      
    } catch (error) {
      console.error('[AI核心] API密钥配置失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function getChatHistory - 获取聊天历史
   * @description 获取指定标签页的聊天历史
   * @param {number} tabId - 标签页ID
   * @returns {Array} 聊天历史数组
   */
  getChatHistory(tabId) {
    if (!this.state.chatHistory.has(tabId)) {
      this.state.chatHistory.set(tabId, []);
    }
    return this.state.chatHistory.get(tabId);
  }

  /**
   * @function setChatHistory - 设置聊天历史
   * @description 设置指定标签页的聊天历史
   * @param {number} tabId - 标签页ID
   * @param {Array} history - 聊天历史数组
   */
  setChatHistory(tabId, history) {
    // 限制历史记录长度
    const maxHistory = 50;
    if (history.length > maxHistory) {
      history = history.slice(-maxHistory);
    }
    
    this.state.chatHistory.set(tabId, history);
    
    // 自动保存（如果启用）
    if (this.config.autoSave) {
      this.saveChatHistory(tabId, history);
    }
  }

  /**
   * @function clearChatHistory - 清除聊天历史
   * @description 清除指定标签页或所有标签页的聊天历史
   * @param {number} tabId - 标签页ID（可选，不提供则清除所有）
   */
  clearChatHistory(tabId = null) {
    if (tabId) {
      this.state.chatHistory.delete(tabId);
      console.log(`[AI核心] 已清除标签页 ${tabId} 的聊天历史`);
    } else {
      this.state.chatHistory.clear();
      console.log('[AI核心] 已清除所有聊天历史');
    }
    
    // 触发历史清除事件
    this.emit('chat:history_cleared', { tabId: tabId });
  }

  /**
   * @function saveChatHistory - 保存聊天历史
   * @description 将聊天历史保存到存储
   * @param {number} tabId - 标签页ID
   * @param {Array} history - 聊天历史
   */
  async saveChatHistory(tabId, history) {
    try {
      const storageKey = `ai_sidebar_chat_history_${tabId}`;
      await chrome.storage.local.set({
        [storageKey]: {
          history: history,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('[AI核心] 聊天历史保存失败:', error);
    }
  }

  /**
   * @function loadChatHistory - 加载聊天历史
   * @description 从存储加载聊天历史
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Array>} 聊天历史数组
   */
  async loadChatHistory(tabId) {
    try {
      const storageKey = `ai_sidebar_chat_history_${tabId}`;
      const result = await chrome.storage.local.get([storageKey]);
      const historyData = result[storageKey];
      
      if (historyData && historyData.history) {
        this.state.chatHistory.set(tabId, historyData.history);
        return historyData.history;
      }
      
      return [];
    } catch (error) {
      console.error('[AI核心] 聊天历史加载失败:', error);
      return [];
    }
  }

  /**
   * @function loadUserPreferences - 加载用户偏好
   * @description 从存储加载用户偏好设置
   * @returns {Promise<void>}
   */
  async loadUserPreferences() {
    try {
      const result = await chrome.storage.sync.get(['ai_sidebar_user_preferences']);
      this.state.userPreferences = result.ai_sidebar_user_preferences || {
        theme: 'auto',
        language: 'zh-CN',
        autoAnalysis: true,
        streamingMode: true,
        notifications: true
      };
      
      console.log('[AI核心] 用户偏好加载完成');
    } catch (error) {
      console.error('[AI核心] 用户偏好加载失败:', error);
    }
  }

  /**
   * @function saveUserPreferences - 保存用户偏好
   * @description 保存用户偏好设置到存储
   * @returns {Promise<void>}
   */
  async saveUserPreferences() {
    try {
      await chrome.storage.sync.set({
        'ai_sidebar_user_preferences': this.state.userPreferences
      });
      
      console.log('[AI核心] 用户偏好保存完成');
    } catch (error) {
      console.error('[AI核心] 用户偏好保存失败:', error);
    }
  }

  /**
   * @function updateUserPreference - 更新用户偏好
   * @description 更新单个用户偏好设置
   * @param {string} key - 偏好键
   * @param {any} value - 偏好值
   * @returns {Promise<void>}
   */
  async updateUserPreference(key, value) {
    this.state.userPreferences[key] = value;
    await this.saveUserPreferences();
    
    // 触发偏好更新事件
    this.emit('preferences:updated', {
      key: key,
      value: value
    });
  }

  /**
   * @function getFallbackReply - 获取备用回复
   * @description 当AI服务不可用时提供备用回复
   * @param {Object} messageData - 消息数据
   * @returns {string} 备用回复
   */
  getFallbackReply(messageData) {
    const fallbackReplies = [
      '抱歉，AI服务暂时不可用，请稍后再试。',
      '当前网络连接有问题，请检查网络后重试。',
      '服务正在维护中，感谢您的耐心等待。'
    ];
    
    return fallbackReplies[Math.floor(Math.random() * fallbackReplies.length)];
  }

  /**
   * @function updatePerformanceStats - 更新性能统计
   * @description 更新系统性能统计信息
   * @param {boolean} success - 是否成功
   * @param {number} responseTime - 响应时间
   */
  updatePerformanceStats(success, responseTime) {
    this.performance.requestCount++;
    
    if (!success) {
      this.performance.errorCount++;
    }
    
    // 计算平均响应时间
    this.performance.averageResponseTime = (
      (this.performance.averageResponseTime * (this.performance.requestCount - 1) + responseTime) /
      this.performance.requestCount
    );
  }

  /**
   * @function getSystemStatus - 获取系统状态
   * @description 获取当前系统状态信息
   * @returns {Object} 系统状态对象
   */
  getSystemStatus() {
    return {
      isInitialized: this.isInitialized,
      connectionStatus: this.state.connectionStatus,
      modules: {
        security: this.securityManager?.isInitialized || false,
        api: this.apiManager?.isInitialized || false,
        analyzer: this.contentAnalyzer?.isInitialized || false,
        templates: this.templateManager?.isInitialized || false
      },
      performance: {
        ...this.performance,
        uptime: Date.now() - this.performance.startTime,
        errorRate: this.performance.requestCount > 0 ? 
          (this.performance.errorCount / this.performance.requestCount * 100).toFixed(2) + '%' : '0%'
      },
      stats: {
        chatSessions: this.state.chatHistory.size,
        activeAnalysis: this.state.activeAnalysis ? true : false,
        queueSize: this.messageQueue.length
      }
    };
  }

  /**
   * @function setupEventListeners - 设置事件监听
   * @description 设置内部事件监听器
   */
  setupEventListeners() {
    // 监听Chrome扩展事件
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleChromeMessage(message, sender, sendResponse);
      });
    }
  }

  /**
   * @function handleChromeMessage - 处理Chrome消息
   * @description 处理来自其他扩展组件的消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  async handleChromeMessage(message, sender, sendResponse) {
    try {
      const { type, data } = message;
      
      switch (type) {
        case 'CHAT_MESSAGE':
          const chatResult = await this.handleChatMessage(data.messageData, data.tabId);
          sendResponse(chatResult);
          break;
          
        case 'ANALYZE_CONTENT':
          const analysisResult = await this.analyzePageContent(data.pageData, data.options);
          sendResponse(analysisResult);
          break;
          
        case 'GENERATE_REPLY':
          const replyResult = await this.generateSmartReply(data.contextData, data.options);
          sendResponse(replyResult);
          break;
          
        case 'MANAGE_TEMPLATE':
          const templateResult = await this.manageTemplate(data.action, data.templateId, data.data);
          sendResponse(templateResult);
          break;
          
        case 'CONFIGURE_API':
          const configResult = await this.configureApiKey(data.provider, data.apiKey);
          sendResponse(configResult);
          break;
          
        case 'GET_STATUS':
          sendResponse(this.getSystemStatus());
          break;
          
        default:
          sendResponse({ success: false, error: '未知消息类型' });
      }
    } catch (error) {
      console.error('[AI核心] Chrome消息处理失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * @function startMessageQueueProcessor - 启动消息队列处理器
   * @description 启动消息队列处理，避免并发请求过多
   */
  startMessageQueueProcessor() {
    if (this.isProcessingQueue) return;
    
    this.isProcessingQueue = true;
    
    const processQueue = async () => {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift();
        try {
          await message.handler();
        } catch (error) {
          console.error('[AI核心] 队列消息处理失败:', error);
        }
        
        // 避免请求过于频繁
        await this.delay(50);
      }
      
      this.isProcessingQueue = false;
    };
    
    processQueue();
  }

  /**
   * @function delay - 延迟函数
   * @description 异步延迟指定毫秒数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise<void>}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * @function on - 添加事件监听器
   * @description 添加自定义事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * @function off - 移除事件监听器
   * @description 移除指定的事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * @function emit - 触发事件
   * @description 触发指定事件并调用所有监听器
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[AI核心] 事件监听器执行失败 (${event}):`, error);
        }
      });
    }
  }

  /**
   * @function cleanup - 清理核心模块
   * @description 清理所有资源和子模块
   */
  cleanup() {
    console.log('[AI核心] 开始清理核心模块...');
    
    // 清理子模块
    if (this.securityManager) {
      this.securityManager.cleanup();
    }
    
    if (this.apiManager) {
      this.apiManager.cleanup();
    }
    
    if (this.contentAnalyzer) {
      this.contentAnalyzer.cleanup();
    }
    
    if (this.templateManager) {
      this.templateManager.cleanup();
    }
    
    // 清理状态
    this.state.chatHistory.clear();
    this.eventListeners.clear();
    this.messageQueue.length = 0;
    
    this.isInitialized = false;
    this.state.connectionStatus = 'disconnected';
    
    console.log('[AI核心] 核心模块清理完成');
  }
}

// 创建全局实例
export const aiSidebarCore = new AiSidebarCore(); 