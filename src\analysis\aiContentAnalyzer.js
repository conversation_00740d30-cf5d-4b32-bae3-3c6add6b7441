/**
 * @file 智能内容分析引擎
 * @description 负责页面内容的智能分析、摘要生成和关键信息提取
 */

/**
 * @class AiContentAnalyzer - 智能内容分析器
 * @description 提供页面内容的深度分析功能，包括摘要、关键词、情感分析等
 */
export class AiContentAnalyzer {
  /**
   * @function constructor - 构造函数
   * @description 初始化内容分析器
   * @param {AiApiManager} apiManager - API管理器实例
   */
  constructor(apiManager) {
    this.apiManager = apiManager;
    this.isInitialized = false;
    
    // 分析配置常量
    this.ANALYSIS_TYPES = {
      SUMMARY: 'summary',
      KEYWORDS: 'keywords',
      SENTIMENT: 'sentiment',
      TOPICS: 'topics',
      ENTITIES: 'entities',
      LANGUAGE: 'language'
    };
    
    this.CONTENT_TYPES = {
      NEWS: 'news',
      BLOG: 'blog',
      DOCUMENT: 'document',
      PRODUCT: 'product',
      FORUM: 'forum',
      SOCIAL: 'social',
      ACADEMIC: 'academic',
      ECOMMERCE: 'ecommerce'
    };
    
    // 分析缓存
    this.analysisCache = new Map();
    this.cacheConfig = {
      maxSize: 100,
      ttl: 30 * 60 * 1000 // 30分钟
    };
    
    // 性能统计
    this.stats = {
      totalAnalyses: 0,
      successCount: 0,
      errorCount: 0,
      averageTime: 0
    };
  }

  /**
   * @function init - 初始化分析器
   * @description 设置分析器配置和依赖
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[内容分析] 初始化智能分析器...');
      
      if (!this.apiManager || !this.apiManager.isInitialized) {
        throw new Error('API管理器未正确初始化');
      }
      
      // 启动缓存清理
      this.startCacheCleanup();
      
      this.isInitialized = true;
      console.log('[内容分析] 智能分析器初始化完成');
    } catch (error) {
      console.error('[内容分析] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function analyzePageContent - 分析页面内容
   * @description 对页面内容进行全面分析
   * @param {Object} pageData - 页面数据
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 分析结果
   */
  async analyzePageContent(pageData, options = {}) {
    const startTime = Date.now();
    
    try {
      console.log('[内容分析] 开始分析页面内容...');
      
      const { textContent, pageInfo, metadata = {} } = pageData;
      const { includeKeywords = true, includeSentiment = false, deep = false } = options;
      
      // 检查缓存
      const cacheKey = this.generateCacheKey(pageData, options);
      const cachedResult = this.getFromCache(cacheKey);
      if (cachedResult) {
        console.log('[内容分析] 使用缓存结果');
        return cachedResult;
      }
      
      // 预处理内容
      const processedContent = this.preprocessContent(textContent);
      
      // 基础分析
      const basicAnalysis = await this.performBasicAnalysis(processedContent, pageInfo);
      
      // 高级分析（可选）
      let advancedAnalysis = {};
      if (deep) {
        advancedAnalysis = await this.performAdvancedAnalysis(processedContent, pageInfo);
      }
      
      // 构建完整分析结果
      const analysisResult = {
        success: true,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime,
        
        // 基础信息
        basic: {
          title: pageInfo.title || '无标题',
          url: pageInfo.url || '',
          domain: this.extractDomain(pageInfo.url),
          language: this.detectLanguage(textContent.mainContent),
          wordCount: this.countWords(textContent.mainContent),
          contentType: this.classifyContentType(textContent, pageInfo)
        },
        
        // 内容分析
        content: {
          summary: basicAnalysis.summary,
          keyPoints: basicAnalysis.keyPoints,
          readingTime: this.estimateReadingTime(textContent.mainContent)
        },
        
        // 结构分析
        structure: {
          headings: this.extractHeadings(textContent),
          images: textContent.images?.length || 0,
          links: textContent.links?.length || 0,
          forms: textContent.forms?.length || 0
        }
      };
      
      // 添加关键词分析
      if (includeKeywords) {
        analysisResult.keywords = await this.extractKeywords(processedContent);
      }
      
      // 添加情感分析
      if (includeSentiment) {
        analysisResult.sentiment = await this.analyzeSentiment(processedContent);
      }
      
      // 添加高级分析结果
      if (deep) {
        analysisResult.advanced = advancedAnalysis;
      }
      
      // 缓存结果
      this.saveToCache(cacheKey, analysisResult);
      
      // 更新统计
      this.updateStats(true, Date.now() - startTime);
      
      console.log('[内容分析] 页面内容分析完成');
      return analysisResult;
      
    } catch (error) {
      console.error('[内容分析] 页面内容分析失败:', error);
      this.updateStats(false, Date.now() - startTime);
      
      return {
        success: false,
        error: error.message,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * @function performBasicAnalysis - 执行基础分析
   * @description 使用AI进行基础的内容摘要和关键点提取
   * @param {Object} content - 处理后的内容
   * @param {Object} pageInfo - 页面信息
   * @returns {Promise<Object>} 基础分析结果
   */
  async performBasicAnalysis(content, pageInfo) {
    try {
      const response = await this.apiManager.analyzeContent({
        textContent: content,
        pageInfo: pageInfo
      });
      
      if (response.success) {
        return {
          summary: response.analysis.summary,
          keyPoints: response.analysis.keyPoints
        };
      } else {
        throw new Error(response.error || '基础分析失败');
      }
    } catch (error) {
      console.error('[内容分析] 基础分析失败:', error);
      
      // 回退到本地分析
      return this.fallbackBasicAnalysis(content, pageInfo);
    }
  }

  /**
   * @function performAdvancedAnalysis - 执行高级分析
   * @description 进行深度的内容分析，包括实体识别、主题分类等
   * @param {Object} content - 处理后的内容
   * @param {Object} pageInfo - 页面信息
   * @returns {Promise<Object>} 高级分析结果
   */
  async performAdvancedAnalysis(content, pageInfo) {
    try {
      const analysisPrompt = this.buildAdvancedAnalysisPrompt(content, pageInfo);
      
      const response = await this.apiManager.sendChatMessage({
        message: analysisPrompt,
        history: [],
        context: { type: 'advanced_analysis' }
      });
      
      if (response.success) {
        return this.parseAdvancedAnalysisResponse(response.reply);
      } else {
        throw new Error(response.error || '高级分析失败');
      }
    } catch (error) {
      console.error('[内容分析] 高级分析失败:', error);
      return {
        entities: [],
        topics: [],
        categories: [],
        complexity: 'unknown'
      };
    }
  }

  /**
   * @function extractKeywords - 提取关键词
   * @description 从内容中提取重要的关键词和短语
   * @param {Object} content - 处理后的内容
   * @returns {Promise<Array>} 关键词列表
   */
  async extractKeywords(content) {
    try {
      const keywordPrompt = this.buildKeywordExtractionPrompt(content);
      
      const response = await this.apiManager.sendChatMessage({
        message: keywordPrompt,
        history: [],
        context: { type: 'keyword_extraction' }
      });
      
      if (response.success) {
        return this.parseKeywordsResponse(response.reply);
      } else {
        throw new Error('关键词提取失败');
      }
    } catch (error) {
      console.error('[内容分析] 关键词提取失败:', error);
      
      // 回退到本地关键词提取
      return this.fallbackKeywordExtraction(content.mainContent);
    }
  }

  /**
   * @function analyzeSentiment - 分析情感倾向
   * @description 分析内容的情感倾向和语调
   * @param {Object} content - 处理后的内容
   * @returns {Promise<Object>} 情感分析结果
   */
  async analyzeSentiment(content) {
    try {
      const sentimentPrompt = this.buildSentimentAnalysisPrompt(content);
      
      const response = await this.apiManager.sendChatMessage({
        message: sentimentPrompt,
        history: [],
        context: { type: 'sentiment_analysis' }
      });
      
      if (response.success) {
        return this.parseSentimentResponse(response.reply);
      } else {
        throw new Error('情感分析失败');
      }
    } catch (error) {
      console.error('[内容分析] 情感分析失败:', error);
      return {
        overall: 'neutral',
        confidence: 0.5,
        emotions: [],
        tone: 'unknown'
      };
    }
  }

  /**
   * @function preprocessContent - 预处理内容
   * @description 清理和预处理页面内容
   * @param {Object} textContent - 原始文本内容
   * @returns {Object} 处理后的内容
   */
  preprocessContent(textContent) {
    const processed = {
      mainContent: '',
      headings: [],
      paragraphs: [],
      lists: [],
      metadata: {}
    };
    
    // 清理主要内容
    if (textContent.mainContent) {
      processed.mainContent = this.cleanText(textContent.mainContent);
      processed.paragraphs = this.extractParagraphs(processed.mainContent);
    }
    
    // 提取标题
    if (textContent.headings) {
      processed.headings = textContent.headings.map(h => ({
        level: h.level,
        text: this.cleanText(h.text)
      }));
    }
    
    // 提取列表
    if (textContent.lists) {
      processed.lists = textContent.lists.map(list => 
        list.map(item => this.cleanText(item))
      );
    }
    
    return processed;
  }

  /**
   * @function classifyContentType - 分类内容类型
   * @description 根据内容特征分类页面类型
   * @param {Object} textContent - 文本内容
   * @param {Object} pageInfo - 页面信息
   * @returns {string} 内容类型
   */
  classifyContentType(textContent, pageInfo) {
    const url = pageInfo.url || '';
    const title = pageInfo.title || '';
    const content = textContent.mainContent || '';
    
    // URL模式匹配
    const urlPatterns = {
      [this.CONTENT_TYPES.NEWS]: /news|article|story/i,
      [this.CONTENT_TYPES.BLOG]: /blog|post/i,
      [this.CONTENT_TYPES.PRODUCT]: /product|item|shop|store/i,
      [this.CONTENT_TYPES.FORUM]: /forum|discussion|thread/i,
      [this.CONTENT_TYPES.SOCIAL]: /social|facebook|twitter|linkedin/i,
      [this.CONTENT_TYPES.ACADEMIC]: /edu|academic|research|paper/i,
      [this.CONTENT_TYPES.ECOMMERCE]: /buy|price|cart|checkout/i
    };
    
    // 检查URL模式
    for (const [type, pattern] of Object.entries(urlPatterns)) {
      if (pattern.test(url)) {
        return type;
      }
    }
    
    // 内容特征匹配
    const contentLength = content.length;
    const hasPrice = /\$|\€|\£|¥|价格|售价/i.test(content);
    const hasDate = /\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}[-/]\d{1,2}[-/]\d{4}/i.test(content);
    const hasAuthor = /作者|author|by\s+/i.test(content);
    
    if (hasPrice) return this.CONTENT_TYPES.PRODUCT;
    if (hasDate && hasAuthor && contentLength > 500) return this.CONTENT_TYPES.NEWS;
    if (contentLength > 1000 && hasAuthor) return this.CONTENT_TYPES.BLOG;
    if (contentLength > 2000) return this.CONTENT_TYPES.DOCUMENT;
    
    return 'general';
  }

  /**
   * @function buildAdvancedAnalysisPrompt - 构建高级分析提示词
   * @description 为高级分析构建专门的提示词
   * @param {Object} content - 内容对象
   * @param {Object} pageInfo - 页面信息
   * @returns {string} 分析提示词
   */
  buildAdvancedAnalysisPrompt(content, pageInfo) {
    return `请对以下内容进行深度分析，并以JSON格式返回结果：

标题：${pageInfo.title || '无标题'}
内容：${content.mainContent?.substring(0, 2000) || '无内容'}...

请提供以下高级分析：
1. entities: 识别的实体（人名、地名、组织、产品等）
2. topics: 主要话题分类
3. categories: 内容类别标签
4. complexity: 内容复杂度（simple/medium/complex）
5. target_audience: 目标受众
6. actionable_items: 可操作项目或建议

请严格按照JSON格式返回：
{
  "entities": ["实体1", "实体2"],
  "topics": ["话题1", "话题2"],
  "categories": ["类别1", "类别2"],
  "complexity": "medium",
  "target_audience": "目标受众描述",
  "actionable_items": ["建议1", "建议2"]
}`;
  }

  /**
   * @function buildKeywordExtractionPrompt - 构建关键词提取提示词
   * @description 构建专门用于关键词提取的提示词
   * @param {Object} content - 内容对象
   * @returns {string} 关键词提取提示词
   */
  buildKeywordExtractionPrompt(content) {
    return `请从以下内容中提取最重要的5-10个关键词，按重要性排序：

内容：${content.mainContent?.substring(0, 1500) || '无内容'}

要求：
1. 关键词应该准确反映内容核心主题
2. 包含专业术语和重要概念
3. 避免过于常见的词汇
4. 以JSON数组格式返回

格式：["关键词1", "关键词2", "关键词3", ...]`;
  }

  /**
   * @function buildSentimentAnalysisPrompt - 构建情感分析提示词
   * @description 构建专门用于情感分析的提示词
   * @param {Object} content - 内容对象
   * @returns {string} 情感分析提示词
   */
  buildSentimentAnalysisPrompt(content) {
    return `请分析以下内容的情感倾向和语调：

内容：${content.mainContent?.substring(0, 1000) || '无内容'}

请提供以下分析，以JSON格式返回：
1. overall: 整体情感（positive/negative/neutral）
2. confidence: 置信度（0-1）
3. emotions: 检测到的情绪列表
4. tone: 语调特征（formal/casual/professional/emotional等）

格式：
{
  "overall": "neutral",
  "confidence": 0.8,
  "emotions": ["平静", "专业"],
  "tone": "professional"
}`;
  }

  /**
   * @function fallbackBasicAnalysis - 备用基础分析
   * @description 当AI分析失败时的本地分析方法
   * @param {Object} content - 内容对象
   * @param {Object} pageInfo - 页面信息
   * @returns {Object} 基础分析结果
   */
  fallbackBasicAnalysis(content, pageInfo) {
    const text = content.mainContent || '';
    const sentences = text.split(/[.!?。！？]+/).filter(s => s.trim().length > 10);
    
    // 生成简单摘要（取前几句）
    const summary = sentences.slice(0, 3).join('。') + '。';
    
    // 提取关键点（基于段落标题或列表）
    const keyPoints = [];
    if (content.headings?.length > 0) {
      keyPoints.push(...content.headings.slice(0, 5).map(h => h.text));
    }
    if (content.lists?.length > 0) {
      keyPoints.push(...content.lists[0]?.slice(0, 3) || []);
    }
    
    return {
      summary: summary || '无法生成摘要',
      keyPoints: keyPoints.length > 0 ? keyPoints : ['内容分析中...']
    };
  }

  /**
   * @function fallbackKeywordExtraction - 备用关键词提取
   * @description 本地关键词提取方法
   * @param {string} text - 文本内容
   * @returns {Array} 关键词列表
   */
  fallbackKeywordExtraction(text) {
    if (!text) return [];
    
    // 简单的词频分析
    const words = text.toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-z\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1);
    
    const wordCount = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });
    
    // 过滤常见词并排序
    const commonWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', 'the', 'a', 'an', 'and', 'or', 'but']);
    const keywords = Object.entries(wordCount)
      .filter(([word, count]) => !commonWords.has(word) && count > 1)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 8)
      .map(([word]) => word);
    
    return keywords;
  }

  /**
   * @function parseAdvancedAnalysisResponse - 解析高级分析响应
   * @description 解析AI返回的高级分析结果
   * @param {string} responseText - AI响应文本
   * @returns {Object} 解析后的高级分析结果
   */
  parseAdvancedAnalysisResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      return {
        entities: [],
        topics: [],
        categories: [],
        complexity: 'unknown',
        target_audience: '未知',
        actionable_items: []
      };
    } catch (error) {
      console.error('[内容分析] 高级分析结果解析失败:', error);
      return {
        entities: [],
        topics: [],
        categories: [],
        complexity: 'unknown',
        target_audience: '解析失败',
        actionable_items: []
      };
    }
  }

  /**
   * @function parseKeywordsResponse - 解析关键词响应
   * @description 解析AI返回的关键词列表
   * @param {string} responseText - AI响应文本
   * @returns {Array} 关键词数组
   */
  parseKeywordsResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const keywords = JSON.parse(jsonMatch[0]);
        return Array.isArray(keywords) ? keywords : [];
      }
      
      // 如果不是JSON格式，尝试按行分割
      const lines = responseText.split('\n')
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .filter(line => line.length > 0);
      
      return lines.slice(0, 10);
    } catch (error) {
      console.error('[内容分析] 关键词解析失败:', error);
      return [];
    }
  }

  /**
   * @function parseSentimentResponse - 解析情感分析响应
   * @description 解析AI返回的情感分析结果
   * @param {string} responseText - AI响应文本
   * @returns {Object} 情感分析结果
   */
  parseSentimentResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      return {
        overall: 'neutral',
        confidence: 0.5,
        emotions: [],
        tone: 'unknown'
      };
    } catch (error) {
      console.error('[内容分析] 情感分析解析失败:', error);
      return {
        overall: 'neutral',
        confidence: 0.5,
        emotions: [],
        tone: 'unknown'
      };
    }
  }

  /**
   * @function extractHeadings - 提取标题信息
   * @description 从内容中提取标题结构
   * @param {Object} textContent - 文本内容
   * @returns {Array} 标题列表
   */
  extractHeadings(textContent) {
    if (!textContent.headings) return [];
    
    return textContent.headings.map(heading => ({
      level: heading.level,
      text: heading.text,
      length: heading.text.length
    }));
  }

  /**
   * @function estimateReadingTime - 估算阅读时间
   * @description 根据内容长度估算阅读时间
   * @param {string} text - 文本内容
   * @returns {number} 阅读时间（分钟）
   */
  estimateReadingTime(text) {
    if (!text) return 0;
    
    const wordsPerMinute = 200; // 平均阅读速度
    const wordCount = this.countWords(text);
    
    return Math.max(1, Math.round(wordCount / wordsPerMinute));
  }

  /**
   * @function countWords - 统计字数
   * @description 统计文本的字数（中英文混合）
   * @param {string} text - 文本内容
   * @returns {number} 字数
   */
  countWords(text) {
    if (!text || typeof text !== 'string') return 0;
    
    // 中文字符
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    // 英文单词
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    
    return chineseChars + englishWords;
  }

  /**
   * @function detectLanguage - 检测语言
   * @description 检测文本的主要语言
   * @param {string} text - 文本内容
   * @returns {string} 语言标识
   */
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const totalChars = text.length;
    
    if (chineseChars / totalChars > 0.3) {
      return 'zh-CN';
    } else if (/[a-zA-Z]/.test(text)) {
      return 'en';
    }
    
    return 'unknown';
  }

  /**
   * @function extractDomain - 提取域名
   * @description 从URL中提取域名
   * @param {string} url - URL地址
   * @returns {string} 域名
   */
  extractDomain(url) {
    if (!url) return '';
    
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * @function extractParagraphs - 提取段落
   * @description 将文本分割为段落
   * @param {string} text - 文本内容
   * @returns {Array} 段落数组
   */
  extractParagraphs(text) {
    if (!text) return [];
    
    return text.split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 20);
  }

  /**
   * @function cleanText - 清理文本
   * @description 清理和标准化文本内容
   * @param {string} text - 原始文本
   * @returns {string} 清理后的文本
   */
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();
  }

  /**
   * @function generateCacheKey - 生成缓存键
   * @description 为分析结果生成唯一的缓存键
   * @param {Object} pageData - 页面数据
   * @param {Object} options - 分析选项
   * @returns {string} 缓存键
   */
  generateCacheKey(pageData, options) {
    const contentHash = this.simpleHash(
      (pageData.textContent?.mainContent || '') + 
      (pageData.pageInfo?.url || '')
    );
    const optionsHash = this.simpleHash(JSON.stringify(options));
    
    return `analysis_${contentHash}_${optionsHash}`;
  }

  /**
   * @function simpleHash - 简单哈希
   * @description 生成字符串的简单哈希值
   * @param {string} str - 输入字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * @function getFromCache - 从缓存获取
   * @description 从缓存中获取分析结果
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存的结果
   */
  getFromCache(key) {
    const cached = this.analysisCache.get(key);
    if (!cached) return null;
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
      this.analysisCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * @function saveToCache - 保存到缓存
   * @description 将分析结果保存到缓存
   * @param {string} key - 缓存键
   * @param {Object} data - 要缓存的数据
   */
  saveToCache(key, data) {
    // 检查缓存大小限制
    if (this.analysisCache.size >= this.cacheConfig.maxSize) {
      // 删除最旧的条目
      const oldestKey = this.analysisCache.keys().next().value;
      this.analysisCache.delete(oldestKey);
    }
    
    this.analysisCache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  /**
   * @function startCacheCleanup - 启动缓存清理
   * @description 定期清理过期的缓存条目
   */
  startCacheCleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, value] of this.analysisCache.entries()) {
        if (now - value.timestamp > this.cacheConfig.ttl) {
          this.analysisCache.delete(key);
        }
      }
    }, 10 * 60 * 1000); // 每10分钟清理一次
  }

  /**
   * @function updateStats - 更新统计信息
   * @description 更新分析器的性能统计
   * @param {boolean} success - 是否成功
   * @param {number} processingTime - 处理时间
   */
  updateStats(success, processingTime) {
    this.stats.totalAnalyses++;
    
    if (success) {
      this.stats.successCount++;
    } else {
      this.stats.errorCount++;
    }
    
    // 计算平均处理时间
    this.stats.averageTime = (
      (this.stats.averageTime * (this.stats.totalAnalyses - 1) + processingTime) /
      this.stats.totalAnalyses
    );
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取分析器的性能统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      cacheSize: this.analysisCache.size,
      successRate: this.stats.totalAnalyses > 0 ? 
        (this.stats.successCount / this.stats.totalAnalyses * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function cleanup - 清理分析器
   * @description 清理资源和缓存
   */
  cleanup() {
    this.analysisCache.clear();
    this.isInitialized = false;
    console.log('[内容分析] 智能分析器已清理');
  }
} 