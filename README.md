# AI侧边栏助手 - Chrome扩展

> 智能网页内容分析与AI对话助手，提供无缝的浏览器内AI交互体验

## 🚀 项目概览

AI侧边栏助手是一个基于Chrome Extension Manifest V3的智能扩展程序，为用户提供：

- **智能内容理解**: 实时分析网页内容，提取关键信息
- **AI对话交互**: 基于页面内容的智能对话和问答
- **高效工作流**: 智能回复建议、创作增强工具
- **无缝集成**: 与Notion等生产力工具深度集成

## ✨ 核心功能

### 🧠 智能分析引擎
- 自动解析网页内容结构
- 提取关键信息和数据
- 生成内容摘要和洞察
- 多维度分析报告

### 💬 AI对话中心
- 基于页面内容的智能问答
- 流式响应显示
- 多轮对话上下文保持
- 自定义AI模型支持

### ⚡ 快速操作面板
- 一键页面分析
- 智能回复生成
- 快捷操作按钮
- 状态实时监控

### 🔧 创作增强工具
- 多风格回复建议
- 模板管理系统
- 光标输入增强
- 个性化学习

### 🔗 深度集成
- Notion数据库同步
- 对话历史归档
- 知识库检索
- 云端配置同步

## 🛠️ 技术架构

### 核心技术栈
- **前端框架**: 原生JavaScript ES6+
- **UI样式**: CSS3 + BEM命名规范
- **扩展架构**: Chrome Extension Manifest V3
- **通信机制**: Message Passing API
- **存储方案**: Chrome Storage API

### 架构设计
```
├── Background Service Worker     # 后台服务工作器
│   ├── 消息路由中心
│   ├── AI接口管理  
│   ├── 存储管理
│   └── 侧边栏控制
│
├── Content Scripts              # 内容脚本
│   ├── 页面内容捕获
│   ├── DOM变化监听
│   └── 数据提取引擎
│
├── Sidebar Panel               # 侧边栏面板
│   ├── 对话交互界面
│   ├── 分析结果展示
│   └── 设置配置面板
│
└── Popup Control              # 弹窗控制中心
    ├── 快速操作面板
    ├── 状态监控显示
    └── 工具入口集合
```

## 📦 安装说明

### 开发环境安装
1. **克隆项目**
   ```bash
   git clone [repository-url]
   cd ai-sidebar-extension
   ```

2. **配置API密钥**
   ```bash
   # 复制配置模板
   cp config.example.js config.js
   
   # 编辑配置文件，替换API密钥
   # 访问 https://makersuite.google.com/app/apikey 获取Gemini API密钥
   ```

3. **加载扩展**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录

4. **验证安装**
   - 检查扩展图标是否出现在工具栏
   - 点击图标确认弹窗正常显示
   - 测试侧边栏开启功能

### 生产环境安装
从Chrome网上应用店安装（开发中）

## 🔧 开发指南

### 项目结构
```
ai-sidebar-extension/
├── manifest.json                 # 扩展配置文件
├── src/                         # 源代码目录
│   ├── background/              # 后台脚本
│   ├── content/                 # 内容脚本
│   └── ui/                      # 用户界面
│       ├── sidebar/             # 侧边栏界面
│       └── popup/               # 弹窗界面
├── assets/                      # 资源文件
│   ├── icons/                   # 图标资源
│   └── styles/                  # 全局样式
├── memory-bank/                 # 项目文档
└── development-tasks.md         # 开发任务
```

### 开发规范
- **代码注释**: 使用JSDoc标准注释
- **命名规范**: 参考 `memory-bank/naming-conventions.md`
- **样式规范**: 采用BEM命名方法
- **提交规范**: 详见开发文档

### 调试方法
1. **Background调试**: Chrome开发者工具 > Extensions > Service Worker
2. **Content调试**: 页面右键 > 检查 > Console
3. **Popup调试**: 右键扩展图标 > 检查弹出内容
4. **Sidebar调试**: 侧边栏右键 > 检查

## 📋 开发状态

### 当前进度
- ✅ 项目基础架构搭建
- ✅ Chrome扩展配置
- ✅ 侧边栏UI框架
- ✅ 弹窗控制中心
- ✅ 内容捕获引擎
- ⏳ AI接口集成
- ⏳ 智能分析功能
- ⏳ Notion集成

### 里程碑计划
- **MVP版本** (第8周): 基础功能完整实现
- **Beta版本** (第12周): 全功能集成测试
- **正式版本** (第14周): 发布到Chrome商店

## 🎯 使用场景

### 客服人员
- 快速理解客户问题页面
- 生成专业回复建议
- 提高响应效率

### 内容运营
- 分析页面内容质量
- 生成内容摘要
- 辅助内容创作

### 研发人员
- 技术文档快速理解
- 代码问题智能分析
- 开发效率提升

### 管理人员
- 快速获取页面洞察
- 决策支持信息提取
- 工作流程优化

## 🔒 安全隐私

### 数据安全
- 本地数据加密存储
- API密钥安全管理
- 最小权限原则

### 隐私保护
- 用户数据不上传
- 页面内容本地处理
- 可配置隐私设置

## 🤝 贡献指南

### 参与开发
1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 发起Pull Request

### 问题反馈
- 通过GitHub Issues报告问题
- 提供详细的错误信息和复现步骤
- 建议功能改进和优化

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- **项目地址**: [GitHub Repository]
- **文档地址**: [Documentation Site]
- **问题反馈**: [GitHub Issues]

---

**AI侧边栏助手** - 让AI成为您浏览器中的智能伙伴 🤖✨ 