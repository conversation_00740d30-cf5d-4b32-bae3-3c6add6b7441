/**
 * @file Notion集成连接器
 * @description 负责与Notion API的集成，实现对话历史归档、知识库检索等功能
 */

// 导入配置文件和缓存管理器
import { CONFIG } from '../../config.js';
import { AiNotionCacheManager } from './aiNotionCacheManager.js';

/**
 * @class AiNotionConnector - Notion集成连接器
 * @description 提供与Notion的完整集成功能
 */
export class AiNotionConnector {
  /**
   * @function constructor - 构造函数
   * @description 初始化Notion连接器
   * @param {AiSecurityManager} securityManager - 安全管理器实例
   */
  constructor(securityManager) {
    this.securityManager = securityManager;
    this.isInitialized = false;
    this.isConnected = false;
    
    // Notion API配置 - 使用config.js中的配置
    this.config = {
      // Integration Token 配置
      integration: {
        token: CONFIG.NOTION.INTEGRATION_TOKEN, // 从config.js加载
        storageKey: 'ai_sidebar_notion_integration_token',
        // 使用config.js中的预配置数据库ID
        databases: {
          chatHistory: CONFIG.NOTION.DATABASES.CHAT_HISTORY,
          knowledgeBase: CONFIG.NOTION.DATABASES.KNOWLEDGE_BASE
        }
      },

      // API 端点配置 - 使用config.js中的配置
      api: {
        baseUrl: CONFIG.NOTION.BASE_URL,
        version: CONFIG.NOTION.API_VERSION,
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      
      // 同步配置
      sync: {
        batchSize: 50,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        supportedTypes: ['chat', 'analysis', 'template'],
        autoSyncInterval: 15 * 60 * 1000, // 15分钟
        conflictResolution: 'client_wins' // 'client_wins', 'server_wins', 'merge'
      },
      
      // 预设数据库模板
      databaseTemplates: {
        chatHistory: {
          title: 'AI助手对话记录',
          icon: '💬',
          properties: {
            '标题': { type: 'title' },
            '时间': { type: 'date' },
            '页面URL': { type: 'url' },
            '对话内容': { type: 'rich_text' },
            '标签': { type: 'multi_select', options: ['工作', '学习', '客服', '分析'] }
          }
        },
        analysisResults: {
          title: 'AI分析结果',
          icon: '📊',
          properties: {
            '标题': { type: 'title' },
            '分析时间': { type: 'date' },
            '页面URL': { type: 'url' },
            '分析类型': { type: 'select', options: ['内容分析', '趋势分析', '对比分析'] },
            '结果摘要': { type: 'rich_text' },
            '置信度': { type: 'number', format: 'percent' }
          }
        },
        templates: {
          title: 'AI模板库',
          icon: '📋',
          properties: {
            '模板名称': { type: 'title' },
            '创建时间': { type: 'date' },
            '分类': { type: 'select', options: ['客服', '运营', '调度', '运维', '管理'] },
            '模板内容': { type: 'rich_text' },
            '使用次数': { type: 'number' }
          }
        }
      }
    };
    
    // 连接状态
    this.connectionState = {
      isConnected: false,
      isAuthenticated: false,
      accessToken: null,
      refreshToken: null,
      tokenExpiry: null,
      workspaceId: null,
      userId: null,
      lastSync: null,
      syncInProgress: false
    };
    
    // 数据库映射 - 使用预配置的数据库ID
    this.databaseMapping = {
      chatHistory: this.config.integration.databases.chatHistory,
      knowledgeBase: this.config.integration.databases.knowledgeBase,
      analysisResults: null, // 保留用于分析结果（如果需要）
      templates: null // 保留用于模板（如果需要）
    };

    // 连接状态 - 简化为Integration Token
    this.connectionState = {
      integrationToken: null,
      userId: null,
      workspaceId: null,
      connectedAt: null,
      botId: null
    };

    // 同步队列
    this.syncQueue = [];
    this.syncTimer = null;
    
    // 错误统计
    this.errorStats = {
      authErrors: 0,
      syncErrors: 0,
      apiErrors: 0,
      lastError: null
    };
    
    // 事件监听器
    this.eventListeners = new Map();

    // 初始化缓存管理器
    this.cacheManager = new AiNotionCacheManager();

    // 初始化
    this.init();
  }

  /**
   * @function getRedirectUri - 安全获取重定向URI
   * @description 安全地获取Chrome Identity API的重定向URI
   * @returns {string} 重定向URI
   */
  getRedirectUri() {
    try {
      if (typeof chrome !== 'undefined' && chrome.identity && chrome.identity.getRedirectURL) {
        return chrome.identity.getRedirectURL();
      } else {
        // 后备方案
        return `https://${chrome.runtime.id}.chromiumapp.org/`;
      }
    } catch (error) {
      console.error('[Notion集成] 获取重定向URI失败:', error);
      // 使用默认模式
      return `https://${chrome.runtime.id}.chromiumapp.org/`;
    }
  }

  /**
   * @function init - 初始化Notion连接器
   * @description 设置连接器配置和检查现有连接
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[Notion集成] 初始化Notion连接器...');
      
      if (!this.securityManager || !this.securityManager.isInitialized) {
        throw new Error('安全管理器未正确初始化');
      }
      
      // 加载保存的连接配置
      await this.loadConnectionConfig();
      
      // 验证现有连接
      if (this.connectionState.accessToken) {
        await this.validateConnection();
      }
      
      // 启动自动同步（如果已连接）
      if (this.isConnected && this.config.sync.autoSyncInterval) {
        this.startAutoSync();
      }
      
      this.isInitialized = true;
      console.log('[Notion集成] Notion连接器初始化完成');
    } catch (error) {
      console.error('[Notion集成] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function connectToNotion - 连接到Notion
   * @description 使用Integration Token连接到Notion（简化认证流程）
   * @param {string} integrationToken - 可选的Integration Token
   * @returns {Promise<Object>} 连接结果
   */
  async connectToNotion(integrationToken = null) {
    try {
      console.log('[Notion集成] 开始连接到Notion（使用Integration Token）...');

      // 获取Integration Token
      let token = integrationToken;
      if (!token) {
        // 尝试从存储中加载
        token = await this.loadIntegrationToken();
      }

      if (!token) {
        // 尝试从配置文件加载
        token = await this.loadTokenFromConfig();
      }

      if (!token) {
        throw new Error('未找到有效的Integration Token，请在配置文件中设置NOTION.INTEGRATION_TOKEN');
      }

      // 验证Token并获取用户信息
      console.log('[Notion集成] 验证Integration Token...');
      const userInfo = await this.validateIntegrationToken(token);

      if (!userInfo.success) {
        throw new Error(`Token验证失败: ${userInfo.error}`);
      }

      // 保存连接状态
      this.connectionState = {
        integrationToken: token,
        userId: userInfo.data.owner?.user?.id,
        workspaceId: userInfo.data.workspace?.id,
        connectedAt: Date.now(),
        botId: userInfo.data.bot?.id
      };

      // 保存Token到安全存储
      await this.saveIntegrationToken(token);

      // 初始化数据库连接
      await this.initializeDatabases();

      this.isConnected = true;

      console.log('[Notion集成] 连接成功');
      return {
        success: true,
        message: 'Notion连接成功',
        user: userInfo.data.owner?.user,
        workspace: userInfo.data.workspace,
        bot: userInfo.data.bot
      };

    } catch (error) {
      console.error('[Notion集成] 连接失败:', error);
      this.isConnected = false;
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function loadIntegrationToken - 从存储加载Integration Token
   * @description 从Chrome安全存储中加载Integration Token
   * @returns {Promise<string|null>} Integration Token或null
   */
  async loadIntegrationToken() {
    try {
      // 使用 chrome.storage.local 替代不存在的 chrome.storage.secure
      const result = await chrome.storage.local.get([this.config.integration.storageKey]);
      return result[this.config.integration.storageKey] || null;
    } catch (error) {
      console.warn('[Notion集成] 从存储加载Token失败:', error);
      return null;
    }
  }

  /**
   * @function saveIntegrationToken - 保存Integration Token到存储
   * @description 将Integration Token保存到Chrome安全存储
   * @param {string} token - Integration Token
   * @returns {Promise<boolean>} 保存是否成功
   */
  async saveIntegrationToken(token) {
    try {
      // 使用 chrome.storage.local 替代不存在的 chrome.storage.secure
      await chrome.storage.local.set({
        [this.config.integration.storageKey]: token
      });
      console.log('[Notion集成] Integration Token已保存到本地存储');
      return true;
    } catch (error) {
      console.error('[Notion集成] 保存Token失败:', error);
      return false;
    }
  }

  /**
   * @function loadTokenFromConfig - 从配置加载Token
   * @description 从预设配置中加载Integration Token（Service Worker兼容）
   * @returns {Promise<string|null>} Integration Token或null
   */
  async loadTokenFromConfig() {
    try {
      // 首先尝试从config.js加载Token
      const configToken = CONFIG.NOTION.INTEGRATION_TOKEN;

      if (configToken && configToken !== 'secret_YOUR_NOTION_INTEGRATION_TOKEN_HERE') {
        console.log('[Notion集成] 从config.js加载Token成功');
        return configToken;
      }

      // 如果config.js中没有有效Token，尝试从Chrome存储加载
      try {
        const result = await chrome.storage.sync.get(['notion_integration_token']);
        if (result.notion_integration_token) {
          console.log('[Notion集成] 从Chrome存储加载Token成功');
          return result.notion_integration_token;
        }
      } catch (storageError) {
        console.warn('[Notion集成] 从Chrome存储加载Token失败:', storageError);
      }

      console.warn('[Notion集成] 未找到有效的Integration Token');
      console.info('[Notion集成] 请在config.js中设置NOTION.INTEGRATION_TOKEN或通过Chrome存储设置');
      return null;
    } catch (error) {
      console.error('[Notion集成] 加载Token失败:', error);
      return null;
    }
  }

  /**
   * @function setIntegrationTokenFromStorage - 设置Integration Token到存储
   * @description 将Integration Token保存到Chrome存储（用于配置）
   * @param {string} token - Integration Token
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setIntegrationTokenFromStorage(token) {
    try {
      await chrome.storage.sync.set({ notion_integration_token: token });
      console.log('[Notion集成] Integration Token已保存到Chrome存储');
      return true;
    } catch (error) {
      console.error('[Notion集成] 保存Token到Chrome存储失败:', error);
      return false;
    }
  }

  /**
   * @function validateIntegrationToken - 验证Integration Token
   * @description 通过调用Notion API验证Token的有效性
   * @param {string} token - Integration Token
   * @returns {Promise<Object>} 验证结果
   */
  async validateIntegrationToken(token) {
    try {
      const response = await fetch(`${this.config.api.baseUrl}/users/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Notion-Version': this.config.api.version,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP ${response.status}: ${response.statusText}`
        };
      }

      const userData = await response.json();
      console.log('[Notion集成] Token验证成功');

      return {
        success: true,
        data: userData
      };
    } catch (error) {
      console.error('[Notion集成] Token验证失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function authenticate - 用户认证（已弃用，保留兼容性）
   * @description 原OAuth认证流程，现在重定向到Integration Token流程
   * @returns {Promise<Object>} 认证结果
   */
  async authenticate() {
    console.warn('[Notion集成] OAuth认证流程已弃用，自动重定向到Integration Token流程');

    // 重定向到新的Integration Token连接流程
    return await this.connectToNotion();
  }

  /**
   * @function syncChatHistory - 同步聊天历史
   * @description 将聊天历史同步到Notion数据库
   * @param {Array} chatHistory - 聊天历史数组
   * @param {Object} metadata - 元数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncChatHistory(chatHistory, metadata = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('Notion未连接');
      }
      
      console.log('[Notion集成] 同步聊天历史...');
      
      // 构建页面内容
      const pageContent = this.buildChatHistoryPage(chatHistory, metadata);
      
      // 创建或更新Notion页面
      const result = await this.createNotionPage(
        this.databaseMapping.chatHistory,
        pageContent
      );
      
      if (result.success) {
        console.log('[Notion集成] 聊天历史同步成功');
        this.errorStats.syncErrors = 0;
      }
      
      return result;
      
    } catch (error) {
      console.error('[Notion集成] 聊天历史同步失败:', error);
      this.errorStats.syncErrors++;
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function syncPageAnalysis - 同步页面分析结果
   * @description 将页面分析结果同步到Notion
   * @param {Object} analysisResult - 分析结果
   * @param {Object} pageInfo - 页面信息
   * @returns {Promise<Object>} 同步结果
   */
  async syncPageAnalysis(analysisResult, pageInfo) {
    try {
      if (!this.isConnected) {
        throw new Error('Notion未连接');
      }
      
      console.log('[Notion集成] 同步页面分析结果...');
      
      // 构建分析页面内容
      const pageContent = this.buildAnalysisPage(analysisResult, pageInfo);
      
      // 创建Notion页面
      const result = await this.createNotionPage(
        this.databaseMapping.analysisResults,
        pageContent
      );
      
      return result;
      
    } catch (error) {
      console.error('[Notion集成] 页面分析同步失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function searchKnowledgeBase - 搜索知识库
   * @description 在Notion知识库数据库中搜索相关内容
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  async searchKnowledgeBase(query, options = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('Notion未连接');
      }

      if (!this.databaseMapping.knowledgeBase) {
        throw new Error('知识库数据库未配置');
      }

      console.log('[Notion集成] 搜索知识库:', query);

      const {
        limit = 10,
        searchType = 'all', // 'title', 'content', 'all'
        useCache = true,
        sortBy = 'relevance' // 'relevance', 'lastEdited', 'created'
      } = options;

      // 优先使用缓存搜索
      if (useCache && this.cacheManager) {
        try {
          console.log('[Notion集成] 尝试从缓存搜索...');

          const cacheResults = await this.cacheManager.searchCache(query, {
            limit: limit,
            searchType: searchType,
            sortBy: sortBy
          });

          if (cacheResults.length > 0) {
            console.log(`[Notion集成] 缓存搜索完成，找到 ${cacheResults.length} 个结果`);

            return {
              success: true,
              results: cacheResults.map(page => ({
                id: page.id,
                title: page.title,
                url: page.url,
                created: new Date(page.createdTime).toISOString(),
                lastEdited: new Date(page.lastEdited).toISOString(),
                content: page.content,
                properties: page.properties,
                searchScore: page.searchScore,
                matchedFields: page.matchedFields,
                source: 'cache'
              })),
              total: cacheResults.length,
              query: query,
              searchType: searchType,
              source: 'cache'
            };
          } else {
            console.log('[Notion集成] 缓存中未找到结果，回退到API搜索');
          }
        } catch (cacheError) {
          console.warn('[Notion集成] 缓存搜索失败，回退到API搜索:', cacheError);
        }
      }

      // 回退到API搜索
      console.log('[Notion集成] 使用API搜索...');

      // 构建搜索请求
      let searchPayload;

      if (searchType === 'content') {
        // 内容搜索（通过属性）
        searchPayload = {
          filter: {
            property: 'Content',
            rich_text: {
              contains: query
            }
          },
          sorts: [
            {
              property: 'last_edited_time',
              direction: 'descending'
            }
          ],
          page_size: limit
        };
      } else if (searchType === 'title') {
        // 标题搜索
        searchPayload = {
          filter: {
            property: 'Name',
            title: {
              contains: query
            }
          },
          sorts: [
            {
              property: 'last_edited_time',
              direction: 'descending'
            }
          ],
          page_size: limit
        };
      } else {
        // 全搜索（默认）- 搜索标题
        searchPayload = {
          filter: {
            property: 'Name',
            title: {
              contains: query
            }
          },
          sorts: [
            {
              property: 'last_edited_time',
              direction: 'descending'
            }
          ],
          page_size: limit
        };
      }

      // 执行API搜索
      const response = await this.callNotionApi(
        `databases/${this.databaseMapping.knowledgeBase}/query`,
        'POST',
        searchPayload
      );

      if (response.success) {
        const results = response.data.results.map(page => ({
          id: page.id,
          title: this.extractPageTitle(page),
          url: page.url,
          created: page.created_time,
          lastEdited: page.last_edited_time,
          content: this.extractPageContent(page),
          properties: this.extractPageProperties(page),
          source: 'api'
        }));

        console.log(`[Notion集成] API搜索完成，找到 ${results.length} 个结果`);

        return {
          success: true,
          results: results,
          total: response.data.results.length,
          query: query,
          searchType: searchType,
          source: 'api'
        };
      }

      throw new Error(response.error || '搜索失败');

    } catch (error) {
      console.error('[Notion集成] 知识库搜索失败:', error);
      return {
        success: false,
        error: error.message,
        results: [],
        query: query
      };
    }
  }

  /**
   * @function buildAuthUrl - 构建认证URL
   * @description 构建Notion OAuth认证URL
   * @returns {string} 认证URL
   */
  buildAuthUrl() {
    const params = new URLSearchParams({
      client_id: this.config.oauth.clientId,
      response_type: 'code',
      owner: 'user',
      redirect_uri: this.config.oauth.redirectUri
    });
    
    return `https://api.notion.com/v1/oauth/authorize?${params.toString()}`;
  }

  /**
   * @function extractAuthCodeFromUrl - 从URL提取授权码
   * @description 从重定向URL中提取授权码
   * @param {string} url - 重定向URL
   * @returns {string|null} 授权码
   */
  extractAuthCodeFromUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get('code');
    } catch {
      return null;
    }
  }

  /**
   * @function exchangeCodeForToken - 交换访问令牌
   * @description 使用授权码交换访问令牌
   * @param {string} code - 授权码
   * @returns {Promise<Object>} 令牌数据
   */
  async exchangeCodeForToken(code) {
    const response = await fetch(this.config.oauth.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: this.config.oauth.redirectUri
      })
    });
    
    if (!response.ok) {
      throw new Error(`令牌交换失败: ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * @function getUserInfo - 获取用户信息
   * @description 获取当前用户的Notion信息
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo() {
    const response = await this.callNotionApi('users/me', 'GET');
    
    if (response.success) {
      return response.data;
    }
    
    throw new Error(response.error || '获取用户信息失败');
  }

  /**
   * @function initializeDatabases - 初始化数据库
   * @description 验证预配置的Notion数据库连接
   * @returns {Promise<void>}
   */
  async initializeDatabases() {
    try {
      console.log('[Notion集成] 初始化数据库连接...');

      // 验证对话数据库连接
      if (this.databaseMapping.chatHistory) {
        try {
          const chatDbInfo = await this.callNotionApi(`databases/${this.databaseMapping.chatHistory}`, 'GET');
          if (chatDbInfo.success) {
            console.log('[Notion集成] 对话数据库连接验证成功:', chatDbInfo.data.title?.[0]?.plain_text || '未知标题');
          } else {
            console.warn('[Notion集成] 对话数据库连接验证失败:', chatDbInfo.error);
          }
        } catch (error) {
          console.error('[Notion集成] 对话数据库验证错误:', error);
        }
      }

      // 验证知识库数据库连接
      if (this.databaseMapping.knowledgeBase) {
        try {
          const kbDbInfo = await this.callNotionApi(`databases/${this.databaseMapping.knowledgeBase}`, 'GET');
          if (kbDbInfo.success) {
            console.log('[Notion集成] 知识库数据库连接验证成功:', kbDbInfo.data.title?.[0]?.plain_text || '未知标题');
          } else {
            console.warn('[Notion集成] 知识库数据库连接验证失败:', kbDbInfo.error);
          }
        } catch (error) {
          console.error('[Notion集成] 知识库数据库验证错误:', error);
        }
      }

      console.log('[Notion集成] 数据库连接初始化完成');
      console.log('[Notion集成] 配置的数据库:', {
        chatHistory: this.databaseMapping.chatHistory,
        knowledgeBase: this.databaseMapping.knowledgeBase
      });

      // 启动知识库缓存更新
      if (this.databaseMapping.knowledgeBase && this.cacheManager) {
        try {
          console.log('[Notion集成] 启动知识库缓存更新...');

          // 异步更新缓存，不阻塞初始化流程
          this.cacheManager.updateCache(this).then(result => {
            if (result.success) {
              console.log(`[Notion集成] 知识库缓存更新完成: ${result.totalPages} 个页面`);
            } else {
              console.warn('[Notion集成] 知识库缓存更新失败:', result.error);
            }
          }).catch(error => {
            console.error('[Notion集成] 知识库缓存更新异常:', error);
          });
        } catch (error) {
          console.warn('[Notion集成] 启动缓存更新失败:', error);
        }
      }

    } catch (error) {
      console.error('[Notion集成] 数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function buildChatHistoryPage - 构建聊天历史页面
   * @description 将聊天历史转换为Notion页面格式
   * @param {Array} chatHistory - 聊天历史
   * @param {Object} metadata - 元数据
   * @returns {Object} 页面内容
   */
  buildChatHistoryPage(chatHistory, metadata) {
    const title = `聊天记录 - ${new Date().toLocaleString('zh-CN')}`;
    
    const content = {
      parent: { database_id: this.databaseMapping.chatHistory },
      properties: {
        '标题': {
          title: [{ text: { content: title } }]
        },
        '页面URL': {
          url: metadata.pageUrl || ''
        },
        '页面标题': {
          rich_text: [{ text: { content: metadata.pageTitle || '' } }]
        },
        '消息数量': {
          number: chatHistory.length
        },
        '创建时间': {
          date: { start: new Date().toISOString() }
        }
      },
      children: this.buildChatHistoryBlocks(chatHistory)
    };
    
    return content;
  }

  /**
   * @function buildChatHistoryBlocks - 构建聊天历史块
   * @description 将聊天消息转换为Notion块
   * @param {Array} chatHistory - 聊天历史
   * @returns {Array} Notion块数组
   */
  buildChatHistoryBlocks(chatHistory) {
    const blocks = [];
    
    chatHistory.forEach((message, index) => {
      // 添加消息分隔符
      if (index > 0) {
        blocks.push({
          object: 'block',
          type: 'divider',
          divider: {}
        });
      }
      
      // 添加消息内容
      const isUser = message.type === 'user';
      const prefix = isUser ? '👤 用户' : '🤖 AI助手';
      
      blocks.push({
        object: 'block',
        type: 'heading_3',
        heading_3: {
          rich_text: [{ text: { content: prefix } }]
        }
      });
      
      blocks.push({
        object: 'block',
        type: 'paragraph',
        paragraph: {
          rich_text: [{ text: { content: message.text } }]
        }
      });
      
      // 添加时间戳
      if (message.timestamp) {
        const timeStr = new Date(message.timestamp).toLocaleString('zh-CN');
        blocks.push({
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: [{
              text: { content: `时间: ${timeStr}` },
              annotations: { italic: true, color: 'gray' }
            }]
          }
        });
      }
    });
    
    return blocks;
  }

  /**
   * @function buildAnalysisPage - 构建分析页面
   * @description 将分析结果转换为Notion页面格式
   * @param {Object} analysisResult - 分析结果
   * @param {Object} pageInfo - 页面信息
   * @returns {Object} 页面内容
   */
  buildAnalysisPage(analysisResult, pageInfo) {
    const title = `页面分析 - ${pageInfo.title || '未知页面'}`;
    
    return {
      parent: { database_id: this.databaseMapping.analysisResults },
      properties: {
        '标题': {
          title: [{ text: { content: title } }]
        },
        'URL': {
          url: pageInfo.url || ''
        },
        '分析时间': {
          date: { start: new Date().toISOString() }
        },
        '字数': {
          number: analysisResult.basic?.wordCount || 0
        }
      },
      children: this.buildAnalysisBlocks(analysisResult)
    };
  }

  /**
   * @function buildAnalysisBlocks - 构建分析块
   * @description 将分析结果转换为Notion块
   * @param {Object} analysisResult - 分析结果
   * @returns {Array} Notion块数组
   */
  buildAnalysisBlocks(analysisResult) {
    const blocks = [];
    
    // 基础信息
    if (analysisResult.basic) {
      blocks.push({
        object: 'block',
        type: 'heading_2',
        heading_2: {
          rich_text: [{ text: { content: '📊 基础信息' } }]
        }
      });
      
      const basicInfo = [
        `语言: ${analysisResult.basic.language}`,
        `字数: ${analysisResult.basic.wordCount}`,
        `内容类型: ${analysisResult.basic.contentType}`,
        `阅读时间: ${analysisResult.content?.readingTime || 0} 分钟`
      ];
      
      basicInfo.forEach(info => {
        blocks.push({
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: [{ text: { content: info } }]
          }
        });
      });
    }
    
    // 内容摘要
    if (analysisResult.content?.summary) {
      blocks.push({
        object: 'block',
        type: 'heading_2',
        heading_2: {
          rich_text: [{ text: { content: '📝 内容摘要' } }]
        }
      });
      
      blocks.push({
        object: 'block',
        type: 'paragraph',
        paragraph: {
          rich_text: [{ text: { content: analysisResult.content.summary } }]
        }
      });
    }
    
    // 关键词
    if (analysisResult.keywords && analysisResult.keywords.length > 0) {
      blocks.push({
        object: 'block',
        type: 'heading_2',
        heading_2: {
          rich_text: [{ text: { content: '🔑 关键词' } }]
        }
      });
      
      const keywordText = analysisResult.keywords.join(', ');
      blocks.push({
        object: 'block',
        type: 'paragraph',
        paragraph: {
          rich_text: [{ text: { content: keywordText } }]
        }
      });
    }
    
    return blocks;
  }

  /**
   * @function callNotionApi - 调用Notion API
   * @description 统一的Notion API调用方法
   * @param {string} endpoint - API端点
   * @param {string} method - HTTP方法
   * @param {Object} data - 请求数据
   * @returns {Promise<Object>} API响应
   */
  async callNotionApi(endpoint, method = 'GET', data = null) {
    try {
      // 检查Integration Token是否可用
      if (!this.connectionState.integrationToken) {
        throw new Error('Integration Token未设置，请先连接到Notion');
      }

      const url = `${this.config.api.baseUrl}/${endpoint}`;
      const options = {
        method: method,
        headers: {
          'Authorization': `Bearer ${this.connectionState.integrationToken}`,
          'Notion-Version': this.config.api.version,
          'Content-Type': 'application/json'
        }
      };

      if (data && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);

      let responseData;
      try {
        responseData = await response.json();
      } catch (parseError) {
        responseData = { message: 'Failed to parse response JSON' };
      }

      if (!response.ok) {
        throw new Error(responseData.message || `API调用失败: ${response.status} ${response.statusText}`);
      }

      return {
        success: true,
        data: responseData
      };

    } catch (error) {
      console.error('[Notion集成] API调用失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * @function createNotionPage - 创建Notion页面
   * @description 在指定数据库中创建新页面
   * @param {string} databaseId - 数据库ID
   * @param {Object} pageContent - 页面内容
   * @returns {Promise<Object>} 创建结果
   */
  async createNotionPage(databaseId, pageContent) {
    // 确保页面内容包含数据库ID
    const pageData = {
      ...pageContent,
      parent: {
        database_id: databaseId
      }
    };

    return await this.callNotionApi('pages', 'POST', pageData);
  }

  /**
   * @function getChatHistorySchema - 获取聊天历史数据库模式
   * @description 定义聊天历史数据库的属性模式
   * @returns {Object} 数据库模式
   */
  getChatHistorySchema() {
    return {
      '标题': { title: {} },
      '页面URL': { url: {} },
      '页面标题': { rich_text: {} },
      '消息数量': { number: {} },
      '创建时间': { date: {} }
    };
  }

  /**
   * @function getPageAnalysisSchema - 获取页面分析数据库模式
   * @description 定义页面分析数据库的属性模式
   * @returns {Object} 数据库模式
   */
  getPageAnalysisSchema() {
    return {
      '标题': { title: {} },
      'URL': { url: {} },
      '分析时间': { date: {} },
      '字数': { number: {} }
    };
  }

  /**
   * @function validateConnection - 验证连接
   * @description 验证当前Notion连接是否有效
   * @returns {Promise<boolean>} 连接是否有效
   */
  async validateConnection() {
    try {
      const response = await this.callNotionApi('users/me', 'GET');
      this.isConnected = response.success;
      return this.isConnected;
    } catch (error) {
      this.isConnected = false;
      return false;
    }
  }

  /**
   * @function isTokenExpired - 检查令牌是否过期
   * @description 检查访问令牌是否即将过期
   * @returns {boolean} 是否过期
   */
  isTokenExpired() {
    if (!this.connectionState.expiresAt) return true;
    // 提前5分钟刷新令牌
    return Date.now() >= (this.connectionState.expiresAt - 5 * 60 * 1000);
  }

  /**
   * @function refreshAccessToken - 刷新访问令牌
   * @description 使用刷新令牌获取新的访问令牌
   * @returns {Promise<void>}
   */
  async refreshAccessToken() {
    if (!this.connectionState.refreshToken) {
      throw new Error('没有可用的刷新令牌');
    }
    
    // Notion API目前不支持刷新令牌，需要重新认证
    console.warn('[Notion集成] 访问令牌过期，需要重新认证');
    this.isConnected = false;
  }

  /**
   * @function loadConnectionConfig - 加载连接配置
   * @description 从安全存储加载连接配置
   * @returns {Promise<void>}
   */
  async loadConnectionConfig() {
    try {
      const config = await this.securityManager.getSecureData('notion_connection');
      if (config) {
        this.connectionState = config;
      }
    } catch (error) {
      console.error('[Notion集成] 加载连接配置失败:', error);
    }
  }

  /**
   * @function saveConnectionConfig - 保存连接配置
   * @description 安全保存连接配置
   * @returns {Promise<void>}
   */
  async saveConnectionConfig() {
    try {
      await this.securityManager.storeSecureData('notion_connection', this.connectionState);
    } catch (error) {
      console.error('[Notion集成] 保存连接配置失败:', error);
    }
  }

  /**
   * @function startAutoSync - 启动自动同步
   * @description 启动定期自动同步功能
   */
  startAutoSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    this.syncTimer = setInterval(() => {
      if (this.isConnected && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    }, this.config.sync.autoSyncInterval);
    
    console.log('[Notion集成] 自动同步已启动');
  }

  /**
   * @function processSyncQueue - 处理同步队列
   * @description 处理待同步的数据队列
   * @returns {Promise<void>}
   */
  async processSyncQueue() {
    if (this.connectionState.syncInProgress || this.syncQueue.length === 0) {
      return;
    }
    
    this.connectionState.syncInProgress = true;
    
    try {
      const batch = this.syncQueue.splice(0, this.config.sync.batchSize);
      
      for (const item of batch) {
        try {
          if (item.type === 'chat') {
            await this.syncChatHistory(item.data, item.metadata);
          } else if (item.type === 'analysis') {
            await this.syncPageAnalysis(item.data, item.metadata);
          }
        } catch (error) {
          console.error('[Notion集成] 同步项目失败:', error);
        }
      }
      
      this.connectionState.lastSync = Date.now();
    } finally {
      this.connectionState.syncInProgress = false;
    }
  }

  /**
   * @function disconnect - 断开连接
   * @description 断开Notion连接并清理数据
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      // 清理连接状态
      this.connectionState = {
        accessToken: null,
        refreshToken: null,
        expiresAt: null,
        userId: null,
        workspaceId: null
      };
      
      // 清理存储的配置
      await this.securityManager.removeSecureData('notion_connection');
      
      // 停止自动同步
      if (this.syncTimer) {
        clearInterval(this.syncTimer);
        this.syncTimer = null;
      }
      
      this.isConnected = false;
      console.log('[Notion集成] 已断开Notion连接');
    } catch (error) {
      console.error('[Notion集成] 断开连接失败:', error);
    }
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取Notion集成的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.errorStats,
      isConnected: this.isConnected,
      queueSize: this.syncQueue.length,
      successRate: this.errorStats.syncErrors > 0 ? 
        ((this.errorStats.syncErrors - this.errorStats.apiErrors) / this.errorStats.syncErrors * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function extractPageTitle - 提取页面标题
   * @description 从Notion页面对象中提取标题
   * @param {Object} page - Notion页面对象
   * @returns {string} 页面标题
   */
  extractPageTitle(page) {
    try {
      // 尝试从不同的属性中提取标题
      if (page.properties) {
        // 查找标题类型的属性
        for (const [key, property] of Object.entries(page.properties)) {
          if (property.type === 'title' && property.title && property.title.length > 0) {
            return property.title[0].plain_text || property.title[0].text?.content || '';
          }
        }

        // 查找Name属性（常见的标题属性名）
        if (page.properties.Name && page.properties.Name.title) {
          return page.properties.Name.title[0]?.plain_text || '';
        }

        // 查找其他可能的标题属性
        const titleKeys = ['标题', 'Title', '名称'];
        for (const key of titleKeys) {
          if (page.properties[key] && page.properties[key].title) {
            return page.properties[key].title[0]?.plain_text || '';
          }
        }
      }

      // 如果没有找到标题，返回默认值
      return '无标题';
    } catch (error) {
      console.error('[Notion集成] 提取页面标题失败:', error);
      return '无标题';
    }
  }

  /**
   * @function extractPageContent - 提取页面内容
   * @description 从Notion页面对象中提取内容摘要
   * @param {Object} page - Notion页面对象
   * @returns {string} 页面内容摘要
   */
  extractPageContent(page) {
    try {
      let content = '';

      // 从属性中提取富文本内容
      if (page.properties) {
        for (const [key, property] of Object.entries(page.properties)) {
          if (property.type === 'rich_text' && property.rich_text && property.rich_text.length > 0) {
            const richTextContent = property.rich_text
              .map(text => text.plain_text || text.text?.content || '')
              .join(' ');
            if (richTextContent.trim()) {
              content += richTextContent + ' ';
            }
          }
        }
      }

      // 如果没有内容，返回默认提示
      return content.trim() || '暂无内容预览';
    } catch (error) {
      console.error('[Notion集成] 提取页面内容失败:', error);
      return '内容提取失败';
    }
  }

  /**
   * @function extractPageProperties - 提取页面属性
   * @description 从Notion页面对象中提取所有属性
   * @param {Object} page - Notion页面对象
   * @returns {Object} 页面属性对象
   */
  extractPageProperties(page) {
    try {
      const properties = {};

      if (page.properties) {
        for (const [key, property] of Object.entries(page.properties)) {
          switch (property.type) {
            case 'title':
              properties[key] = property.title?.[0]?.plain_text || '';
              break;
            case 'rich_text':
              properties[key] = property.rich_text?.map(text => text.plain_text).join(' ') || '';
              break;
            case 'number':
              properties[key] = property.number || 0;
              break;
            case 'select':
              properties[key] = property.select?.name || '';
              break;
            case 'multi_select':
              properties[key] = property.multi_select?.map(item => item.name) || [];
              break;
            case 'date':
              properties[key] = property.date?.start || '';
              break;
            case 'url':
              properties[key] = property.url || '';
              break;
            case 'email':
              properties[key] = property.email || '';
              break;
            case 'phone_number':
              properties[key] = property.phone_number || '';
              break;
            case 'checkbox':
              properties[key] = property.checkbox || false;
              break;
            default:
              properties[key] = property;
          }
        }
      }

      return properties;
    } catch (error) {
      console.error('[Notion集成] 提取页面属性失败:', error);
      return {};
    }
  }

  /**
   * @function cleanup - 清理连接器
   * @description 清理资源和连接
   */
  cleanup() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncQueue.length = 0;
    this.isInitialized = false;
    this.isConnected = false;

    console.log('[Notion集成] Notion连接器已清理');
  }
}