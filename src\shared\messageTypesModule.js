/**
 * @file messageTypesModule.js
 * @description ES6模块版本的消息类型常量，专门用于Service Worker等支持ES6模块的环境
 * @version 1.0.0
 * @since 2024-12-19
 */

// 使用 Object.freeze 保证枚举不可变
const MESSAGE_TYPES = Object.freeze({
  // 内容捕获
  CONTENT_CAPTURED: 'ai:content:captured',
  CONTENT_SUMMARIZE: 'ai:content:summarize',
  CONTENT_ANALYZE: 'ai:content:analyze',

  // 聊天
  CHAT_SEND: 'ai:chat:send',
  CHAT_STREAM: 'ai:chat:stream',

  // 智能回复
  SMART_REPLY_GENERATE: 'ai:smart-reply:generate',

  // 页面分析
  ANALYSIS_REQUEST: 'ai:analysis:request',
  ANALYSIS_COMPARE: 'ai:analysis:compare',
  ANALYSIS_TREND: 'ai:analysis:trend',

  // 设置
  SETTINGS_GET: 'ai:settings:get',
  SETTINGS_SET: 'ai:settings:set',
  SETTINGS_RESET: 'ai:settings:reset',
  SETTINGS_EXPORT: 'ai:settings:export',
  SETTINGS_IMPORT: 'ai:settings:import',

  // 旧版配置 (兼容)
  CONFIG_GET: 'ai:config:get',
  CONFIG_SET: 'ai:config:set',

  // Notion
  NOTION_CONNECT: 'ai:notion:connect',
  NOTION_SYNC: 'ai:notion:sync',
  NOTION_CACHE_CLEAR: 'ai:notion:cache:clear',
  NOTION_CACHE_STATUS: 'ai:notion:cache:status',
  NOTION_DATABASES_GET: 'ai:notion:databases:get',

  // 光标增强
  CURSOR_ENHANCE_TOGGLE: 'ai:cursor:enhance:toggle',
  CURSOR_ENHANCE_STATUS: 'ai:cursor:enhance:status',

  // 模板
  TEMPLATE_GENERATE_REPLY: 'ai:template:generate-reply',
  TEMPLATE_SUGGEST: 'ai:template:suggest',
  TEMPLATE_USE: 'ai:template:use',

  // 工作流
  WORKFLOW_EXECUTE: 'ai:workflow:execute',

  // 国际化
  I18N_TRANSLATE: 'ai:i18n:translate',

  // 协作
  COLLABORATION_SHARE: 'ai:collaboration:share',

  // 通用
  SIDEBAR_TOGGLE: 'ai:sidebar:toggle',
  SIDEBAR_STATUS: 'ai:sidebar:status',
  NOTIFICATION_CREATE: 'ai:notification:create',
  API_CONFIGURE: 'ai:api:configure',
  STATUS_REQUEST: 'ai:status:request',
  STATUS_GET: 'ai:status:get',

  // 测试
  TEST_NOTIFICATION: 'ai:test:notification',
  TEST_ADVANCED_FALLBACK: 'ai:test:advanced-fallback',

  // 从 Background -> UI 的消息
  ANALYSIS_RESULT: 'ai:analysis:result',
  SETTINGS_UPDATE: 'ai:settings:update',
  NOTION_STATUS: 'ai:notion:status',
  NOTIFICATION_SHOW: 'ai:notification:show'
});

// ES6模块导出
export { MESSAGE_TYPES };
