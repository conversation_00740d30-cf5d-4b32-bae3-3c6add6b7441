/**
 * @file AI侧边栏模块加载器
 * @description 实现模块的懒加载，提高启动性能和内存使用效率
 */

/**
 * @class AiModuleLoader
 * @description 模块懒加载器，负责按需加载非关键模块
 */
class AiModuleLoader {
  /**
   * @function constructor - 构造函数
   * @description 初始化模块加载器
   */
  constructor() {
    // 加载器配置
    this.config = {
      // 加载超时时间
      loadTimeout: 10000, // 10秒
      
      // 重试配置
      maxRetries: 3,
      retryDelay: 1000, // 1秒
      
      // 预加载配置
      preloadDelay: 2000, // 2秒后开始预加载
      preloadEnabled: true,
      
      // 缓存配置
      cacheModules: true,
      maxCacheAge: 30 * 60 * 1000 // 30分钟
    };
    
    // 模块状态
    this.moduleStates = new Map();
    this.loadedModules = new Map();
    this.loadingPromises = new Map();
    this.preloadQueue = [];
    
    // 模块定义
    this.moduleDefinitions = new Map([
      ['notion', {
        path: '../integrations/aiNotionConnector.js',
        className: 'AiNotionConnector',
        dependencies: ['security'],
        priority: 'low',
        preload: false,
        description: 'Notion集成模块'
      }],
      ['analyzer', {
        path: '../analysis/aiAdvancedAnalyzer.js',
        className: 'AiAdvancedAnalyzer',
        dependencies: ['api'],
        priority: 'medium',
        preload: true,
        description: '高级分析器模块'
      }],
      ['cursor', {
        path: '../ui/aiCursorEnhancer.js',
        className: 'AiCursorEnhancer',
        dependencies: [],
        priority: 'low',
        preload: false,
        description: '光标增强器模块'
      }],
      ['workflow', {
        path: '../automation/aiWorkflowEngine.js',
        className: 'AiWorkflowEngine',
        dependencies: ['api'],
        priority: 'low',
        preload: false,
        description: 'AI工作流引擎'
      }],


    ]);
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 性能统计
    this.stats = {
      totalLoads: 0,
      successfulLoads: 0,
      failedLoads: 0,
      cacheHits: 0,
      averageLoadTime: 0,
      totalLoadTime: 0
    };
    
    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化模块加载器
   * @description 初始化加载器并开始预加载
   */
  async init() {
    console.log('[模块加载] 初始化模块加载器...');
    
    // 初始化所有模块状态
    for (const [name, definition] of this.moduleDefinitions) {
      this.moduleStates.set(name, {
        status: 'unloaded',
        loadTime: null,
        lastUsed: null,
        error: null,
        retryCount: 0
      });
    }
    
    // 开始预加载
    if (this.config.preloadEnabled) {
      setTimeout(() => {
        this.startPreloading();
      }, this.config.preloadDelay);
    }
    
    console.log('[模块加载] 模块加载器初始化完成');
    this.emit('initialized', { moduleCount: this.moduleDefinitions.size });
  }

  /**
   * @function loadModule - 加载模块
   * @description 懒加载指定的模块
   * @param {string} moduleName - 模块名称
   * @param {Object} options - 加载选项
   * @returns {Promise<Object>} 加载的模块实例
   */
  async loadModule(moduleName, options = {}) {
    const {
      force = false,
      timeout = this.config.loadTimeout,
      showProgress = true
    } = options;
    
    // 检查模块定义
    if (!this.moduleDefinitions.has(moduleName)) {
      throw new Error(`未知模块: ${moduleName}`);
    }
    
    // 检查是否已加载
    if (!force && this.loadedModules.has(moduleName)) {
      this.stats.cacheHits++;
      console.log(`[模块加载] 🎯 使用缓存模块: ${moduleName}`);
      return this.loadedModules.get(moduleName);
    }
    
    // 检查是否正在加载
    if (this.loadingPromises.has(moduleName)) {
      console.log(`[模块加载] ⏳ 等待模块加载: ${moduleName}`);
      return await this.loadingPromises.get(moduleName);
    }
    
    // 开始加载
    const loadingPromise = this.performModuleLoad(moduleName, { timeout, showProgress });
    this.loadingPromises.set(moduleName, loadingPromise);
    
    try {
      const module = await loadingPromise;
      this.loadingPromises.delete(moduleName);
      return module;
    } catch (error) {
      this.loadingPromises.delete(moduleName);
      throw error;
    }
  }

  /**
   * @function performModuleLoad - 执行模块加载
   * @description 执行实际的模块加载过程
   * @param {string} moduleName - 模块名称
   * @param {Object} options - 加载选项
   * @returns {Promise<Object>} 加载的模块实例
   */
  async performModuleLoad(moduleName, options = {}) {
    const { timeout, showProgress } = options;
    const definition = this.moduleDefinitions.get(moduleName);
    const state = this.moduleStates.get(moduleName);
    
    const startTime = Date.now();
    this.stats.totalLoads++;
    
    try {
      console.log(`[模块加载] 🚀 开始加载模块: ${moduleName} (${definition.description})`);
      
      // 更新状态
      state.status = 'loading';
      this.emit('moduleLoadStart', { moduleName, definition });
      
      // 显示加载进度
      if (showProgress) {
        this.showLoadingIndicator(moduleName, definition.description);
      }
      
      // 检查依赖
      await this.loadDependencies(definition.dependencies);
      
      // 加载模块文件
      const ModuleClass = await this.loadModuleFile(definition.path, definition.className, timeout);
      
      // 创建模块实例
      const moduleInstance = await this.createModuleInstance(ModuleClass, moduleName, definition);
      
      // 缓存模块
      if (this.config.cacheModules) {
        this.loadedModules.set(moduleName, moduleInstance);
      }
      
      // 更新状态和统计
      const loadTime = Date.now() - startTime;
      state.status = 'loaded';
      state.loadTime = loadTime;
      state.lastUsed = Date.now(); // 记录首次使用时间
      state.error = null;
      state.retryCount = 0;
      
      this.stats.successfulLoads++;
      this.stats.totalLoadTime += loadTime;
      this.stats.averageLoadTime = this.stats.totalLoadTime / this.stats.successfulLoads;
      
      // 隐藏加载指示器
      if (showProgress) {
        this.hideLoadingIndicator(moduleName);
      }
      
      console.log(`[模块加载] ✅ 模块加载成功: ${moduleName} (${loadTime}ms)`);
      this.emit('moduleLoadSuccess', { moduleName, loadTime, moduleInstance });
      
      return moduleInstance;
      
    } catch (error) {
      const loadTime = Date.now() - startTime;
      
      // 更新状态
      state.status = 'error';
      state.error = error.message;
      state.retryCount++;
      
      this.stats.failedLoads++;
      
      // 隐藏加载指示器
      if (showProgress) {
        this.hideLoadingIndicator(moduleName);
      }
      
      console.error(`[模块加载] ❌ 模块加载失败: ${moduleName} (${loadTime}ms)`, error);
      this.emit('moduleLoadError', { moduleName, error, loadTime });
      
      // 检查是否需要重试
      if (state.retryCount < this.config.maxRetries) {
        console.log(`[模块加载] 🔄 准备重试加载: ${moduleName} (第${state.retryCount}次)`);
        await this.sleep(this.config.retryDelay);
        return await this.performModuleLoad(moduleName, options);
      }
      
      throw new Error(`模块加载失败: ${moduleName} - ${error.message}`);
    }
  }

  /**
   * @function loadModuleFile - 加载模块文件
   * @description 动态导入模块文件
   * @param {string} path - 模块路径
   * @param {string} className - 类名
   * @param {number} timeout - 超时时间
   * @returns {Promise<Function>} 模块类
   */
  async loadModuleFile(path, className, timeout) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`模块加载超时: ${path}`));
      }, timeout);
      
      try {
        // 检查是否在Service Worker环境
        if (typeof importScripts !== 'undefined') {
          // Service Worker环境，使用importScripts
          importScripts(path);
          const ModuleClass = self[className];
          if (!ModuleClass) {
            throw new Error(`模块类未找到: ${className}`);
          }
          clearTimeout(timeoutId);
          resolve(ModuleClass);
        } else {
          // 普通环境，使用动态import
          const module = await import(path);
          const ModuleClass = module[className];
          if (!ModuleClass) {
            throw new Error(`模块类未找到: ${className}`);
          }
          clearTimeout(timeoutId);
          resolve(ModuleClass);
        }
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * @function createModuleInstance - 创建模块实例
   * @description 创建并初始化模块实例
   * @param {Function} ModuleClass - 模块类
   * @param {string} moduleName - 模块名称
   * @param {Object} definition - 模块定义
   * @returns {Promise<Object>} 模块实例
   */
  async createModuleInstance(ModuleClass, moduleName, definition) {
    try {
      // 获取依赖实例
      const dependencies = await this.getDependencyInstances(definition.dependencies);
      
      // 创建实例
      const instance = new ModuleClass(...dependencies);
      
      // 初始化模块（如果有init方法）
      if (typeof instance.init === 'function') {
        await instance.init();
      }
      
      return instance;
    } catch (error) {
      throw new Error(`模块实例创建失败: ${moduleName} - ${error.message}`);
    }
  }

  /**
   * @function loadDependencies - 加载依赖
   * @description 加载模块的依赖项
   * @param {Array} dependencies - 依赖列表
   * @returns {Promise<void>}
   */
  async loadDependencies(dependencies) {
    if (!dependencies || dependencies.length === 0) {
      return;
    }
    
    console.log(`[模块加载] 📦 加载依赖: ${dependencies.join(', ')}`);
    
    const loadPromises = dependencies.map(dep => {
      if (this.moduleDefinitions.has(dep)) {
        return this.loadModule(dep, { showProgress: false });
      } else {
        // 外部依赖，假设已经加载
        console.warn(`[模块加载] ⚠️ 外部依赖: ${dep}`);
        return Promise.resolve();
      }
    });
    
    await Promise.all(loadPromises);
  }

  /**
   * @function getDependencyInstances - 获取依赖实例
   * @description 获取依赖模块的实例
   * @param {Array} dependencies - 依赖列表
   * @returns {Promise<Array>} 依赖实例数组
   */
  async getDependencyInstances(dependencies) {
    if (!dependencies || dependencies.length === 0) {
      return [];
    }
    
    const instances = [];
    
    for (const dep of dependencies) {
      if (this.loadedModules.has(dep)) {
        instances.push(this.loadedModules.get(dep));
      } else {
        // 尝试从全局获取（如核心模块）
        const globalInstance = this.getGlobalInstance(dep);
        if (globalInstance) {
          instances.push(globalInstance);
        } else {
          console.warn(`[模块加载] ⚠️ 依赖实例未找到: ${dep}`);
        }
      }
    }
    
    return instances;
  }

  /**
   * @function getGlobalInstance - 获取全局实例
   * @description 从全局作用域获取核心模块实例
   * @param {string} moduleName - 模块名称
   * @returns {Object|null} 模块实例或null
   */
  getGlobalInstance(moduleName) {
    // 这里需要根据实际的全局实例映射
    const globalInstances = {
      'api': window.apiManagerInstance,
      'security': window.securityManagerInstance,
      'settings': window.settingsManagerInstance
    };
    
    return globalInstances[moduleName] || null;
  }

  /**
   * @function startPreloading - 开始预加载
   * @description 开始预加载高优先级模块
   */
  async startPreloading() {
    console.log('[模块加载] 🔄 开始预加载模块...');
    
    // 获取需要预加载的模块
    const preloadModules = Array.from(this.moduleDefinitions.entries())
      .filter(([name, def]) => def.preload)
      .sort(([, a], [, b]) => this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority));
    
    // 逐个预加载
    for (const [moduleName, definition] of preloadModules) {
      try {
        console.log(`[模块加载] 📦 预加载模块: ${moduleName}`);
        await this.loadModule(moduleName, { showProgress: false });
        
        // 短暂延迟，避免阻塞主线程
        await this.sleep(100);
      } catch (error) {
        console.warn(`[模块加载] ⚠️ 预加载失败: ${moduleName}`, error.message);
      }
    }
    
    console.log('[模块加载] ✅ 预加载完成');
    this.emit('preloadCompleted', { loadedCount: preloadModules.length });
  }

  /**
   * @function getPriorityValue - 获取优先级数值
   * @description 将优先级字符串转换为数值
   * @param {string} priority - 优先级字符串
   * @returns {number} 优先级数值
   */
  getPriorityValue(priority) {
    const priorities = {
      'high': 3,
      'medium': 2,
      'low': 1
    };
    return priorities[priority] || 0;
  }

  /**
   * @function showLoadingIndicator - 显示加载指示器
   * @description 显示模块加载的进度指示器
   * @param {string} moduleName - 模块名称
   * @param {string} description - 模块描述
   */
  showLoadingIndicator(moduleName, description) {
    // 创建加载指示器
    const indicator = document.createElement('div');
    indicator.id = `loading-${moduleName}`;
    indicator.className = 'ai-loading-indicator';
    indicator.innerHTML = `
      <div class="ai-loading-spinner"></div>
      <div class="ai-loading-text">正在加载 ${description}...</div>
    `;
    
    // 添加到页面
    const container = document.querySelector('.ai-sidebar__content') || document.body;
    container.appendChild(indicator);
    
    // 添加样式（如果不存在）
    if (!document.getElementById('ai-loading-styles')) {
      const styles = document.createElement('style');
      styles.id = 'ai-loading-styles';
      styles.textContent = `
        .ai-loading-indicator {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 20px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          gap: 10px;
          z-index: 10000;
        }
        .ai-loading-spinner {
          width: 20px;
          height: 20px;
          border: 2px solid #ffffff33;
          border-top: 2px solid #ffffff;
          border-radius: 50%;
          animation: ai-spin 1s linear infinite;
        }
        @keyframes ai-spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(styles);
    }
  }

  /**
   * @function hideLoadingIndicator - 隐藏加载指示器
   * @description 隐藏模块加载的进度指示器
   * @param {string} moduleName - 模块名称
   */
  hideLoadingIndicator(moduleName) {
    const indicator = document.getElementById(`loading-${moduleName}`);
    if (indicator) {
      indicator.remove();
    }
  }

  /**
   * @function unloadModule - 卸载模块
   * @description 卸载指定的模块以释放内存
   * @param {string} moduleName - 模块名称
   * @returns {boolean} 是否成功卸载
   */
  unloadModule(moduleName) {
    try {
      const module = this.loadedModules.get(moduleName);
      if (module) {
        // 调用清理方法（如果存在）
        if (typeof module.cleanup === 'function') {
          module.cleanup();
        }
        
        // 从缓存中移除
        this.loadedModules.delete(moduleName);
        
        // 更新状态
        const state = this.moduleStates.get(moduleName);
        if (state) {
          state.status = 'unloaded';
          state.loadTime = null;
        }
        
        console.log(`[模块加载] 🗑️ 模块已卸载: ${moduleName}`);
        this.emit('moduleUnloaded', { moduleName });
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`[模块加载] ❌ 模块卸载失败: ${moduleName}`, error);
      return false;
    }
  }

  /**
   * @function getModuleStatus - 获取模块状态
   * @description 获取指定模块的状态信息
   * @param {string} moduleName - 模块名称
   * @returns {Object|null} 模块状态或null
   */
  getModuleStatus(moduleName) {
    const state = this.moduleStates.get(moduleName);
    const definition = this.moduleDefinitions.get(moduleName);
    
    if (!state || !definition) {
      return null;
    }
    
    return {
      name: moduleName,
      status: state.status,
      description: definition.description,
      priority: definition.priority,
      loadTime: state.loadTime,
      error: state.error,
      retryCount: state.retryCount,
      isLoaded: this.loadedModules.has(moduleName)
    };
  }

  /**
   * @function getAllModuleStatus - 获取所有模块状态
   * @description 获取所有模块的状态信息
   * @returns {Array} 模块状态数组
   */
  getAllModuleStatus() {
    return Array.from(this.moduleDefinitions.keys())
      .map(name => this.getModuleStatus(name))
      .filter(status => status !== null);
  }

  /**
   * @function getUnusedModules - 获取未使用的模块
   * @description 获取长时间未使用的模块列表，用于自动卸载优化
   * @param {number} unusedThreshold - 未使用时间阈值(ms)，默认30分钟
   * @returns {Array<string>} 未使用模块名称数组
   */
  getUnusedModules(unusedThreshold = 30 * 60 * 1000) {
    const unusedModules = [];
    const now = Date.now();

    try {
      // 检查已加载的模块
      for (const [moduleName, moduleInstance] of this.loadedModules) {
        const state = this.moduleStates.get(moduleName);
        const definition = this.moduleDefinitions.get(moduleName);

        if (!state || !definition) {
          continue;
        }

        // 检查模块是否长时间未使用
        const lastUsed = state.lastUsed || state.loadTime || 0;
        const unusedTime = now - lastUsed;

        // 跳过高优先级模块和预加载模块
        if (definition.priority === 'high' || definition.preload) {
          continue;
        }

        // 检查是否超过未使用阈值
        if (unusedTime > unusedThreshold) {
          // 检查模块是否有活跃的引用或监听器
          const hasActiveReferences = this.checkModuleActiveReferences(moduleName, moduleInstance);

          if (!hasActiveReferences) {
            unusedModules.push(moduleName);
            // 记录未使用模块（调试信息）
          }
        }
      }

      // 按未使用时间排序，最久未使用的排在前面
      unusedModules.sort((a, b) => {
        const stateA = this.moduleStates.get(a);
        const stateB = this.moduleStates.get(b);
        const lastUsedA = stateA?.lastUsed || stateA?.loadTime || 0;
        const lastUsedB = stateB?.lastUsed || stateB?.loadTime || 0;
        return lastUsedA - lastUsedB;
      });

      console.log(`[模块加载] 📊 发现 ${unusedModules.length} 个未使用模块`);
      return unusedModules;

    } catch (error) {
      console.error('[模块加载] 获取未使用模块失败:', error);
      return [];
    }
  }

  /**
   * @function checkModuleActiveReferences - 检查模块活跃引用
   * @description 检查模块是否有活跃的引用或监听器
   * @param {string} moduleName - 模块名称
   * @param {Object} moduleInstance - 模块实例
   * @returns {boolean} 是否有活跃引用
   */
  checkModuleActiveReferences(moduleName, moduleInstance) {
    try {
      // 检查模块是否有活跃的定时器
      if (moduleInstance.timers && moduleInstance.timers.size > 0) {
        return true;
      }

      // 检查模块是否有活跃的事件监听器
      if (moduleInstance.eventListeners && moduleInstance.eventListeners.size > 0) {
        return true;
      }

      // 检查模块是否有活跃的观察器
      if (moduleInstance.observers && moduleInstance.observers.size > 0) {
        return true;
      }

      // 检查模块是否有活跃的网络请求
      if (moduleInstance.activeRequests && moduleInstance.activeRequests.size > 0) {
        return true;
      }

      // 检查模块是否有活跃的工作流或任务
      if (moduleInstance.activeWorkflows && moduleInstance.activeWorkflows.size > 0) {
        return true;
      }

      return false;
    } catch (error) {
      console.warn(`[模块加载] 检查模块活跃引用失败: ${moduleName}`, error);
      return true; // 出错时保守处理，认为有活跃引用
    }
  }

  /**
   * @function updateModuleUsage - 更新模块使用时间
   * @description 更新模块的最后使用时间
   * @param {string} moduleName - 模块名称
   */
  updateModuleUsage(moduleName) {
    const state = this.moduleStates.get(moduleName);
    if (state) {
      state.lastUsed = Date.now();
    }
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取模块加载器的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      loadedModules: this.loadedModules.size,
      totalModules: this.moduleDefinitions.size,
      loadingModules: this.loadingPromises.size,
      successRate: this.stats.totalLoads > 0 ?
        (this.stats.successfulLoads / this.stats.totalLoads * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function sleep - 睡眠函数
   * @description 异步睡眠指定时间
   * @param {number} ms - 睡眠时间(ms)
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * @function on - 添加事件监听器
   * @description 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * @function emit - 触发事件
   * @description 触发事件并调用所有监听器
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[模块加载] 事件监听器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理模块加载器使用的资源
   */
  cleanup() {
    // 卸载所有模块
    for (const moduleName of this.loadedModules.keys()) {
      this.unloadModule(moduleName);
    }
    
    // 清理状态
    this.moduleStates.clear();
    this.loadingPromises.clear();
    this.eventListeners.clear();
    this.preloadQueue = [];
    
    console.log('[模块加载] 模块加载器已清理');
  }
}

// 导出模块加载器类
export { AiModuleLoader };
