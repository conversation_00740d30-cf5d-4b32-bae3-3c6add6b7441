/**
 * @file messageTypes.js
 * @description 定义扩展内所有统一的消息类型常量，供前端 (Sidebar / Content Script)
 *              与 Background Service Worker 之间通信时使用，避免魔法字符串带来
 *              的拼写错误与协议漂移。
 *
 * 所有新增消息请在此文件集中维护，并确保双方代码均通过 import 引用。
 *
 * 兼容性说明：
 * - 支持ES6模块导入 (import { MESSAGE_TYPES } from './messageTypes.js')
 * - 支持Content Script全局变量访问 (window.MESSAGE_TYPES)
 * - 支持Service Worker全局变量访问 (globalThis.MESSAGE_TYPES)
 */

// 使用IIFE包装，确保在不同环境中都能正常工作
(function() {
  'use strict';

  // 使用 Object.freeze 保证枚举不可变
  const MESSAGE_TYPES = Object.freeze({
    // 内容捕获
    CONTENT_CAPTURED: 'ai:content:captured',
    CONTENT_SUMMARIZE: 'ai:content:summarize',
    CONTENT_ANALYZE: 'ai:content:analyze',

    // 聊天
    CHAT_SEND: 'ai:chat:send',
    CHAT_STREAM: 'ai:chat:stream',

    // 智能回复
    SMART_REPLY_GENERATE: 'ai:smart-reply:generate',

    // 页面分析
    ANALYSIS_REQUEST: 'ai:analysis:request',
    ANALYSIS_COMPARE: 'ai:analysis:compare',
    ANALYSIS_TREND: 'ai:analysis:trend',

    // 设置
    SETTINGS_GET: 'ai:settings:get',
    SETTINGS_SET: 'ai:settings:set',
    SETTINGS_RESET: 'ai:settings:reset',
    SETTINGS_EXPORT: 'ai:settings:export',
    SETTINGS_IMPORT: 'ai:settings:import',

    // 旧版配置 (兼容)
    CONFIG_GET: 'ai:config:get',
    CONFIG_SET: 'ai:config:set',

    // Notion
    NOTION_CONNECT: 'ai:notion:connect',
    NOTION_SYNC: 'ai:notion:sync',
    NOTION_CACHE_CLEAR: 'ai:notion:cache:clear',
    NOTION_CACHE_STATUS: 'ai:notion:cache:status',
    NOTION_DATABASES_GET: 'ai:notion:databases:get',

    // 光标增强
    CURSOR_ENHANCE_TOGGLE: 'ai:cursor:enhance:toggle',
    CURSOR_ENHANCE_STATUS: 'ai:cursor:enhance:status',

    // 模板
    TEMPLATE_GENERATE_REPLY: 'ai:template:generate-reply',
    TEMPLATE_SUGGEST: 'ai:template:suggest',
    TEMPLATE_USE: 'ai:template:use',

    // 工作流
    WORKFLOW_EXECUTE: 'ai:workflow:execute',

    // 国际化
    I18N_TRANSLATE: 'ai:i18n:translate',

    // 协作
    COLLABORATION_SHARE: 'ai:collaboration:share',

    // 通用
    SIDEBAR_TOGGLE: 'ai:sidebar:toggle',
    SIDEBAR_STATUS: 'ai:sidebar:status',
    NOTIFICATION_CREATE: 'ai:notification:create',
    API_CONFIGURE: 'ai:api:configure',
    STATUS_REQUEST: 'ai:status:request',
    STATUS_GET: 'ai:status:get',

    // 测试
    TEST_NOTIFICATION: 'ai:test:notification',
    TEST_ADVANCED_FALLBACK: 'ai:test:advanced-fallback',

    // 从 Background -> UI 的消息
    ANALYSIS_RESULT: 'ai:analysis:result',
    SETTINGS_UPDATE: 'ai:settings:update',
    NOTION_STATUS: 'ai:notion:status',
    NOTIFICATION_SHOW: 'ai:notification:show'
  });

  // 多环境兼容性导出
  // 1. Content Script环境 - 挂载到window对象
  if (typeof window !== 'undefined') {
    window.MESSAGE_TYPES = MESSAGE_TYPES;
  }

  // 2. Service Worker环境 - 挂载到globalThis对象
  if (typeof globalThis !== 'undefined') {
    globalThis.MESSAGE_TYPES = MESSAGE_TYPES;
  }

  // 3. Node.js环境 - 挂载到global对象
  if (typeof global !== 'undefined') {
    global.MESSAGE_TYPES = MESSAGE_TYPES;
  }

  // 4. ES6模块导出支持
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MESSAGE_TYPES };
  }

  // 返回MESSAGE_TYPES以支持ES6模块导入
  return MESSAGE_TYPES;
})();

// ES6模块导出 - 用于支持import语法
export { MESSAGE_TYPES };