/**
 * @file AI侧边栏面板主控制器 - 对话中心化重构版
 * @description 专注于对话中心体验的简化界面控制器，移除冗余功能，优化用户交互
 * @version 6.0.0
 * @since 2024-12-19
 */

// 引入统一的消息类型常量，支持多环境兼容
const MESSAGE_TYPES = window.MESSAGE_TYPES || globalThis.MESSAGE_TYPES || {};

/**
 * @class AiSidebarPanel - AI侧边栏面板主控制类
 * @description 对话中心化设计的简化界面控制器，专注于核心对话功能
 */
class AiSidebarPanel {
  /**
   * @function constructor - 构造函数
   * @description 初始化对话中心化的侧边栏面板
   */
  constructor() {
    // 初始化核心状态
    this.initializeState();

    // 初始化DOM元素引用
    this.initializeElements();

    // 设置事件监听器
    this.setupEventListeners();

    // 建立消息通信
    this.setupMessageConnection();

    // 初始化快捷模板
    this.initializeTemplateSystem();

    // 初始化对话交互
    this.initializeChatBubbleInteractions();

    // 启动自动页面分析（如果启用）
    this.initializeAutoAnalysis();

    // 初始化API和服务状态检查
    this.initializeStatusChecks();

    console.log('🚀 AI侧边栏对话中心化界面已初始化完成');
  }

  /**
   * @function initializeState - 初始化核心状态
   * @description 设置对话中心化界面的核心状态数据
   */
  initializeState() {
    // 对话历史记录
    this.messageHistory = [];

    // 当前回复风格和语言设置
    this.currentReplyStyle = 'professional';
    this.currentLanguage = 'zh';

    // 快捷模板配置
    this.templates = {
      summary: {
        name: '总结',
        icon: '📝',
        prompt: '请总结当前页面的主要内容和关键信息：'
      },
      translate: {
        name: '翻译',
        icon: '🌐',
        prompt: '请将当前页面内容翻译为{language}：'
      },
      analyze: {
        name: '分析',
        icon: '🔍',
        prompt: '请深度分析当前页面的内容结构和要点：'
      },
      reply: {
        name: '回复',
        icon: '💬',
        prompt: '请基于当前页面内容，以{style}风格生成回复：'
      },
      mindmap: {
        name: '导图',
        icon: '🧠',
        prompt: '请为当前页面内容创建思维导图结构：'
      }
    };

    // 应用状态
    this.appState = {
      isConnected: false,
      isProcessing: false,
      autoAnalysisEnabled: true,
      notionSyncEnabled: false
    };

    // 当前页面信息缓存
    this.currentPageInfo = null;
  }

  /**
   * @function initializeElements - 初始化DOM元素引用
   * @description 获取对话中心化界面的关键DOM元素
   */
  initializeElements() {
    // 主容器
    this.container = document.getElementById('ai-sidebar-container');

    // 顶部栏
    this.settingsBtn = document.getElementById('ai-sidebar-settings-btn');

    // 快捷模板系统
    this.templatesBar = this.container?.querySelector('.ai-templates-bar');
    this.templatesScroll = this.container?.querySelector('.ai-templates-scroll');
    this.templatesMoreBtn = document.getElementById('ai-templates-more-btn');

    // 对话区域（核心）
    this.chatMessages = document.getElementById('ai-chat-messages');
    this.chatInputArea = this.container?.querySelector('.ai-chat__input-area');

    // 输入控件
    this.replyStyleSelect = document.getElementById('ai-reply-style');
    this.languageSelect = document.getElementById('ai-language');
    this.chatInput = document.getElementById('ai-chat-input');

    // 统一设置模态框
    this.settingsModal = document.getElementById('ai-settings-modal');
    this.settingsCloseBtn = document.getElementById('ai-settings-close-btn');
    this.settingsSaveBtn = document.getElementById('ai-settings-save-btn');
    this.settingsResetBtn = document.getElementById('ai-settings-reset-btn');

    // 验证关键元素
    this.validateElements();
  }

  /**
   * @function validateElements - 验证关键DOM元素
   * @description 检查重构后界面的关键元素是否正确加载
   */
  validateElements() {
    const requiredElements = {
      'container': this.container,
      'chatMessages': this.chatMessages,
      'chatInput': this.chatInput,
      'replyStyleSelect': this.replyStyleSelect,
      'languageSelect': this.languageSelect,
      'settingsBtn': this.settingsBtn
    };

    const missingElements = [];
    for (const [name, element] of Object.entries(requiredElements)) {
      if (!element) {
        missingElements.push(name);
      }
    }

    if (missingElements.length > 0) {
      console.warn('⚠️ 缺少关键DOM元素:', missingElements);
    } else {
      console.log('✅ 所有关键DOM元素已正确加载');
    }
  }

  /**
   * @function setupEventListeners - 设置事件监听器
   * @description 为重构后的界面元素添加事件监听器
   */
  setupEventListeners() {
    // 设置按钮事件
    this.setupSettingsEvents();

    // 快捷模板系统事件
    this.setupTemplateEvents();

    // 对话输入事件
    this.setupChatInputEvents();

    // 对话气泡交互事件
    this.setupChatBubbleEvents();

    // 全局键盘事件
    this.setupGlobalKeyboardEvents();

    console.log('✅ 事件监听器设置完成');
  }

  /**
   * @function setupSettingsEvents - 设置设置相关事件
   * @description 设置模态框和设置按钮的事件监听
   */
  setupSettingsEvents() {
    // 设置按钮点击事件
    if (this.settingsBtn) {
      this.settingsBtn.addEventListener('click', () => {
        this.openSettingsModal();
      });
    }

    // 设置模态框关闭事件
    if (this.settingsCloseBtn) {
      this.settingsCloseBtn.addEventListener('click', () => {
        this.closeSettingsModal();
      });
    }

    // 设置保存事件
    if (this.settingsSaveBtn) {
      this.settingsSaveBtn.addEventListener('click', () => {
        this.saveSettings();
      });
    }

    // 设置重置事件
    if (this.settingsResetBtn) {
      this.settingsResetBtn.addEventListener('click', () => {
        this.resetSettings();
      });
    }

    // 模态框背景点击关闭
    if (this.settingsModal) {
      this.settingsModal.addEventListener('click', (e) => {
        if (e.target === this.settingsModal) {
          this.closeSettingsModal();
        }
      });
    }
  }

  /**
   * @function setupTemplateEvents - 设置快捷模板事件
   * @description 设置模板按钮和更多按钮的事件监听
   */
  setupTemplateEvents() {
    // 模板按钮点击事件
    if (this.templatesScroll) {
      this.templatesScroll.addEventListener('click', (e) => {
        const templateItem = e.target.closest('.ai-template-item');
        if (templateItem) {
          const templateType = templateItem.dataset.template;
          this.useTemplate(templateType);
        }
      });
    }

    // 更多模板按钮事件
    if (this.templatesMoreBtn) {
      this.templatesMoreBtn.addEventListener('click', () => {
        this.showMoreTemplates();
      });
    }
  }

  /**
   * @function setupChatInputEvents - 设置对话输入事件
   * @description 设置输入框、选择器等输入相关事件
   */
  setupChatInputEvents() {
    // 输入框键盘事件
    if (this.chatInput) {
      this.chatInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // 输入框自动调整高度
      this.chatInput.addEventListener('input', () => {
        this.adjustInputHeight();
      });
    }

    // 回复风格选择事件
    if (this.replyStyleSelect) {
      this.replyStyleSelect.addEventListener('change', (e) => {
        this.currentReplyStyle = e.target.value;
        console.log('回复风格已切换为:', this.currentReplyStyle);
      });
    }

    // 语言选择事件
    if (this.languageSelect) {
      this.languageSelect.addEventListener('change', (e) => {
        this.currentLanguage = e.target.value;
        console.log('语言已切换为:', this.currentLanguage);
      });
    }
  }

  /**
   * @function setupChatBubbleEvents - 设置对话气泡事件
   * @description 设置气泡悬浮菜单和操作按钮事件
   */
  setupChatBubbleEvents() {
    // 使用事件委托处理气泡操作按钮
    if (this.chatMessages) {
      this.chatMessages.addEventListener('click', (e) => {
        const actionBtn = e.target.closest('.ai-action-btn');
        if (actionBtn) {
          const action = actionBtn.dataset.action;
          const messageElement = actionBtn.closest('.ai-chat__message');
          this.handleBubbleAction(action, messageElement);
        }
      });
    }
  }

  /**
   * @function setupGlobalKeyboardEvents - 设置全局键盘事件
   * @description 设置全局快捷键和键盘事件
   */
  setupGlobalKeyboardEvents() {
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (this.settingsModal && !this.settingsModal.classList.contains('hidden')) {
          this.closeSettingsModal();
        }
      }
    });
  }

  /**
   * @function initializeTemplateSystem - 初始化快捷模板系统
   * @description 初始化模板数据和模板按钮
   */
  initializeTemplateSystem() {
    // 渲染模板按钮
    this.renderTemplateButtons();

    // 加载用户自定义模板
    this.loadCustomTemplates();

    console.log('✅ 快捷模板系统初始化完成');
  }

  /**
   * @function initializeChatBubbleInteractions - 初始化对话气泡交互
   * @description 初始化气泡悬浮菜单和操作功能
   */
  initializeChatBubbleInteractions() {
    // 设置气泡操作处理器
    this.bubbleActionHandlers = {
      copy: this.copyMessageContent.bind(this),
      translate: this.translateMessage.bind(this),
      notion: this.saveToNotion.bind(this)
    };

    console.log('✅ 对话气泡交互初始化完成');
  }

  /**
   * @function renderTemplateButtons - 渲染模板按钮
   * @description 在模板栏中渲染快捷模板按钮
   */
  renderTemplateButtons() {
    if (!this.templatesScroll) return;

    const templateButtons = Object.entries(this.templates).map(([key, template]) => {
      return `
        <button type="button" class="ai-template-item" data-template="${key}">
          <span class="ai-template-icon">${template.icon}</span>
          <span class="ai-template-name">${template.name}</span>
        </button>
      `;
    }).join('');

    this.templatesScroll.innerHTML = templateButtons;
  }

  /**
   * @function loadCustomTemplates - 加载用户自定义模板
   * @description 从本地存储加载用户自定义的模板
   */
  loadCustomTemplates() {
    try {
      const customTemplates = localStorage.getItem('ai-custom-templates');
      if (customTemplates) {
        const parsed = JSON.parse(customTemplates);
        this.templates = { ...this.templates, ...parsed };
        this.renderTemplateButtons();
      }
    } catch (error) {
      console.warn('加载自定义模板失败:', error);
    }
  }

  /**
   * @function useTemplate - 使用模板
   * @description 将选中的模板内容插入到输入框
   * @param {string} templateType - 模板类型
   */
  useTemplate(templateType) {
    const template = this.templates[templateType];
    if (!template || !this.chatInput) return;

    let prompt = template.prompt;

    // 替换模板变量
    prompt = prompt.replace('{style}', this.getStyleText(this.currentReplyStyle));
    prompt = prompt.replace('{language}', this.getLanguageText(this.currentLanguage));

    // 插入到输入框
    this.chatInput.value = prompt;
    this.chatInput.focus();

    // 调整输入框高度
    this.adjustInputHeight();

    console.log(`使用模板: ${template.name}`);
  }

  /**
   * @function showMoreTemplates - 显示更多模板
   * @description 显示模板管理界面或更多模板选项
   */
  showMoreTemplates() {
    // 这里可以实现模板管理界面
    console.log('显示更多模板选项');
    // 暂时显示一个简单的提示
    this.showNotification('模板管理功能开发中...', 'info');
  }

  /**
   * @function sendMessage - 发送消息
   * @description 发送用户输入的消息并处理回复
   */
  async sendMessage() {
    const message = this.chatInput?.value?.trim();
    if (!message) return;

    // 清空输入框
    this.chatInput.value = '';
    this.adjustInputHeight();

    // 添加用户消息到界面
    this.addMessageToChat(message, 'user');

    try {
      // 设置处理状态
      this.appState.isProcessing = true;

      // 根据选择的风格生成回复
      const response = await this.generateStyledResponse(message);

      // 添加AI回复到界面
      this.addMessageToChat(response, 'assistant');

    } catch (error) {
      console.error('发送消息失败:', error);
      this.addMessageToChat('抱歉，处理您的消息时出现了错误。请稍后重试。', 'assistant');
    } finally {
      this.appState.isProcessing = false;
    }
  }

  /**
   * @function addMessageToChat - 添加消息到对话界面
   * @description 在对话区域添加新的消息气泡
   * @param {string} content - 消息内容
   * @param {string} type - 消息类型 ('user' | 'assistant')
   */
  addMessageToChat(content, type) {
    if (!this.chatMessages || !content) return;

    const messageId = 'msg-' + Date.now();
    const timestamp = new Date().toLocaleTimeString();

    const messageHtml = `
      <div class="ai-chat__message ai-chat__message--${type}" data-message-id="${messageId}">
        <div class="ai-chat__bubble">
          <div class="ai-chat__content">
            <div class="ai-chat__text">${this.escapeHtml(content)}</div>
          </div>
          <!-- 悬浮操作菜单 - 鼠标悬停显示 -->
          <div class="ai-chat__hover-actions">
            <button type="button" class="ai-action-btn" data-action="copy" title="复制内容">
              <span class="ai-icon">📋</span>
            </button>
            <button type="button" class="ai-action-btn" data-action="translate" title="翻译内容">
              <span class="ai-icon">🌐</span>
            </button>
            <button type="button" class="ai-action-btn" data-action="notion" title="保存到Notion">
              <span class="ai-icon">📝</span>
            </button>
          </div>
        </div>
        <div class="ai-chat__time">${timestamp}</div>
      </div>
    `;

    this.chatMessages.insertAdjacentHTML('beforeend', messageHtml);

    // 滚动到底部
    this.scrollToBottom();

    // 更新消息历史
    this.messageHistory.push({
      id: messageId,
      type,
      content,
      timestamp: Date.now()
    });
  }

  /**
   * @function generateStyledResponse - 生成风格化回复
   * @description 根据选择的风格和语言生成AI回复
   * @param {string} message - 用户消息
   * @returns {Promise<string>} AI回复
   */
  async generateStyledResponse(message) {
    // 构建带风格的提示词
    const stylePrompt = this.buildStylePrompt(message);

    // 发送到后台处理
    const response = await this.sendToBackground({
      type: MESSAGE_TYPES.CHAT_SEND,
      payload: {
        message: stylePrompt,
        style: this.currentReplyStyle,
        language: this.currentLanguage,
        history: this.messageHistory.slice(-5) // 只发送最近5条消息作为上下文
      }
    });

    if (response && response.success) {
      return response.data.reply || '收到您的消息，正在处理中...';
    } else {
      throw new Error(response?.error || '生成回复失败');
    }
  }

  /**
   * @function buildStylePrompt - 构建风格化提示词
   * @description 根据选择的风格构建提示词
   * @param {string} message - 原始消息
   * @returns {string} 风格化提示词
   */
  buildStylePrompt(message) {
    const styleInstructions = {
      professional: '请以专业、正式的语调回复',
      casual: '请以轻松、友好的语调回复',
      detailed: '请提供详细、全面的回复',
      concise: '请提供简洁、要点明确的回复',
      creative: '请以创意、有趣的方式回复'
    };

    const instruction = styleInstructions[this.currentReplyStyle] || styleInstructions.professional;

    return `${instruction}，使用${this.getLanguageText(this.currentLanguage)}回复：\n\n${message}`;
  }

  /**
   * @function handleBubbleAction - 处理气泡操作
   * @description 处理气泡悬浮菜单的操作按钮点击
   * @param {string} action - 操作类型
   * @param {HTMLElement} messageElement - 消息元素
   */
  handleBubbleAction(action, messageElement) {
    if (!messageElement) return;

    const messageId = messageElement.dataset.messageId;
    const textElement = messageElement.querySelector('.ai-chat__text');
    const content = textElement?.textContent || '';

    if (this.bubbleActionHandlers[action]) {
      this.bubbleActionHandlers[action](content, messageId, messageElement);
    } else {
      console.warn('未知的气泡操作:', action);
    }
  }

  /**
   * @function copyMessageContent - 复制消息内容
   * @description 复制消息内容到剪贴板
   * @param {string} content - 消息内容
   * @param {string} messageId - 消息ID
   */
  async copyMessageContent(content, messageId) {
    try {
      await navigator.clipboard.writeText(content);
      this.showNotification('内容已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：使用传统方法
      this.fallbackCopy(content);
    }
  }

  /**
   * @function translateMessage - 翻译消息
   * @description 翻译消息内容
   * @param {string} content - 消息内容
   * @param {string} messageId - 消息ID
   */
  async translateMessage(content, messageId) {
    try {
      const targetLanguage = this.currentLanguage === 'zh' ? 'en' : 'zh';
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.I18N_TRANSLATE,
        payload: {
          text: content,
          targetLanguage,
          sourceLanguage: this.currentLanguage
        }
      });

      if (response && response.success) {
        // 在当前消息下方添加翻译结果
        this.addTranslationResult(messageId, response.data.translatedText, targetLanguage);
      } else {
        throw new Error(response?.error || '翻译失败');
      }
    } catch (error) {
      console.error('翻译失败:', error);
      this.showNotification('翻译失败，请稍后重试', 'error');
    }
  }

  /**
   * @function saveToNotion - 保存到Notion
   * @description 将消息内容保存到Notion
   * @param {string} content - 消息内容
   * @param {string} messageId - 消息ID
   */
  async saveToNotion(content, messageId) {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.NOTION_SYNC,
        payload: {
          content,
          messageId,
          timestamp: Date.now(),
          source: 'ai-sidebar-chat'
        }
      });

      if (response && response.success) {
        this.showNotification('已保存到Notion', 'success');
      } else {
        throw new Error(response?.error || '保存失败');
      }
    } catch (error) {
      console.error('保存到Notion失败:', error);
      this.showNotification('保存到Notion失败，请检查连接设置', 'error');
    }
  }

  /**
   * @function adjustInputHeight - 调整输入框高度
   * @description 根据内容自动调整输入框高度
   */
  adjustInputHeight() {
    if (!this.chatInput) return;

    this.chatInput.style.height = 'auto';
    const scrollHeight = this.chatInput.scrollHeight;
    const maxHeight = 120; // 最大高度120px

    this.chatInput.style.height = Math.min(scrollHeight, maxHeight) + 'px';
  }

  /**
   * @function scrollToBottom - 滚动到底部
   * @description 将对话区域滚动到最底部
   */
  scrollToBottom() {
    if (this.chatMessages) {
      this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
  }

  /**
   * @function getStyleText - 获取风格文本
   * @description 获取风格的中文描述
   * @param {string} style - 风格代码
   * @returns {string} 风格描述
   */
  getStyleText(style) {
    const styleTexts = {
      professional: '专业',
      casual: '轻松',
      detailed: '详细',
      concise: '简洁',
      creative: '创意'
    };
    return styleTexts[style] || '专业';
  }

  /**
   * @function getLanguageText - 获取语言文本
   * @description 获取语言的中文描述
   * @param {string} language - 语言代码
   * @returns {string} 语言描述
   */
  getLanguageText(language) {
    const languageTexts = {
      zh: '中文',
      en: '英文',
      ja: '日文',
      ko: '韩文'
    };
    return languageTexts[language] || '中文';
  }

  /**
   * @function escapeHtml - 转义HTML
   * @description 转义HTML特殊字符，防止XSS
   * @param {string} text - 原始文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * @function fallbackCopy - 降级复制方法
   * @description 当现代剪贴板API不可用时的降级方案
   * @param {string} text - 要复制的文本
   */
  fallbackCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();

    try {
      document.execCommand('copy');
      this.showNotification('内容已复制到剪贴板', 'success');
    } catch (error) {
      this.showNotification('复制失败', 'error');
    } finally {
      document.body.removeChild(textArea);
    }
  }

  /**
   * @function addTranslationResult - 添加翻译结果
   * @description 在消息下方添加翻译结果
   * @param {string} messageId - 消息ID
   * @param {string} translatedText - 翻译文本
   * @param {string} targetLanguage - 目标语言
   */
  addTranslationResult(messageId, translatedText, targetLanguage) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) return;

    // 检查是否已有翻译结果
    const existingTranslation = messageElement.querySelector('.ai-translation-result');
    if (existingTranslation) {
      existingTranslation.remove();
    }

    const languageText = this.getLanguageText(targetLanguage);
    const translationHtml = `
      <div class="ai-translation-result">
        <div class="ai-translation-label">翻译为${languageText}：</div>
        <div class="ai-translation-text">${this.escapeHtml(translatedText)}</div>
      </div>
    `;

    messageElement.insertAdjacentHTML('beforeend', translationHtml);
  }

  /**
   * @function openSettingsModal - 打开设置模态框
   * @description 显示设置模态框并加载当前设置
   */
  openSettingsModal() {
    if (this.settingsModal) {
      this.settingsModal.classList.remove('hidden');
      this.loadCurrentSettings();
    }
  }

  /**
   * @function closeSettingsModal - 关闭设置模态框
   * @description 隐藏设置模态框
   */
  closeSettingsModal() {
    if (this.settingsModal) {
      this.settingsModal.classList.add('hidden');
    }
  }

  /**
   * @function loadCurrentSettings - 加载当前设置
   * @description 从存储中加载设置并填充到表单
   */
  async loadCurrentSettings() {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.SETTINGS_GET
      });

      if (response && response.success) {
        const settings = response.data;
        this.populateSettingsForm(settings);
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  /**
   * @function populateSettingsForm - 填充设置表单
   * @description 将设置数据填充到表单控件
   * @param {Object} settings - 设置数据
   */
  populateSettingsForm(settings) {
    // 检查API状态而不是填充配置（配置现在来自config.js）
    this.checkApiStatus();
    this.checkNotionStatus();

    // 功能设置
    const autoAnalysisCheckbox = document.getElementById('ai-auto-analysis');
    const cursorEnhanceCheckbox = document.getElementById('ai-cursor-enhance');
    const notionSyncCheckbox = document.getElementById('ai-notion-sync');

    if (autoAnalysisCheckbox) {
      autoAnalysisCheckbox.checked = settings.autoAnalysis !== false;
    }
    if (cursorEnhanceCheckbox) {
      cursorEnhanceCheckbox.checked = settings.cursorEnhance !== false;
    }
    if (notionSyncCheckbox) {
      notionSyncCheckbox.checked = settings.notionSync !== false;
    }
  }

  /**
   * @function saveSettings - 保存设置
   * @description 收集表单数据并保存设置
   */
  async saveSettings() {
    try {
      const settings = this.collectSettingsData();

      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.SETTINGS_SET,
        payload: settings
      });

      if (response && response.success) {
        this.showNotification('设置已保存', 'success');
        this.closeSettingsModal();
      } else {
        throw new Error(response?.error || '保存失败');
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      this.showNotification('保存设置失败', 'error');
    }
  }

  /**
   * @function collectSettingsData - 收集设置数据
   * @description 从表单控件收集设置数据
   * @returns {Object} 设置数据
   */
  collectSettingsData() {
    // 只收集功能开关设置，API配置现在来自config.js
    return {
      autoAnalysis: document.getElementById('ai-auto-analysis')?.checked || false,
      cursorEnhance: document.getElementById('ai-cursor-enhance')?.checked || false,
      notionSync: document.getElementById('ai-notion-sync')?.checked || false
    };
  }

  /**
   * @function resetSettings - 重置设置
   * @description 重置所有设置为默认值
   */
  async resetSettings() {
    if (!confirm('确定要重置所有设置吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.SETTINGS_RESET
      });

      if (response && response.success) {
        this.showNotification('设置已重置', 'success');
        this.loadCurrentSettings();
      } else {
        throw new Error(response?.error || '重置失败');
      }
    } catch (error) {
      console.error('重置设置失败:', error);
      this.showNotification('重置设置失败', 'error');
    }
  }

  /**
   * @function setupMessageConnection - 建立消息连接
   * @description 设置与后台服务的消息通信
   */
  setupMessageConnection() {
    // 监听来自后台的消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleBackgroundMessage(message, sender, sendResponse);
      });
    }

    console.log('✅ 消息连接已建立');
  }

  /**
   * @function handleBackgroundMessage - 处理后台消息
   * @description 处理来自后台服务的消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  handleBackgroundMessage(message, sender, sendResponse) {
    const { type, payload } = message;

    switch (type) {
      case MESSAGE_TYPES.ANALYSIS_RESULT:
        this.handleAnalysisResult(payload);
        break;
      case MESSAGE_TYPES.NOTION_STATUS:
        this.handleNotionStatus(payload);
        break;
      case MESSAGE_TYPES.SETTINGS_UPDATE:
        this.handleSettingsUpdate(payload);
        break;
      case 'TRIGGER_AUTO_ANALYSIS':
        // 处理自动分析触发消息
        this.handleAutoAnalysisTrigger(message);
        break;
      default:
        console.log('收到未处理的后台消息:', type);
    }
  }

  /**
   * @function sendToBackground - 发送消息到后台
   * @description 向后台服务发送消息并等待响应
   * @param {Object} message - 消息对象
   * @returns {Promise<Object>} 响应对象
   */
  async sendToBackground(message) {
    return new Promise((resolve, reject) => {
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        reject(new Error('Chrome Runtime API不可用'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('消息发送超时'));
      }, 10000);

      chrome.runtime.sendMessage(message, (response) => {
        clearTimeout(timeout);

        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * @function initializeInterface - 初始化界面
   * @description 初始化界面状态和自动功能
   */
  async initializeInterface() {
    // 设置初始状态
    this.setInitialState();

    // 自动页面分析（如果启用）
    if (this.moduleStates.autoAnalysis.enabled) {
      this.autoAnalyzePage();
    }

    // 加载设置
    await this.loadCurrentSettings();

    console.log('✅ 界面初始化完成');
  }

  /**
   * @function setInitialState - 设置初始状态
   * @description 设置界面的初始状态
   */
  setInitialState() {
    // 设置默认选择器值
    if (this.replyStyleSelect) {
      this.replyStyleSelect.value = this.currentReplyStyle;
    }
    if (this.languageSelect) {
      this.languageSelect.value = this.currentLanguage;
    }

    // 聚焦输入框
    if (this.chatInput) {
      this.chatInput.focus();
    }
  }

  /**
   * @function autoAnalyzePage - 自动分析页面
   * @description 自动分析当前页面内容
   */
  async autoAnalyzePage() {
    try {
      const pageInfo = await this.getCurrentPageInfo();
      if (pageInfo && pageInfo.textContent) {
        // 生成页面摘要
        const summary = await this.generatePageSummary(pageInfo);
        if (summary) {
          this.addMessageToChat('assistant', `📄 页面分析完成：\n\n${summary}`);
        }
      }
    } catch (error) {
      console.error('自动页面分析失败:', error);
    }
  }

  /**
   * @function getCurrentPageInfo - 获取当前页面信息
   * @description 获取当前页面的标题、URL和内容
   * @returns {Promise<Object>} 页面信息
   */
  async getCurrentPageInfo() {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CONTENT_CAPTURED,
        payload: {
          url: window.location.href,
          title: document.title,
          textContent: document.body.innerText.substring(0, 5000) // 限制长度
        }
      });

      if (response && response.success) {
        return response.data;
      }
    } catch (error) {
      console.error('获取页面信息失败:', error);
    }

    return {
      url: window.location.href,
      title: document.title,
      textContent: document.body.innerText.substring(0, 5000)
    };
  }

  /**
   * @function generatePageSummary - 生成页面摘要
   * @description 生成当前页面的智能摘要
   * @param {Object} pageInfo - 页面信息
   * @returns {Promise<string>} 页面摘要
   */
  async generatePageSummary(pageInfo) {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CONTENT_SUMMARIZE,
        payload: {
          content: pageInfo.textContent,
          title: pageInfo.title,
          url: pageInfo.url
        }
      });

      if (response && response.success) {
        return response.data.summary;
      }
    } catch (error) {
      console.error('生成页面摘要失败:', error);
    }

    return null;
  }

  /**
   * @function showNotification - 显示通知
   * @description 显示临时通知消息
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 ('success' | 'error' | 'info' | 'warning')
   */
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `ai-notification ai-notification--${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.classList.add('ai-notification--show');
    }, 10);

    // 自动隐藏
    setTimeout(() => {
      notification.classList.remove('ai-notification--show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  /**
   * @function handleAnalysisResult - 处理分析结果
   * @description 处理来自后台的分析结果
   * @param {Object} payload - 分析结果数据
   */
  handleAnalysisResult(payload) {
    if (payload && payload.summary) {
      this.addMessageToChat('assistant', `📊 页面分析结果：\n\n${payload.summary}`);
    }
  }

  /**
   * @function handleNotionStatus - 处理Notion状态
   * @description 处理Notion连接状态更新
   * @param {Object} payload - 状态数据
   */
  handleNotionStatus(payload) {
    if (payload && payload.connected !== undefined) {
      this.moduleStates.notion.connected = payload.connected;
      const status = payload.connected ? '已连接' : '未连接';
      console.log(`Notion状态: ${status}`);
    }
  }

  /**
   * @function handleSettingsUpdate - 处理设置更新
   * @description 处理设置更新通知
   * @param {Object} payload - 设置数据
   */
  handleSettingsUpdate(payload) {
    console.log('设置已更新:', payload);
    this.showNotification('设置已更新', 'info');
  }

  /**
   * @function initializeAutoAnalysis - 初始化自动页面分析
   * @description 如果启用自动分析，则在页面加载时自动分析内容
   */
  async initializeAutoAnalysis() {
    if (!this.appState.autoAnalysisEnabled) {
      console.log('📊 自动页面分析已禁用');
      return;
    }

    try {
      // 获取当前页面信息
      const pageInfo = await this.getCurrentPageInfo();
      if (pageInfo) {
        this.currentPageInfo = pageInfo;

        // 自动生成欢迎消息，包含页面基本信息
        const welcomeMessage = this.generateWelcomeMessage(pageInfo);
        this.addMessageToChat(welcomeMessage, 'assistant');

        console.log('📊 自动页面分析完成');
      }
    } catch (error) {
      console.warn('⚠️ 自动页面分析失败:', error);
    }
  }

  /**
   * @function generateWelcomeMessage - 生成包含页面信息的欢迎消息
   * @description 基于页面信息生成个性化的欢迎消息
   * @param {Object} pageInfo - 页面信息
   * @returns {string} 欢迎消息内容
   */
  generateWelcomeMessage(pageInfo) {
    const { title, url, textContent } = pageInfo;
    const wordCount = textContent ? textContent.length : 0;

    return `你好！我已经分析了当前页面：

📄 **${title || '未知页面'}**
🔗 ${url || ''}
📊 页面内容约 ${wordCount} 字符

我可以帮你：
• 📝 总结页面要点
• 🌐 翻译内容
• 🔍 深度分析
• 💬 生成回复
• 🧠 创建思维导图

请选择上方的快捷模板，或直接告诉我需要什么帮助！`;
  }

  /**
   * @function getCurrentPageInfo - 获取当前页面信息
   * @description 通过消息传递获取当前页面的基本信息
   * @returns {Promise<Object>} 页面信息对象
   */
  async getCurrentPageInfo() {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CONTENT_CAPTURED || 'ai:content:captured',
        payload: {
          action: 'getCurrentPage'
        }
      });

      if (response && response.success) {
        return response.data;
      }
    } catch (error) {
      console.error('获取页面信息失败:', error);
    }

    // 降级方案：使用基本的页面信息
    return {
      url: window.location?.href || '',
      title: document.title || '未知页面',
      textContent: document.body?.innerText?.substring(0, 1000) || ''
    };
  }

  /**
   * @function handleAutoAnalysisTrigger - 处理自动分析触发
   * @description 当页面切换时自动触发内容分析
   * @param {Object} message - 触发消息
   */
  async handleAutoAnalysisTrigger(message) {
    try {
      console.log('[AI侧边栏] 收到自动分析触发消息:', message);

      // 检查是否启用了自动分析
      const settings = await this.settingsManager.getSettings();
      if (!settings.features?.enableAutoAnalysis || !settings.analysis?.autoAnalyzePages) {
        console.log('[AI侧边栏] 自动分析已禁用，跳过');
        return;
      }

      // 延迟一段时间确保页面内容完全加载
      setTimeout(async () => {
        try {
          console.log('[AI侧边栏] 开始执行自动分析...');

          // 获取当前标签页信息
          const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

          if (currentTab && currentTab.id === message.tabId) {
            // 触发内容分析
            await this.triggerContentAnalysis();
            console.log('[AI侧边栏] 自动分析完成');
          } else {
            console.log('[AI侧边栏] 标签页已切换，跳过自动分析');
          }
        } catch (error) {
          console.error('[AI侧边栏] 自动分析执行失败:', error);
        }
      }, 1000); // 延迟1秒确保内容加载完成

    } catch (error) {
      console.error('[AI侧边栏] 处理自动分析触发失败:', error);
    }
  }

  /**
   * @function triggerContentAnalysis - 触发内容分析
   * @description 主动触发当前页面的内容分析
   */
  async triggerContentAnalysis() {
    try {
      // 获取当前标签页
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!currentTab) {
        console.warn('[AI侧边栏] 无法获取当前标签页');
        return;
      }

      // 向内容脚本请求页面内容
      const response = await chrome.tabs.sendMessage(currentTab.id, {
        type: 'GET_PAGE_CONTENT'
      });

      if (response && response.success) {
        console.log('[AI侧边栏] 获取页面内容成功，开始分析...');

        // 通过后台服务工作器进行内容分析
        const analysisResult = await this.sendToBackground({
          type: 'CONTENT_ANALYSIS',
          payload: {
            content: response.data.textContent,
            pageInfo: {
              url: response.data.url,
              title: response.data.title,
              timestamp: Date.now()
            },
            analysisType: 'auto' // 自动分析类型
          }
        });

        if (analysisResult && analysisResult.success) {
          console.log('[AI侧边栏] 内容分析完成:', analysisResult);

          // 显示分析结果到对话区域
          this.displayAnalysisResult(analysisResult.data);

          // 显示分析完成通知
          this.showAnalysisNotification(analysisResult.data);
        } else {
          console.warn('[AI侧边栏] 内容分析失败:', analysisResult?.error);
          this.showAnalysisNotification({ error: '分析失败' });
        }
      } else {
        console.warn('[AI侧边栏] 获取页面内容失败');
      }
    } catch (error) {
      console.error('[AI侧边栏] 触发内容分析失败:', error);
      this.showAnalysisNotification({ error: '分析失败' });
    }
  }

  /**
   * @function displayAnalysisResult - 显示分析结果到对话区域
   * @description 将分析结果以AI助手消息的形式显示在对话区域
   * @param {Object} analysisData - 分析结果数据
   */
  displayAnalysisResult(analysisData) {
    try {
      if (!analysisData) return;

      // 构建分析结果消息
      let analysisMessage = '📊 **页面分析结果**\n\n';

      // 添加基础分析信息
      if (analysisData.summary) {
        analysisMessage += `**内容摘要：**\n${analysisData.summary}\n\n`;
      }

      // 添加关键词
      if (analysisData.keywords && analysisData.keywords.length > 0) {
        analysisMessage += `**关键词：**\n${analysisData.keywords.join(', ')}\n\n`;
      }

      // 添加情感分析
      if (analysisData.sentiment) {
        analysisMessage += `**情感倾向：** ${analysisData.sentiment}\n\n`;
      }

      // 添加结构分析
      if (analysisData.structure) {
        analysisMessage += `**内容结构：**\n`;
        if (analysisData.structure.headings) {
          analysisMessage += `- 标题数量：${analysisData.structure.headings}\n`;
        }
        if (analysisData.structure.paragraphs) {
          analysisMessage += `- 段落数量：${analysisData.structure.paragraphs}\n`;
        }
        if (analysisData.structure.wordCount) {
          analysisMessage += `- 字数统计：${analysisData.structure.wordCount}\n`;
        }
        analysisMessage += '\n';
      }

      // 添加建议
      if (analysisData.suggestions && analysisData.suggestions.length > 0) {
        analysisMessage += `**智能建议：**\n`;
        analysisData.suggestions.forEach((suggestion, index) => {
          analysisMessage += `${index + 1}. ${suggestion}\n`;
        });
      }

      // 将分析结果作为AI助手消息添加到对话历史
      this.addMessageToChat(analysisMessage, 'assistant');

    } catch (error) {
      console.error('[AI侧边栏] 显示分析结果失败:', error);
    }
  }

  /**
   * @function showAnalysisNotification - 显示分析通知
   * @description 显示内容分析完成的简短通知
   * @param {Object} analysisData - 分析结果数据
   */
  showAnalysisNotification(analysisData) {
    try {
      // 创建一个简短的通知显示分析完成
      const notification = document.createElement('div');
      notification.className = 'ai-analysis-notification';
      notification.innerHTML = `
        <div class="ai-notification-content">
          <span class="ai-notification-icon">🔍</span>
          <span class="ai-notification-text">页面分析完成</span>
        </div>
      `;

      // 添加样式
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--ai-bg-secondary);
        border: 1px solid var(--ai-border-color);
        border-radius: 8px;
        padding: 12px 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        font-size: 14px;
        color: var(--ai-text-primary);
        animation: slideInRight 0.3s ease-out;
      `;

      document.body.appendChild(notification);

      // 3秒后自动移除通知
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideOutRight 0.3s ease-in';
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }
      }, 3000);

    } catch (error) {
      console.error('[AI侧边栏] 显示分析通知失败:', error);
    }
  }

  /**
   * @function checkApiStatus - 检查API状态
   * @description 检查Gemini API的连接状态
   */
  async checkApiStatus() {
    const statusIndicator = document.getElementById('ai-gemini-status');
    if (!statusIndicator) return;

    try {
      // 更新状态为检查中
      this.updateStatusIndicator(statusIndicator, 'loading', '检查中...');

      // 向后台请求API状态检查
      const response = await this.sendToBackground({
        type: 'CHECK_GEMINI_API_STATUS'
      });

      if (response && response.success) {
        this.updateStatusIndicator(statusIndicator, 'success', '已连接');
      } else {
        this.updateStatusIndicator(statusIndicator, 'error', response?.error || '连接失败');
      }
    } catch (error) {
      console.error('[AI侧边栏] 检查API状态失败:', error);
      this.updateStatusIndicator(statusIndicator, 'error', '检查失败');
    }
  }

  /**
   * @function checkNotionStatus - 检查Notion状态
   * @description 检查Notion集成的连接状态
   */
  async checkNotionStatus() {
    const notionStatusIndicator = document.getElementById('ai-notion-status');
    const knowledgeStatusIndicator = document.getElementById('ai-knowledge-status');

    if (!notionStatusIndicator || !knowledgeStatusIndicator) return;

    try {
      // 更新状态为检查中
      this.updateStatusIndicator(notionStatusIndicator, 'loading', '检查中...');
      this.updateStatusIndicator(knowledgeStatusIndicator, 'loading', '检查中...');

      // 向后台请求Notion状态检查
      const response = await this.sendToBackground({
        type: 'CHECK_NOTION_STATUS'
      });

      if (response && response.success) {
        // 更新连接状态
        this.updateStatusIndicator(notionStatusIndicator, 'success', '已连接');

        // 更新知识库状态
        if (response.knowledgeBaseLoaded) {
          this.updateStatusIndicator(knowledgeStatusIndicator, 'success', `已加载 (${response.knowledgeCount || 0}条)`);
        } else {
          this.updateStatusIndicator(knowledgeStatusIndicator, 'warning', '未加载');
        }
      } else {
        this.updateStatusIndicator(notionStatusIndicator, 'error', response?.error || '连接失败');
        this.updateStatusIndicator(knowledgeStatusIndicator, 'error', '无法访问');
      }
    } catch (error) {
      console.error('[AI侧边栏] 检查Notion状态失败:', error);
      this.updateStatusIndicator(notionStatusIndicator, 'error', '检查失败');
      this.updateStatusIndicator(knowledgeStatusIndicator, 'error', '检查失败');
    }
  }

  /**
   * @function updateStatusIndicator - 更新状态指示器
   * @description 更新状态指示器的显示
   * @param {Element} indicator - 状态指示器元素
   * @param {string} status - 状态类型 (loading, success, error, warning)
   * @param {string} text - 状态文本
   */
  updateStatusIndicator(indicator, status, text) {
    if (!indicator) return;

    const dot = indicator.querySelector('.ai-status-dot');
    const textElement = indicator.querySelector('.ai-status-text');

    if (dot) {
      // 移除所有状态类
      dot.classList.remove('ai-status-dot--loading', 'ai-status-dot--success', 'ai-status-dot--error', 'ai-status-dot--warning');
      // 添加新状态类
      dot.classList.add(`ai-status-dot--${status}`);
    }

    if (textElement) {
      textElement.textContent = text;
    }
  }

  /**
   * @function initializeStatusChecks - 初始化状态检查
   * @description 在侧边栏初始化时检查API和服务状态
   */
  async initializeStatusChecks() {
    try {
      console.log('[AI侧边栏] 开始初始化状态检查...');

      // 延迟一段时间确保后台服务已完全初始化
      setTimeout(async () => {
        try {
          // 检查API状态
          await this.checkApiStatus();

          // 检查Notion状态
          await this.checkNotionStatus();

          console.log('[AI侧边栏] 状态检查完成');

          // 设置定期状态更新（每30秒检查一次Notion状态）
          this.setupPeriodicStatusUpdates();

        } catch (error) {
          console.error('[AI侧边栏] 状态检查失败:', error);
        }
      }, 2000); // 延迟2秒确保后台服务初始化完成

    } catch (error) {
      console.error('[AI侧边栏] 初始化状态检查失败:', error);
    }
  }

  /**
   * @function setupPeriodicStatusUpdates - 设置定期状态更新
   * @description 定期检查Notion知识库状态，确保状态指示器实时更新
   */
  setupPeriodicStatusUpdates() {
    // 每30秒检查一次Notion状态
    setInterval(async () => {
      try {
        await this.checkNotionStatus();
      } catch (error) {
        console.warn('[AI侧边栏] 定期状态检查失败:', error);
      }
    }, 30000); // 30秒间隔
  }
}

// 初始化侧边栏面板
document.addEventListener('DOMContentLoaded', () => {
  try {
    window.aiSidebarPanel = new AiSidebarPanel();
    console.log('✅ AI侧边栏对话中心化面板已成功初始化');
  } catch (error) {
    console.error('❌ AI侧边栏面板初始化失败:', error);
  }
});

// 导出类以供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AiSidebarPanel;
}