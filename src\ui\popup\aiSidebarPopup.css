/**
 * @file AI侧边栏弹窗样式文件
 * @description 弹窗控制中心的样式定义，采用BEM命名规范
 */

/* #region 基础变量 */
:root {
  /* 继承侧边栏的设计变量 */
  --ai-primary-color: #2563eb;
  --ai-primary-color-light: #3b82f6;
  --ai-primary-color-dark: #1d4ed8;
  --ai-success-color: #10b981;
  --ai-warning-color: #f59e0b;
  --ai-error-color: #ef4444;
  
  --ai-bg-primary: #ffffff;
  --ai-bg-secondary: #f8fafc;
  --ai-bg-tertiary: #f1f5f9;
  
  --ai-text-primary: #0f172a;
  --ai-text-secondary: #64748b;
  --ai-text-muted: #94a3b8;
  --ai-text-white: #ffffff;
  
  --ai-border-color: #e2e8f0;
  --ai-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --ai-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  --ai-radius-sm: 4px;
  --ai-radius-md: 8px;
  --ai-radius-lg: 12px;
  
  --ai-spacing-xs: 4px;
  --ai-spacing-sm: 8px;
  --ai-spacing-md: 16px;
  --ai-spacing-lg: 24px;
  
  --ai-transition-fast: 0.15s ease-out;
  --ai-transition-normal: 0.25s ease-out;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  width: 320px;
  background: var(--ai-bg-primary);
}
/* #endregion */

/* #region 主容器 */
.ai-popup {
  width: 100%;
  background: var(--ai-bg-primary);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
  box-shadow: var(--ai-shadow-lg);
}
/* #endregion */

/* #region 头部 */
.ai-popup__header {
  padding: var(--ai-spacing-md);
  background: var(--ai-bg-secondary);
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-popup__logo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ai-popup__logo-text {
  font-weight: 600;
  font-size: 16px;
  color: var(--ai-text-primary);
}

.ai-popup__version {
  font-size: 12px;
  color: var(--ai-text-muted);
  background: var(--ai-bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--ai-radius-sm);
}
/* #endregion */

/* #region 连接状态 */
.ai-popup__status {
  padding: var(--ai-spacing-md);
  border-bottom: 1px solid var(--ai-border-color);
}

.ai-popup__status-item {
  display: flex;
  align-items: center;
  gap: var(--ai-spacing-sm);
  font-size: 13px;
  color: var(--ai-text-secondary);
}

.ai-popup__status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--ai-text-muted);
}

.ai-popup__status-indicator--connected {
  background: var(--ai-success-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.ai-popup__status-indicator--disconnected {
  background: var(--ai-error-color);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.ai-popup__status-indicator--connecting {
  background: var(--ai-warning-color);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: ai-pulse 2s infinite;
}

@keyframes ai-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
/* #endregion */

/* #region 快速操作 */
.ai-popup__actions {
  padding: var(--ai-spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--ai-spacing-sm);
}

.ai-popup__action-btn {
  width: 100%;
  padding: var(--ai-spacing-md);
  border: 1px solid var(--ai-border-color);
  border-radius: var(--ai-radius-md);
  background: var(--ai-bg-primary);
  color: var(--ai-text-primary);
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--ai-spacing-sm);
  transition: all var(--ai-transition-fast);
}

.ai-popup__action-btn:hover {
  border-color: var(--ai-primary-color);
  background: var(--ai-bg-secondary);
}

.ai-popup__action-btn--primary {
  border-color: var(--ai-primary-color);
  background: var(--ai-primary-color);
  color: var(--ai-text-white);
}

.ai-popup__action-btn--primary:hover {
  background: var(--ai-primary-color-light);
}

.ai-popup__icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
  flex-shrink: 0;
}
/* #endregion */

/* #region 工具和设置 */
.ai-popup__tools {
  padding: var(--ai-spacing-md);
  border-top: 1px solid var(--ai-border-color);
  background: var(--ai-bg-secondary);
  display: flex;
  gap: var(--ai-spacing-sm);
}

.ai-popup__tool-btn {
  flex: 1;
  padding: var(--ai-spacing-sm);
  border: none;
  border-radius: var(--ai-radius-md);
  background: transparent;
  color: var(--ai-text-secondary);
  font-size: 12px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--ai-spacing-xs);
  transition: all var(--ai-transition-fast);
}

.ai-popup__tool-btn:hover {
  background: var(--ai-bg-tertiary);
  color: var(--ai-text-primary);
}

.ai-popup__tool-btn .ai-popup__icon {
  width: 18px;
  height: 18px;
}
/* #endregion */

/* #region 底部信息 */
.ai-popup__footer {
  padding: var(--ai-spacing-sm) var(--ai-spacing-md);
  background: var(--ai-bg-tertiary);
  border-top: 1px solid var(--ai-border-color);
}

.ai-popup__info {
  font-size: 12px;
  color: var(--ai-text-muted);
  text-align: center;
}
/* #endregion */

/* #region 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --ai-bg-primary: #0f172a;
    --ai-bg-secondary: #1e293b;
    --ai-bg-tertiary: #334155;
    --ai-text-primary: #f8fafc;
    --ai-text-secondary: #cbd5e1;
    --ai-text-muted: #94a3b8;
    --ai-border-color: #334155;
  }
}
/* #endregion */ 