/**
 * @file AI侧边栏后台服务工作器
 * @description 管理Chrome扩展的后台服务，包括消息路由、API管理、存储管理等核心功能
 */

import { AiSidebarCore } from '../core/aiSidebarCore.js';
import { AiApiManager } from '../integrations/aiApiManager.js';
import { AiSecurityManager } from '../security/aiSecurityManager.js';
import { AiNotionConnector } from '../integrations/aiNotionConnector.js';
import { AiSettingsManager } from '../core/aiSettingsManager.js';
import { AiAdvancedAnalyzer } from '../analysis/aiAdvancedAnalyzer.js';
// 引入统一的消息类型常量，避免魔法字符串
import { MESSAGE_TYPES } from '../shared/messageTypes.js';

// 光标增强器 - 使用条件导入以处理潜在的模块格式问题
let AiCursorEnhancer = null;

// 异步加载光标增强器模块
async function loadCursorEnhancer() {
  try {
    const cursorModule = await import('../enhancements/aiCursorEnhancer.js');
    AiCursorEnhancer = cursorModule.AiCursorEnhancer;
    console.log('[Service Worker] ✅ AiCursorEnhancer 模块加载成功');
    return true;
  } catch (error) {
    console.warn('[Service Worker] ⚠️ AiCursorEnhancer 模块加载失败，将跳过相关功能:', error.message);
    return false;
  }
}

// #region 全局变量定义
/**
 * 侧边栏核心实例
 * @type {AiSidebarCore}
 */
let aiSidebarCoreInstance = null;

/**
 * API管理器实例
 * @type {AiApiManager}
 */
let apiManagerInstance = null;

/**
 * 安全管理器实例
 * @type {AiSecurityManager}
 */
let securityManagerInstance = null;

/**
 * Notion连接器实例
 * @type {AiNotionConnector}
 */
let notionConnectorInstance = null;

/**
 * 设置管理器实例
 * @type {AiSettingsManager}
 */
let settingsManagerInstance = null;

/**
 * 光标增强器实例
 * @type {AiCursorEnhancer}
 */
let cursorEnhancerInstance = null;

/**
 * 高级分析器实例
 * @type {AiAdvancedAnalyzer}
 */
let advancedAnalyzerInstance = null;
// #endregion

// #region 服务工作器初始化
/**
 * @function initAiSidebarServiceWorker - 初始化AI侧边栏后台服务
 * @description 启动后台服务，初始化核心组件和消息监听器
 * @returns {Promise<void>}
 */
async function initAiSidebarServiceWorker() {
  try {
    console.log('[AI侧边栏] 后台服务工作器正在启动...');
    
    // 使用新的核心模块进行统一初始化
    aiSidebarCoreInstance = new AiSidebarCore();
    await aiSidebarCoreInstance.init();
    
    // 从核心模块获取子模块实例
    securityManagerInstance = aiSidebarCoreInstance.securityManager;
    apiManagerInstance = aiSidebarCoreInstance.apiManager;
    
    // 初始化第三阶段新模块
    await initThirdPhaseModules();
    
    // 设置消息监听器
    setupMessageListeners();
    
    // 设置侧边栏管理
    setupSidePanelManagement();
    
    // 监听核心模块事件
    setupCoreEventListeners();
    
    console.log('[AI侧边栏] 后台服务工作器启动完成');
  } catch (error) {
    console.error('[AI侧边栏] 后台服务工作器启动失败:', error);
    // 记录错误到存储以便调试
    await chrome.storage.local.set({
      'ai_sidebar_last_error': {
        timestamp: Date.now(),
        error: error.message,
        stack: error.stack
      }
    });
  }
}

/**
 * @function initThirdPhaseModules - 初始化第三阶段模块
 * @description 初始化Notion集成、设置管理和光标增强功能
 * @returns {Promise<void>}
 */
async function initThirdPhaseModules() {
  console.log('[AI侧边栏] 正在初始化第三阶段模块...');

  // 初始化设置管理器（需要安全管理器）
  try {
    settingsManagerInstance = new AiSettingsManager(securityManagerInstance);
    await settingsManagerInstance.init();
    console.log('[AI侧边栏] ✅ 设置管理器初始化成功');
  } catch (error) {
    console.warn('[AI侧边栏] ⚠️ 设置管理器初始化失败，但这是可选功能:', error.message);
    settingsManagerInstance = null;
  }

  // 初始化Notion连接器（需要安全管理器）
  try {
    notionConnectorInstance = new AiNotionConnector(securityManagerInstance);
    await notionConnectorInstance.init();
    console.log('[AI侧边栏] ✅ Notion连接器初始化成功');
  } catch (error) {
    console.warn('[AI侧边栏] ⚠️ Notion连接器初始化失败，但这是可选功能:', error.message);
    notionConnectorInstance = null;
  }

  // 注意：光标增强器已移到Content Script中初始化
  // 因为它需要直接访问DOM，在Background Service Worker中无法运行
  console.log('[AI侧边栏] 光标增强器将在Content Script中初始化');

  // 初始化高级分析器（需要API管理器）
  try {
    if (!apiManagerInstance) {
      throw new Error('API管理器未正确初始化');
    }
    advancedAnalyzerInstance = new AiAdvancedAnalyzer(apiManagerInstance);
    await advancedAnalyzerInstance.init();
    console.log('[AI侧边栏] ✅ 高级分析器初始化成功');
  } catch (error) {
    console.warn('[AI侧边栏] ⚠️ 高级分析器初始化失败，但这是可选功能:', error.message);
    advancedAnalyzerInstance = null;
  }

  // 初始化光标增强器模块
  try {
    const cursorLoaded = await loadCursorEnhancer();
    if (cursorLoaded) {
      console.log('[AI侧边栏] ✅ 光标增强器模块加载成功');
    }
  } catch (error) {
    console.warn('[AI侧边栏] ⚠️ 光标增强器模块加载失败，但这是可选功能:', error.message);
  }

  console.log('[AI侧边栏] 第三阶段模块初始化完成');
}
// #endregion

// #region 消息通信管理
/**
 * @function setupMessageListeners - 设置消息监听器
 * @description 建立与content scripts和UI组件之间的消息通信
 */
function setupMessageListeners() {
  // 监听来自content scripts和UI的消息
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    handleIncomingMessage(message, sender, sendResponse);
    return true; // 保持消息通道开放以支持异步响应
  });
  
  // 监听连接请求
  chrome.runtime.onConnect.addListener((port) => {
    handlePortConnection(port);
  });
}

/**
 * @function handleIncomingMessage - 处理接收到的消息
 * @description 根据消息类型路由到相应的处理函数
 * @param {Object} message - 接收到的消息对象
 * @param {chrome.runtime.MessageSender} sender - 消息发送者信息
 * @param {Function} sendResponse - 响应函数
 * @returns {Promise<void>}
 */
async function handleIncomingMessage(message, sender, sendResponse) {
  try {
    // 防御式编程：验证消息格式
    if (!message || typeof message !== 'object') {
      console.warn('[AI侧边栏] 收到无效消息格式:', message);
      sendResponse({
        success: false,
        error: '无效的消息格式',
        requestId: null
      });
      return;
    }

    const { type, payload } = message;

    // 验证消息类型
    if (!type || typeof type !== 'string') {
      console.warn('[AI侧边栏] 消息缺少类型字段:', message);
      sendResponse({
        success: false,
        error: '消息缺少类型字段',
        requestId: message.requestId
      });
      return;
    }

    console.log(`[AI侧边栏] 收到消息类型: ${type}`, payload);

    let response = {
      success: true,
      requestId: message.requestId || null,
      data: null
    };
    
    switch (type) {
      // 内容脚本消息
      case MESSAGE_TYPES.CONTENT_CAPTURED:
        await handleContentCaptured(payload, sender);
        break;

      // 聊天消息
      case MESSAGE_TYPES.CHAT_SEND:
      case MESSAGE_TYPES.CHAT_STREAM:
        await handleChatMessage(payload, sender);
        break;

      // 分析请求
      case MESSAGE_TYPES.ANALYSIS_REQUEST:
        await handleAnalysisRequest(payload, sender);
        break;

      // 模板操作
      case MESSAGE_TYPES.TEMPLATE_GENERATE_REPLY:
      case MESSAGE_TYPES.TEMPLATE_SUGGEST:
      case MESSAGE_TYPES.TEMPLATE_USE:
        await handleTemplateOperation(payload, sender, type);
        break;

      // 智能回复
      case MESSAGE_TYPES.SMART_REPLY_GENERATE:
        await handleSmartReplyGeneration(payload, sender);
        break;
      
      // API配置
      case MESSAGE_TYPES.API_CONFIGURE:
        await handleApiConfiguration(payload, sender);
        break;

      // 状态请求
      case MESSAGE_TYPES.STATUS_REQUEST:
        await handleStatusRequest(payload, sender);
        break;

      // API状态检查
      case 'CHECK_GEMINI_API_STATUS':
        response = await handleGeminiApiStatusCheck(payload, sender);
        break;

      // Notion状态检查
      case 'CHECK_NOTION_STATUS':
        response = await handleNotionStatusCheck(payload, sender);
        break;

      // 内容分析
      case 'CONTENT_ANALYSIS':
        response = await handleContentAnalysis(payload, sender);
        break;
      
      // 配置获取/设置 (旧版兼容)
      case MESSAGE_TYPES.CONFIG_GET:
        await handleConfigGet(payload, sender);
        break;
      case MESSAGE_TYPES.CONFIG_SET:
        await handleConfigSet(payload, sender);
        break;

      // 侧边栏开关
      case MESSAGE_TYPES.SIDEBAR_TOGGLE:
        await handleSidebarToggle(payload, sender);
        break;

      // Notion 操作
      case MESSAGE_TYPES.NOTION_CONNECT:
      case MESSAGE_TYPES.NOTION_SYNC:
      case MESSAGE_TYPES.NOTION_CACHE_CLEAR:
      case MESSAGE_TYPES.NOTION_CACHE_STATUS:
      case MESSAGE_TYPES.NOTION_DATABASES_GET:
        await handleNotionOperation(payload, sender, type);
        break;

      // 设置操作
      case MESSAGE_TYPES.SETTINGS_GET:
      case MESSAGE_TYPES.SETTINGS_SET:
      case MESSAGE_TYPES.SETTINGS_EXPORT:
      case MESSAGE_TYPES.SETTINGS_IMPORT:
      case MESSAGE_TYPES.SETTINGS_RESET:
        await handleSettingsOperation(payload, sender, type);
        break;

      // 光标增强
      case MESSAGE_TYPES.CURSOR_ENHANCE_TOGGLE:
      case MESSAGE_TYPES.CURSOR_ENHANCE_STATUS:
        await handleCursorEnhancement(payload, sender, type);
        break;

      // 高级分析
      case MESSAGE_TYPES.ANALYSIS_COMPARE:
      case MESSAGE_TYPES.ANALYSIS_TREND:
        await handleAdvancedAnalysis(payload, sender, type);
        break;
      
      // 测试通知
      case MESSAGE_TYPES.TEST_NOTIFICATION:
        await handleTestNotification(payload, sender);
        break;
      
      // 创建通知
      case MESSAGE_TYPES.NOTIFICATION_CREATE:
        await handleNotificationCreate(payload, sender);
        break;

      // 获取状态
      case MESSAGE_TYPES.STATUS_GET:
        await handleStatusGet(payload, sender);
        break;

      // 测试高级回退
      case MESSAGE_TYPES.TEST_ADVANCED_FALLBACK:
        await handleAdvancedFallbackTest(payload, sender);
        break;

      // 内容操作
      case MESSAGE_TYPES.CONTENT_SUMMARIZE:
      case MESSAGE_TYPES.CONTENT_ANALYZE:
        await handleContentOperation(payload, sender, type);
        break;

      // 工作流操作
      case MESSAGE_TYPES.WORKFLOW_EXECUTE:
        await handleWorkflowOperation(payload, sender, type);
        break;
      
      // 国际化操作
      case MESSAGE_TYPES.I18N_TRANSLATE:
        await handleI18nOperation(payload, sender, type);
        break;
      
      // 协作操作
      case MESSAGE_TYPES.COLLABORATION_SHARE:
        await handleCollaborationOperation(payload, sender, type);
        break;

      // 光标增强操作
      case MESSAGE_TYPES.CURSOR_ENHANCE_TOGGLE:
      case MESSAGE_TYPES.CURSOR_ENHANCE_STATUS:
        await handleCursorEnhanceOperation(payload, sender, type);
        break;

      // 侧边栏状态操作
      case MESSAGE_TYPES.SIDEBAR_STATUS:
      case MESSAGE_TYPES.STATUS_GET:
        await handleStatusOperation(payload, sender, type);
        break;

      // 通知操作
      case MESSAGE_TYPES.NOTIFICATION_CREATE:
      case MESSAGE_TYPES.TEST_NOTIFICATION:
        await handleNotificationOperation(payload, sender, type);
        break;

      default:
        response.success = false;
        response.error = `未知消息类型: ${type}`;
        console.warn(`[AI侧边栏] 收到未处理的消息类型: ${type}`, payload);
    }
    
    sendResponse(response);
  } catch (error) {
    console.error('[AI侧边栏] 消息处理错误:', error);
    sendResponse({
      success: false,
      error: error.message,
      requestId: message.requestId
    });
  }
}

/**
 * @function handlePortConnection - 处理端口连接
 * @description 处理长连接请求，用于实时数据传输
 * @param {chrome.runtime.Port} port - 连接端口
 */
function handlePortConnection(port) {
  console.log(`[AI侧边栏] 建立端口连接: ${port.name}`);
  
  port.onMessage.addListener(async (message) => {
    try {
      if (message.type === 'ai:chat:stream') {
        // 处理流式聊天消息
        await handleStreamingChat(message.data, port);
      }
    } catch (error) {
      port.postMessage({
        type: 'error',
        error: error.message
      });
    }
  });
  
  port.onDisconnect.addListener(() => {
    console.log(`[AI侧边栏] 端口连接断开: ${port.name}`);
  });
}
// #endregion

// #region 消息处理器函数
/**
 * @function handleContentCaptured - 处理内容捕获消息
 * @description 处理从content script捕获的页面内容
 * @param {Object} data - 捕获的内容数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 处理结果
 */
async function handleContentCaptured(data, sender) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  // 添加标签页ID到数据中
  const pageData = { ...data, tabId: sender.tab.id };
  return await aiSidebarCoreInstance.analyzePageContent(pageData);
}

/**
 * @function handleChatMessage - 处理聊天消息
 * @description 处理用户发送的聊天消息
 * @param {Object} data - 聊天消息数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} AI响应结果
 */
async function handleChatMessage(data, sender) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  return await aiSidebarCoreInstance.handleChatMessage(data, sender.tab.id);
}

/**
 * @function handleAnalysisRequest - 处理分析请求
 * @description 处理内容分析请求
 * @param {Object} data - 分析请求数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 分析结果
 */
async function handleAnalysisRequest(data, sender) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  const pageData = { ...data, tabId: sender.tab.id };
  return await aiSidebarCoreInstance.analyzePageContent(pageData, data.options);
}

/**
 * @function handleTemplateOperation - 处理模板操作
 * @description 处理模板的增删改查操作
 * @param {Object} data - 操作数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @param {string} type - 操作类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleTemplateOperation(data, sender, type) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  const action = type.split(':')[2]; // 从 'ai:template:create' 中提取 'create'
  return await aiSidebarCoreInstance.manageTemplate(action, data.templateId, data);
}

/**
 * @function handleSmartReplyGeneration - 处理智能回复生成
 * @description 生成基于上下文的智能回复建议
 * @param {Object} data - 上下文数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 回复建议结果
 */
async function handleSmartReplyGeneration(data, sender) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  return await aiSidebarCoreInstance.generateSmartReply(data.contextData, data.options);
}

/**
 * @function handleApiConfiguration - 处理API配置
 * @description 配置AI服务API密钥
 * @param {Object} data - 配置数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 配置结果
 */
async function handleApiConfiguration(data, sender) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  return await aiSidebarCoreInstance.configureApiKey(data.provider, data.apiKey);
}

/**
 * @function handleStatusRequest - 处理状态请求
 * @description 获取系统状态信息
 * @param {Object} data - 请求数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 状态信息
 */
async function handleStatusRequest(data, sender) {
  if (!aiSidebarCoreInstance) {
    return {
      success: false,
      error: 'AI侧边栏核心服务未初始化'
    };
  }
  
  return {
    success: true,
    status: aiSidebarCoreInstance.getSystemStatus()
  };
}

/**
 * @function handleConfigGet - 处理配置获取
 * @description 获取用户配置
 * @param {Object} data - 请求数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 配置数据
 */
async function handleConfigGet(data, sender) {
  const config = await chrome.storage.sync.get(['ai_sidebar_config']);
  return config.ai_sidebar_config || {};
}

/**
 * @function handleConfigSet - 处理配置设置
 * @description 设置用户配置
 * @param {Object} data - 配置数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 设置结果
 */
async function handleConfigSet(data, sender) {
  await chrome.storage.sync.set({ 'ai_sidebar_config': data });
  return { success: true };
}

/**
 * @function handleSidebarToggle - 处理侧边栏切换
 * @description 切换侧边栏显示状态或直接打开侧边栏
 * @param {Object} data - 切换数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 切换结果
 */
async function handleSidebarToggle(data, sender) {
  try {
    const tabId = sender.tab.id;

    // 如果没有指定enabled状态，则直接打开侧边栏
    if (data.enabled === undefined) {
      console.log('[AI侧边栏] 收到打开侧边栏请求');

      // 使用安全的侧边栏打开方法
      return await openSidebarSafely(tabId, 'message_request');
    } else {
      // 传统的启用/禁用逻辑
      await chrome.sidePanel.setOptions({
        tabId: tabId,
        enabled: data.enabled
      });

      // 如果启用了侧边栏，尝试安全打开它
      if (data.enabled) {
        try {
          const result = await openSidebarSafely(tabId, 'enable_request');
          return {
            success: true,
            enabled: data.enabled,
            visible: result.visible,
            action: result.action
          };
        } catch (openError) {
          console.warn('[AI侧边栏] 启用后无法打开侧边栏:', openError.message);
          return { success: true, enabled: data.enabled, visible: false };
        }
      }

      return { success: true, enabled: data.enabled, visible: data.enabled };
    }
  } catch (error) {
    console.error('[AI侧边栏] 侧边栏操作失败:', error);
    throw error;
  }
}

/**
 * @function handleStreamingChat - 处理流式聊天
 * @description 处理需要流式响应的聊天消息
 * @param {Object} data - 聊天数据
 * @param {chrome.runtime.Port} port - 连接端口
 * @returns {Promise<void>}
 */
async function handleStreamingChat(data, port) {
  if (!aiSidebarCoreInstance) {
    throw new Error('AI侧边栏核心服务未初始化');
  }
  
  // 注册流式连接端口
  if (data.tabId) {
    registerStreamingPort(data.tabId, port);
  }
  
  // 设置流式消息数据
  const messageData = {
    ...data,
    isStreaming: true
  };
  
  try {
    // 使用核心模块处理流式聊天
    const result = await aiSidebarCoreInstance.handleChatMessage(messageData, data.tabId);
    
    // 发送最终完成信号
    port.postMessage({
      type: 'ai:chat:stream:complete',
      data: result
    });
  } catch (error) {
    // 发送错误信号
    port.postMessage({
      type: 'ai:chat:stream:error',
      error: error.message
    });
  }
}
// #endregion

// #region 侧边栏管理

/**
 * @function validateNotificationOptions - 验证通知选项
 * @description 确保所有必需属性都存在且有效
 * @param {Object} options - 通知选项
 * @returns {Object} 验证后的选项
 */
function validateNotificationOptions(options) {
  const validatedOptions = {
    type: 'basic',
    title: 'AI侧边栏助手',
    message: '操作已完成',
    ...options
  };

  // 验证必需属性
  if (!validatedOptions.type) {
    validatedOptions.type = 'basic';
    console.log('[AI侧边栏] 补充缺失的type属性');
  }

  if (!validatedOptions.title || validatedOptions.title.trim() === '') {
    validatedOptions.title = 'AI侧边栏助手';
    console.log('[AI侧边栏] 补充缺失的title属性');
  }

  if (!validatedOptions.message || validatedOptions.message.trim() === '') {
    validatedOptions.message = '操作已完成';
    console.log('[AI侧边栏] 补充缺失的message属性');
  }

  // 验证图标路径
  if (validatedOptions.iconUrl) {
    const validIcons = [
      'assets/icons/icon-16.png',
      'assets/icons/icon-32.png',
      'assets/icons/icon-48.png',
      'assets/icons/icon-128.png'
    ];

    if (!validIcons.includes(validatedOptions.iconUrl)) {
      console.warn('[AI侧边栏] 无效图标路径，使用默认图标:', validatedOptions.iconUrl);
      validatedOptions.iconUrl = 'assets/icons/icon-48.png';
    }
  }

  return validatedOptions;
}

/**
 * @function validatePNGIcon - 验证PNG图标文件
 * @description 检查PNG文件是否可用于通知
 * @param {string} iconPath - 图标文件路径
 * @returns {Promise<boolean>} 图标是否有效
 */
async function validatePNGIcon(iconPath) {
  try {
    // 尝试创建一个简单的测试通知来验证图标
    const testId = await chrome.notifications.create({
      type: 'basic',
      iconUrl: iconPath,
      title: 'Test',
      message: 'Test'
    });

    // 立即清除测试通知
    if (testId) {
      await chrome.notifications.clear(testId);
      return true;
    }
    return false;
  } catch (error) {
    console.warn(`[AI侧边栏] PNG图标验证失败: ${iconPath}`, error.message);
    return false;
  }
}

/**
 * @function createSafeNotification - 安全创建通知 (增强版)
 * @description 包含完整的PNG处理和多层fallback机制
 * @param {object} options - 调用者提供的通知选项
 * @returns {Promise<string|null>} 成功时返回通知ID，失败时返回null
 */
async function createSafeNotification(options) {
  console.log('[AI侧边栏] createSafeNotification 被调用，参数:', options);

  // 验证和补充必需属性
  const validatedOptions = validateNotificationOptions(options);
  console.log('[AI侧边栏] 验证后的选项:', validatedOptions);

  // 定义多个可用的图标选项 (按优先级排序)
  const iconOptions = [
    'assets/icons/icon-48.png',    // 主要选择 (48x48)
    'assets/icons/icon-32.png',    // 备用选择1 (32x32)
    'assets/icons/icon-16.png',    // 备用选择2 (16x16)
    'assets/icons/icon-128.png',   // 备用选择3 (128x128)
    null                           // 无图标选择
  ];

  const baseOptions = {
    type: validatedOptions.type,
    title: validatedOptions.title,
    message: validatedOptions.message
  };

  // 尝试每个图标选项
  for (let i = 0; i < iconOptions.length; i++) {
    const iconUrl = iconOptions[i];
    const finalOptions = iconUrl ?
      { ...baseOptions, iconUrl } :
      { ...baseOptions };

    try {
      const notificationId = await chrome.notifications.create(finalOptions);

      if (iconUrl) {
        console.log(`[AI侧边栏] ✅ 通知创建成功，使用图标: ${iconUrl} (ID: ${notificationId})`);
      } else {
        console.log(`[AI侧边栏] ✅ 无图标通知创建成功 (ID: ${notificationId})`);
      }

      return notificationId;
    } catch (error) {
      if (iconUrl) {
        console.warn(`[AI侧边栏] ⚠️ 图标 ${iconUrl} 创建通知失败: ${error.message}`);
      } else {
        console.error(`[AI侧边栏] ❌ 无图标通知也失败: ${error.message}`);
      }

      // 如果不是最后一个选项，继续尝试下一个
      if (i < iconOptions.length - 1) {
        continue;
      }
    }
  }

  console.error('[AI侧边栏] ❌ 所有通知创建方式都失败');
  return null;
}

/**
 * @function createNotificationWithAdvancedFallback - 高级fallback通知创建
 * @description 包含SVG和base64图标的完整fallback机制
 * @param {object} options - 通知选项
 * @returns {Promise<string|null>} 通知ID或null
 */
async function createNotificationWithAdvancedFallback(options) {
  console.log('[AI侧边栏] 尝试高级fallback通知创建');

  // 首先尝试标准PNG图标
  const pngResult = await createSafeNotification(options);
  if (pngResult) return pngResult;

  console.warn('[AI侧边栏] PNG图标全部失败，尝试SVG图标');

  // PNG失败，尝试SVG (如果存在)
  try {
    const svgOptions = {
      type: 'basic',
      title: 'AI侧边栏助手',
      message: '操作已完成。',
      ...options,
      iconUrl: 'assets/icons/icon.svg'
    };

    const svgResult = await chrome.notifications.create(svgOptions);
    console.log('[AI侧边栏] ✅ SVG图标通知创建成功');
    return svgResult;
  } catch (svgError) {
    console.warn('[AI侧边栏] ⚠️ SVG图标也失败:', svgError.message);
  }

  console.warn('[AI侧边栏] SVG失败，尝试base64内嵌图标');

  // 最后尝试base64内嵌图标 (1x1像素透明PNG)
  try {
    const base64Icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

    const base64Options = {
      type: 'basic',
      title: 'AI侧边栏助手',
      message: '操作已完成。',
      ...options,
      iconUrl: base64Icon
    };

    const base64Result = await chrome.notifications.create(base64Options);
    console.log('[AI侧边栏] ✅ Base64图标通知创建成功');
    return base64Result;
  } catch (base64Error) {
    console.error('[AI侧边栏] ❌ Base64图标也失败:', base64Error.message);
  }

  console.error('[AI侧边栏] ❌ 所有高级fallback方案都失败');
  return null;
}

/**
 * @function isUserGestureContext - 检查是否在用户手势上下文中
 * @description 尝试检测当前是否有有效的用户手势上下文
 * @returns {boolean} 是否可能有用户手势上下文
 */
function isUserGestureContext() {
  // 这是一个启发式检查，Chrome没有直接的API来检测用户手势上下文
  // 我们可以检查调用栈或其他指标，但这里使用简单的时间窗口检查
  return true; // 暂时总是返回true，依赖try-catch处理
}

/**
 * @function openSidebarSafely - 安全打开侧边栏
 * @description 在保持用户手势上下文的情况下安全打开侧边栏
 * @param {number} tabId - 标签页ID
 * @param {string} context - 调用上下文（用于日志记录）
 * @param {boolean} skipValidation - 是否跳过页面验证（用于用户直接点击）
 * @returns {Promise<Object>} 操作结果
 */
async function openSidebarSafely(tabId, context = 'unknown', skipValidation = false) {
  console.log(`[AI侧边栏] openSidebarSafely 调用来源: ${context}`);
  if (!tabId) {
    throw new Error('无效的标签页ID');
  }

  try {
    // 首先验证标签页ID
    let tab;
    try {
      tab = await chrome.tabs.get(tabId);
      if (!tab) {
        throw new Error(`标签页不存在: ${tabId}`);
      }
    } catch (tabError) {
      console.warn(`[AI侧边栏] 标签页验证失败 (${tabId}):`, tabError.message);
      throw new Error(`标签页不可用: ${tabId}`);
    }

    // 如果不跳过验证，先检查页面是否支持侧边栏
    if (!skipValidation) {
      const shouldShow = await shouldShowSidebarForTab(tabId);
      if (!shouldShow) {
        await createSafeNotification({
          title: 'AI侧边栏助手',
          message: '当前页面不支持AI侧边栏功能'
        });
        return {
          success: false,
          visible: false,
          action: 'blocked',
          context: context,
          message: '页面不支持侧边栏'
        };
      }
    }

    // 确保侧边栏已启用，然后尝试打开
    try {
      // 首先确保侧边栏已启用
      await chrome.sidePanel.setOptions({
        tabId: tabId,
        enabled: true
      });

      // 然后尝试打开侧边栏
      await chrome.sidePanel.open({ tabId: tabId });
      console.log(`[AI侧边栏] 侧边栏成功打开 (上下文: ${context})`);

      return {
        success: true,
        visible: true,
        action: 'opened',
        context: context
      };
    } catch (openError) {
      // 如果是"No active side panel"错误，尝试重新初始化
      if (openError.message.includes('No active side panel')) {
        console.log(`[AI侧边栏] 检测到侧边栏未激活，尝试重新初始化 (标签页: ${tabId})`);

        // 重新设置侧边栏配置
        await chrome.sidePanel.setOptions({
          tabId: tabId,
          path: 'src/ui/sidebar/aiSidebar.html',
          enabled: true
        });

        // 短暂延迟后再次尝试打开
        await new Promise(resolve => setTimeout(resolve, 100));
        await chrome.sidePanel.open({ tabId: tabId });

        console.log(`[AI侧边栏] 侧边栏重新初始化后成功打开 (上下文: ${context})`);
        return {
          success: true,
          visible: true,
          action: 'reopened',
          context: context
        };
      } else {
        // 其他类型的打开错误，重新抛出
        throw openError;
      }
    }

  } catch (error) {
    console.warn(`[AI侧边栏] 无法打开侧边栏 (上下文: ${context}):`, error.message);

    // 检查是否是用户手势错误
    if (error.message.includes('user gesture')) {
      console.log(`[AI侧边栏] 用户手势上下文丢失，尝试启用侧边栏供手动打开`);

      try {
        // 作为备用方案，至少启用侧边栏
        await chrome.sidePanel.setOptions({
          tabId: tabId,
          enabled: true
        });

        // 显示提示通知
        await createSafeNotification({
          title: 'AI侧边栏助手',
          message: '侧边栏已启用，请点击浏览器侧边栏图标打开'
        });

        return {
          success: true,
          visible: false,
          action: 'enabled',
          context: context,
          message: '侧边栏已启用，需要手动打开'
        };

      } catch (enableError) {
        console.error(`[AI侧边栏] 启用侧边栏也失败:`, enableError);
        throw enableError;
      }
    } else {
      // 其他类型的错误
      throw error;
    }
  }
}

/**
 * @function setupSidePanelManagement - 设置侧边栏管理
 * @description 配置侧边栏的显示和管理逻辑
 */
function setupSidePanelManagement() {
  // 监听扩展图标点击事件
  chrome.action.onClicked.addListener(async (tab) => {
    try {
      console.log('[AI侧边栏] 扩展图标被点击，准备打开侧边栏');

      // 立即尝试打开侧边栏，跳过页面验证以保持用户手势上下文
      const result = await openSidebarSafely(tab.id, 'user_click', true);

      // 如果因为页面不支持而被阻止，显示相应通知
      if (!result.success && result.action === 'blocked') {
        console.log('[AI侧边栏] 用户点击被阻止:', result.message);
      }

    } catch (error) {
      console.error('[AI侧边栏] 处理图标点击失败:', error);

      // 显示错误通知
      await createSafeNotification({
        title: 'AI侧边栏助手',
        message: '打开侧边栏失败，请重试'
      });
    }
  });

  // 监听标签页激活
  chrome.tabs.onActivated.addListener(async (activeInfo) => {
    try {
      console.log(`[AI侧边栏] 标签页激活: ${activeInfo.tabId}`);

      // 检查当前标签页是否应该显示侧边栏
      const shouldShow = await shouldShowSidebarForTab(activeInfo.tabId);

      // 设置侧边栏选项，包括路径以确保正确初始化
      await chrome.sidePanel.setOptions({
        tabId: activeInfo.tabId,
        path: 'src/ui/sidebar/aiSidebar.html',
        enabled: shouldShow
      });

      console.log(`[AI侧边栏] 标签页 ${activeInfo.tabId} 侧边栏状态: ${shouldShow ? '启用' : '禁用'}`);

    } catch (error) {
      console.error('[AI侧边栏] 标签页切换处理错误:', error);

      // 如果设置失败，尝试基本的启用操作
      try {
        await chrome.sidePanel.setOptions({
          tabId: activeInfo.tabId,
          enabled: false
        });
        console.log(`[AI侧边栏] 标签页 ${activeInfo.tabId} 侧边栏已禁用（备用方案）`);
      } catch (fallbackError) {
        console.error('[AI侧边栏] 标签页切换备用方案也失败:', fallbackError);
      }
    }
  });
  
  // 监听标签页更新
  chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
      try {
        console.log(`[AI侧边栏] 标签页更新完成: ${tabId}, URL: ${tab.url}`);

        const shouldShow = await shouldShowSidebarForTab(tabId);

        // 设置侧边栏选项，包括路径以确保正确初始化
        await chrome.sidePanel.setOptions({
          tabId: tabId,
          path: 'src/ui/sidebar/aiSidebar.html',
          enabled: shouldShow
        });

        console.log(`[AI侧边栏] 标签页 ${tabId} 更新后侧边栏状态: ${shouldShow ? '启用' : '禁用'}`);

        // 如果侧边栏应该显示，触发自动分析
        if (shouldShow) {
          // 延迟一段时间确保页面内容完全加载
          setTimeout(async () => {
            try {
              console.log(`[AI侧边栏] 触发标签页 ${tabId} 的自动分析`);

              // 向侧边栏发送自动分析消息
              await chrome.runtime.sendMessage({
                type: 'TRIGGER_AUTO_ANALYSIS',
                tabId: tabId,
                url: tab.url,
                timestamp: Date.now()
              });

              console.log(`[AI侧边栏] 已发送自动分析触发消息给标签页 ${tabId}`);
            } catch (analysisError) {
              console.warn('[AI侧边栏] 触发自动分析失败:', analysisError);
            }
          }, 2000); // 延迟2秒确保页面内容加载完成
        }

      } catch (error) {
        console.error('[AI侧边栏] 标签页更新处理错误:', error);

        // 如果设置失败，尝试基本的禁用操作
        try {
          await chrome.sidePanel.setOptions({
            tabId: tabId,
            enabled: false
          });
          console.log(`[AI侧边栏] 标签页 ${tabId} 更新后侧边栏已禁用（备用方案）`);
        } catch (fallbackError) {
          console.error('[AI侧边栏] 标签页更新备用方案也失败:', fallbackError);
        }
      }
    }
  });
}

/**
 * @function shouldShowSidebarForTab - 判断标签页是否应该显示侧边栏
 * @description 根据页面类型和用户配置判断是否显示侧边栏
 * @param {number} tabId - 标签页ID
 * @returns {Promise<boolean>} 是否显示侧边栏
 */
async function shouldShowSidebarForTab(tabId) {
  try {
    // 验证tabId是否有效
    if (!tabId || typeof tabId !== 'number') {
      console.warn('[AI侧边栏] 无效的标签页ID:', tabId);
      return false;
    }

    const tab = await chrome.tabs.get(tabId);

    // 检查tab对象是否有效
    if (!tab) {
      console.warn('[AI侧边栏] 无法获取标签页信息:', tabId);
      return false;
    }

    const url = tab.url;

    // 检查URL是否存在且有效
    if (!url || typeof url !== 'string') {
      console.warn('[AI侧边栏] 标签页URL无效:', { tabId, url });
      return false;
    }

    // 排除特殊页面
    if (url.startsWith('chrome://') ||
        url.startsWith('chrome-extension://') ||
        url.startsWith('moz-extension://') ||
        url.startsWith('edge://') ||
        url.startsWith('about:') ||
        url === 'chrome://newtab/' ||
        url === 'about:blank') {
      console.log('[AI侧边栏] 跳过特殊页面:', url);
      return false;
    }

    // 检查用户配置
    const config = await chrome.storage.sync.get(['ai_sidebar_enabled']);
    const isEnabled = config.ai_sidebar_enabled !== false; // 默认启用

    console.log('[AI侧边栏] 侧边栏显示检查结果:', { tabId, url, isEnabled });
    return isEnabled;

  } catch (error) {
    console.error('[AI侧边栏] 检查侧边栏显示状态错误:', error);

    // 如果是标签页不存在的错误，返回false
    if (error.message && error.message.includes('No tab with id')) {
      console.warn('[AI侧边栏] 标签页不存在:', tabId);
      return false;
    }

    // 其他错误时默认显示
    return true;
  }
}
// #endregion

// #region 核心事件监听
/**
 * @function setupCoreEventListeners - 设置核心模块事件监听
 * @description 监听核心模块触发的事件并进行相应处理
 */
function setupCoreEventListeners() {
  if (!aiSidebarCoreInstance) return;
  
  // 监听核心初始化完成事件
  aiSidebarCoreInstance.on('core:initialized', (data) => {
    console.log('[AI侧边栏] 核心模块初始化完成:', data);
    
    // 通知所有连接的UI组件
    chrome.runtime.sendMessage({
      type: 'ai:core:initialized',
      data: data
    }).catch(() => {
      // 忽略没有接收者的错误
    });
  });
  
  // 监听分析完成事件
  aiSidebarCoreInstance.on('analysis:completed', (data) => {
    console.log('[AI侧边栏] 内容分析完成:', data.tabId);
    
    // 通知对应标签页的UI组件
    chrome.tabs.sendMessage(data.tabId, {
      type: 'ai:analysis:completed',
      data: data.result
    }).catch(() => {
      // 忽略没有接收者的错误
    });
  });
  
  // 监听聊天流式响应事件
  aiSidebarCoreInstance.on('chat:streaming', (data) => {
    // 通过端口发送流式数据（如果有活动连接）
    if (activeStreamingPorts.has(data.tabId)) {
      const port = activeStreamingPorts.get(data.tabId);
      port.postMessage({
        type: 'ai:chat:stream:chunk',
        data: {
          chunk: data.chunk,
          fullText: data.fullText,
          isComplete: data.isComplete
        }
      });
    }
  });
  
  // 监听回复生成完成事件
  aiSidebarCoreInstance.on('reply:generated', (data) => {
    console.log('[AI侧边栏] 智能回复生成完成');
    
    // 广播回复建议（可以被多个UI组件接收）
    chrome.runtime.sendMessage({
      type: 'ai:reply:suggestions',
      data: data
    }).catch(() => {
      // 忽略没有接收者的错误
    });
  });
  
  // 监听模板操作事件
  aiSidebarCoreInstance.on('template:operation', (data) => {
    console.log(`[AI侧边栏] 模板${data.action}操作完成:`, data.templateId);
    
    // 通知UI组件更新模板列表
    chrome.runtime.sendMessage({
      type: 'ai:template:updated',
      data: {
        action: data.action,
        templateId: data.templateId,
        result: data.result
      }
    }).catch(() => {
      // 忽略没有接收者的错误
    });
  });
  
  // 监听API配置事件
  aiSidebarCoreInstance.on('api:configured', (data) => {
    console.log(`[AI侧边栏] ${data.provider} API配置完成`);
    
    // 通知UI组件API配置状态变化
    chrome.runtime.sendMessage({
      type: 'ai:api:status_changed',
      data: {
        provider: data.provider,
        configured: true,
        timestamp: data.timestamp
      }
    }).catch(() => {
      // 忽略没有接收者的错误
    });
  });
  
  // 监听用户偏好更新事件
  aiSidebarCoreInstance.on('preferences:updated', (data) => {
    console.log(`[AI侧边栏] 用户偏好更新: ${data.key} = ${data.value}`);
    
    // 通知UI组件偏好变化
    chrome.runtime.sendMessage({
      type: 'ai:preferences:updated',
      data: data
    }).catch(() => {
      // 忽略没有接收者的错误
    });
  });
}
// #endregion

// #region 流式连接管理
/**
 * 活动的流式连接端口映射
 * @type {Map<number, chrome.runtime.Port>}
 */
const activeStreamingPorts = new Map();

/**
 * @function registerStreamingPort - 注册流式连接端口
 * @description 为指定标签页注册流式连接端口
 * @param {number} tabId - 标签页ID
 * @param {chrome.runtime.Port} port - 连接端口
 */
function registerStreamingPort(tabId, port) {
  activeStreamingPorts.set(tabId, port);
  
  port.onDisconnect.addListener(() => {
    activeStreamingPorts.delete(tabId);
    console.log(`[AI侧边栏] 标签页 ${tabId} 的流式连接已断开`);
  });
}
// #endregion

// #region 错误处理和清理
/**
 * @function handleServiceWorkerShutdown - 处理服务工作器关闭
 * @description 清理资源和保存状态
 */
chrome.runtime.onSuspend.addListener(() => {
  console.log('[AI侧边栏] 后台服务工作器正在关闭，清理资源...');
  
  // 清理流式连接
  activeStreamingPorts.clear();
  
  // 清理实例
  if (aiSidebarCoreInstance) {
    aiSidebarCoreInstance.cleanup();
  }
  
  if (apiManagerInstance) {
    apiManagerInstance.cleanup();
  }
  
  if (securityManagerInstance) {
    securityManagerInstance.cleanup();
  }
  
  // 清理第三阶段模块
  if (notionConnectorInstance) {
    notionConnectorInstance.cleanup();
  }
  
  if (settingsManagerInstance) {
    settingsManagerInstance.cleanup();
  }
  
  if (cursorEnhancerInstance) {
    cursorEnhancerInstance.cleanup();
  }
  
  if (advancedAnalyzerInstance) {
    advancedAnalyzerInstance.cleanup();
  }
});

/**
 * @function handleUnhandledError - 处理未捕获的错误
 * @description 全局错误处理器
 * @param {Error} error - 错误对象
 */
self.addEventListener('error', async (event) => {
  console.error('[AI侧边栏] 未捕获的错误:', event.error);
  
  // 记录错误
  try {
    await chrome.storage.local.set({
      'ai_sidebar_unhandled_errors': {
        timestamp: Date.now(),
        error: event.error.message,
        stack: event.error.stack,
        filename: event.filename,
        lineno: event.lineno
      }
    });
  } catch (storageError) {
    console.error('[AI侧边栏] 错误记录失败:', storageError);
  }
});
// #endregion

// #region 第三阶段模块处理器
/**
 * @function handleNotionOperation - 处理Notion操作
 * @description 处理Notion集成相关的操作请求
 * @param {Object} data - 操作数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @param {string} type - 操作类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleNotionOperation(data, sender, type) {
  try {
    if (!notionConnectorInstance) {
      throw new Error('Notion连接器未初始化');
    }
    
    switch (type) {
      case 'ai:notion:connect':
        return await notionConnectorInstance.connectToNotion(data.authCode);

      case 'ai:notion:sync':
        return await notionConnectorInstance.syncChatHistory(data.chatHistory, data.options);

      case 'ai:notion:search':
        return await notionConnectorInstance.searchKnowledgeBase(data.query, data.options);

      case 'ai:notion:create':
        return await notionConnectorInstance.createNotionPage(data.title, data.content, data.options);

      case 'ai:notion:disconnect':
        return await notionConnectorInstance.disconnect();

      // 缓存管理操作
      case 'ai:notion:cache:update':
        if (notionConnectorInstance.cacheManager) {
          return await notionConnectorInstance.cacheManager.updateCache(
            notionConnectorInstance,
            data.forceUpdate || false
          );
        } else {
          throw new Error('缓存管理器未初始化');
        }

      case 'ai:notion:cache:clear':
        if (notionConnectorInstance.cacheManager) {
          return await notionConnectorInstance.cacheManager.clearCache();
        } else {
          throw new Error('缓存管理器未初始化');
        }

      case 'ai:notion:cache:status':
        if (notionConnectorInstance.cacheManager) {
          return {
            success: true,
            status: notionConnectorInstance.cacheManager.getCacheStatus()
          };
        } else {
          throw new Error('缓存管理器未初始化');
        }

      default:
        throw new Error(`未支持的Notion操作: ${type}`);
    }
  } catch (error) {
    console.error(`[AI侧边栏] Notion操作失败 (${type}):`, error);
    throw error;
  }
}

/**
 * @function handleSettingsOperation - 处理设置操作
 * @description 处理用户设置相关的操作请求
 * @param {Object} data - 操作数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @param {string} type - 操作类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleSettingsOperation(data, sender, type) {
  try {
    // 检查设置管理器是否已初始化，如果没有则尝试初始化
    if (!settingsManagerInstance) {
      console.warn('[AI侧边栏] 设置管理器未初始化，尝试延迟初始化...');

      // 等待一段时间让初始化完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (!settingsManagerInstance) {
        // 如果是初始化请求，返回默认设置
        if (data.action === 'initialize') {
          return {
            success: true,
            message: '设置管理器正在初始化中',
            settings: {} // 返回空设置对象
          };
        }
        throw new Error('设置管理器未初始化');
      }
    }

    // 处理初始化请求
    if (data.action === 'initialize') {
      return {
        success: true,
        message: '设置管理器已初始化',
        settings: await settingsManagerInstance.getAllSettings()
      };
    }

    switch (type) {
      case 'ai:settings:get':
        if (data.key) {
          return await settingsManagerInstance.getSetting(data.key);
        } else {
          return await settingsManagerInstance.getAllSettings();
        }

      case 'ai:settings:set':
        return await settingsManagerInstance.updateSetting(data.key, data.value);

      case 'ai:settings:reset':
        if (data.key) {
          return await settingsManagerInstance.resetSetting(data.key);
        } else {
          return await settingsManagerInstance.resetAllSettings();
        }
        
      case 'ai:settings:export':
        return await settingsManagerInstance.exportSettings();
        
      case 'ai:settings:import':
        return await settingsManagerInstance.importSettings(data.settings);
        
      default:
        throw new Error(`未支持的设置操作: ${type}`);
    }
  } catch (error) {
    console.error(`[AI侧边栏] 设置操作失败 (${type}):`, error);
    throw error;
  }
}

/**
 * @function handleCursorEnhancement - 处理光标增强操作
 * @description 将光标增强消息转发到Content Script处理
 * @param {Object} data - 操作数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @param {string} type - 操作类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleCursorEnhancement(data, sender, type) {
  try {
    // 处理初始化请求（来自侧边栏面板）
    if (data.action === 'initialize') {
      console.log('[AI侧边栏] 处理光标增强器初始化请求');

      // 对于初始化请求，我们不需要转发到特定标签页
      // 而是返回成功状态，让侧边栏知道功能可用
      return {
        success: true,
        message: '光标增强器已准备就绪',
        features: ['智能预测', '自动补全', '上下文感知']
      };
    }

    // 对于其他操作，需要转发到Content Script
    const tabId = sender.tab?.id;

    // 如果没有标签页ID，尝试获取当前活动标签页
    let targetTabId = tabId;
    if (!targetTabId) {
      try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length > 0) {
          targetTabId = tabs[0].id;
          console.log(`[AI侧边栏] 使用当前活动标签页: ${targetTabId}`);
        } else {
          throw new Error('无法找到活动标签页');
        }
      } catch (tabError) {
        console.warn('[AI侧边栏] 无法获取活动标签页:', tabError);

        // 对于无法获取标签页的情况，返回功能不可用的状态
        return {
          success: false,
          error: '光标增强功能需要在网页标签页中使用',
          message: '请在网页中使用此功能'
        };
      }
    }

    // 将消息转发到Content Script中的光标增强器
    try {
      const response = await chrome.tabs.sendMessage(targetTabId, {
        type: 'ai:cursor:forward',
        originalType: type,
        data: data
      });

      return response;
    } catch (sendError) {
      console.warn('[AI侧边栏] 消息转发失败:', sendError);

      // 如果转发失败，可能是因为Content Script未加载
      return {
        success: false,
        error: '光标增强功能暂时不可用',
        message: '请刷新页面后重试'
      };
    }

  } catch (error) {
    console.error(`[AI侧边栏] 光标增强操作失败 (${type}):`, error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleAdvancedAnalysis - 处理高级分析操作
 * @description 处理高级分析相关的操作请求
 * @param {Object} data - 操作数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @param {string} type - 操作类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleAdvancedAnalysis(data, sender, type) {
  try {
    // 检查高级分析器是否已初始化，如果没有则尝试初始化
    if (!advancedAnalyzerInstance) {
      console.warn('[AI侧边栏] 高级分析器未初始化，尝试延迟初始化...');

      // 等待一段时间让初始化完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (!advancedAnalyzerInstance) {
        // 如果是初始化请求，返回成功状态
        if (data.action === 'initialize') {
          return {
            success: true,
            message: '高级分析器正在初始化中'
          };
        }
        throw new Error('高级分析器未初始化');
      }
    }

    // 处理初始化请求
    if (data.action === 'initialize') {
      return {
        success: true,
        message: '高级分析器已初始化'
      };
    }

    switch (type) {
      case 'ai:analysis:compare':
        // 检查是否有足够的页面进行对比
        if (!data.pages || data.pages.length < 2) {
          throw new Error('至少需要两个页面进行对比分析');
        }
        return await advancedAnalyzerInstance.comparePages(data.pages, data.options);
        
      case 'ai:analysis:trend':
        return await advancedAnalyzerInstance.generateTrendReport(data.options);
        
      case 'ai:analysis:visualize':
        return await advancedAnalyzerInstance.generateVisualization(data.analysisResult, data.options);
        
      case 'ai:analysis:export':
        return await advancedAnalyzerInstance.exportAnalysis(data.analysisResult, data.options);
        
      default:
        throw new Error(`未支持的高级分析操作: ${type}`);
    }
  } catch (error) {
    console.error(`[AI侧边栏] 高级分析操作失败 (${type}):`, error);
    throw error;
  }
}

/**
 * @function handleTestNotification - 处理测试通知
 * @description 用于测试通知系统的修复效果
 * @param {Object} data - 通知数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 测试结果
 */
async function handleTestNotification(data, sender) {
  try {
    console.log('[AI侧边栏] 收到测试通知请求:', data);

    const notificationId = await createSafeNotification({
      title: data.title || '测试通知',
      message: data.message || '这是一个从后台发送的测试通知。'
    });

    return {
      success: true,
      notificationId: notificationId,
      message: '增强通知发送成功'
    };
  } catch (error) {
    console.error('[AI侧边栏] 测试通知发送失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleNotificationCreate - 处理通知创建请求
 * @description 统一的通知创建处理器，确保属性完整性
 * @param {Object} data - 通知数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 创建结果
 */
async function handleNotificationCreate(data, sender) {
  try {
    console.log('[AI侧边栏] 收到通知创建请求:', data);

    // 验证选项
    const validatedOptions = validateNotificationOptions(data);

    // 使用增强的通知创建函数
    const notificationId = await createSafeNotification(validatedOptions);

    return {
      success: true,
      notificationId: notificationId,
      message: '通知创建成功'
    };
  } catch (error) {
    console.error('[AI侧边栏] 通知创建失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleStatusGet - 处理状态获取请求
 * @description 获取Service Worker和各模块的初始化状态
 * @param {Object} data - 状态查询数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 状态信息
 */
async function handleStatusGet(data, sender) {
  try {
    console.log('[AI侧边栏] 收到状态查询请求:', data);

    const component = data.component || 'all';
    const statusInfo = {
      timestamp: Date.now(),
      component: component
    };

    switch (component) {
      case 'service-worker':
        statusInfo.initialized = true;
        statusInfo.modules = {
          core: !!aiSidebarCoreInstance,
          api: !!apiManagerInstance,
          security: !!securityManagerInstance,
          settings: !!settingsManagerInstance,
          notion: !!notionConnectorInstance,
          analysis: !!advancedAnalyzerInstance
        };
        break;

      case 'all':
        statusInfo.serviceWorker = {
          initialized: true,
          modules: {
            core: !!aiSidebarCoreInstance,
            api: !!apiManagerInstance,
            security: !!securityManagerInstance,
            settings: !!settingsManagerInstance,
            notion: !!notionConnectorInstance,
            analysis: !!advancedAnalyzerInstance
          }
        };
        break;

      default:
        throw new Error(`未知的状态组件: ${component}`);
    }

    return {
      success: true,
      data: statusInfo,
      message: '状态查询成功'
    };
  } catch (error) {
    console.error('[AI侧边栏] 状态查询失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleAdvancedFallbackTest - 处理高级fallback测试
 * @description 测试SVG和Base64 fallback功能
 * @param {Object} data - 测试数据
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 测试结果
 */
async function handleAdvancedFallbackTest(data, sender) {
  try {
    console.log('[AI侧边栏] 收到高级fallback测试请求:', data);

    const notificationId = await createNotificationWithAdvancedFallback({
      title: data.title || '高级Fallback测试',
      message: data.message || '测试SVG和Base64 fallback功能'
    });

    return {
      success: true,
      notificationId: notificationId,
      message: '高级fallback测试成功'
    };
  } catch (error) {
    console.error('[AI侧边栏] 高级fallback测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
// #endregion

/**
 * @function handleContentOperation - 处理内容操作
 * @description 处理内容相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleContentOperation(data, sender, type) {
  try {
    switch (type) {
      case 'ai:content:summarize':
        return {
          success: true,
          data: {
            summary: '内容摘要功能将在Content Script中实现',
            message: '内容摘要已生成'
          }
        };

      case 'ai:content:analyze':
        return {
          success: true,
          data: {
            analysis: '内容分析功能将在Content Script中实现',
            message: '内容分析已完成'
          }
        };

      case 'ai:content:extract':
        return {
          success: true,
          data: {
            extracted: {},
            message: '内容提取功能将在Content Script中实现'
          }
        };

      default:
        throw new Error(`未支持的内容操作: ${type}`);
    }
  } catch (error) {
    console.error('[AI侧边栏] 内容操作处理失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleWorkflowOperation - 处理工作流操作
 * @description 处理工作流相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleWorkflowOperation(data, sender, type) {
  try {
    switch (type) {
      case 'ai:workflow:create':
        return {
          success: true,
          data: {
            workflowId: 'workflow_' + Date.now(),
            message: '工作流创建功能将在Content Script中实现'
          }
        };

      case 'ai:workflow:execute':
        return {
          success: true,
          data: {
            executed: true,
            message: '工作流执行功能将在Content Script中实现'
          }
        };

      case 'ai:workflow:list':
        return {
          success: true,
          data: {
            workflows: [],
            message: '工作流列表功能将在Content Script中实现'
          }
        };

      case 'ai:workflow:delete':
        return {
          success: true,
          data: {
            deleted: true,
            message: '工作流删除功能将在Content Script中实现'
          }
        };

      default:
        throw new Error(`未支持的工作流操作: ${type}`);
    }
  } catch (error) {
    console.error('[AI侧边栏] 工作流操作处理失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleI18nOperation - 处理国际化操作
 * @description 处理国际化相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleI18nOperation(data, sender, type) {
  try {
    switch (type) {
      case 'ai:i18n:set-language':
        return {
          success: true,
          data: {
            language: data.language || 'zh-CN',
            message: '语言设置功能将在Content Script中实现'
          }
        };

      case 'ai:i18n:get-languages':
        return {
          success: true,
          data: {
            languages: ['zh-CN', 'en-US', 'ja-JP', 'es-ES'],
            message: '支持的语言列表'
          }
        };

      case 'ai:i18n:translate':
        return {
          success: true,
          data: {
            translated: data.text || '',
            message: '翻译功能将在Content Script中实现'
          }
        };

      default:
        throw new Error(`未支持的国际化操作: ${type}`);
    }
  } catch (error) {
    console.error('[AI侧边栏] 国际化操作处理失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleCollaborationOperation - 处理协作操作
 * @description 处理协作相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleCollaborationOperation(data, sender, type) {
  try {
    switch (type) {
      case 'ai:collaboration:create':
        return {
          success: true,
          data: {
            sessionId: 'session_' + Date.now(),
            message: '协作会话创建功能将在Content Script中实现'
          }
        };

      case 'ai:collaboration:join':
        return {
          success: true,
          data: {
            joined: true,
            message: '加入协作功能将在Content Script中实现'
          }
        };

      case 'ai:collaboration:share':
        return {
          success: true,
          data: {
            shared: true,
            message: '内容分享功能将在Content Script中实现'
          }
        };

      default:
        throw new Error(`未支持的协作操作: ${type}`);
    }
  } catch (error) {
    console.error('[AI侧边栏] 协作操作处理失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * @function handleCursorEnhanceOperation - 处理光标增强操作
 * @description 处理光标增强相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleCursorEnhanceOperation(data, sender, type) {
  try {
    switch (type) {
      case MESSAGE_TYPES.CURSOR_ENHANCE_TOGGLE:
        // 切换光标增强功能状态
        const isEnabled = data?.enabled !== undefined ? data.enabled : true;
        return {
          success: true,
          data: {
            enabled: isEnabled,
            message: `光标增强功能已${isEnabled ? '启用' : '禁用'}`
          }
        };

      case MESSAGE_TYPES.CURSOR_ENHANCE_STATUS:
        // 获取光标增强功能状态
        return {
          success: true,
          data: {
            enabled: true, // 默认启用状态
            features: {
              smartCompletion: true,
              contextAware: true,
              multiLanguage: false
            },
            message: '光标增强功能状态已获取'
          }
        };

      default:
        throw new Error(`未支持的光标增强操作: ${type}`);
    }
  } catch (error) {
    console.error(`[AI侧边栏] 光标增强操作失败 (${type}):`, error);
    return {
      success: false,
      error: error.message,
      data: {
        enabled: false,
        message: '光标增强功能暂时不可用'
      }
    };
  }
}

/**
 * @function handleStatusOperation - 处理状态操作
 * @description 处理状态查询相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleStatusOperation(data, sender, type) {
  try {
    switch (type) {
      case MESSAGE_TYPES.SIDEBAR_STATUS:
        // 获取侧边栏状态
        return {
          success: true,
          data: {
            isOpen: true,
            isCollapsed: false,
            currentPanel: 'chat',
            message: '侧边栏状态已获取'
          }
        };

      case MESSAGE_TYPES.STATUS_GET:
        // 获取整体状态
        return {
          success: true,
          data: {
            version: '1.0.0',
            status: 'active',
            modules: {
              core: 'initialized',
              api: 'connected',
              notion: 'disconnected',
              cursor: 'enabled'
            },
            message: '系统状态已获取'
          }
        };

      default:
        throw new Error(`未支持的状态操作: ${type}`);
    }
  } catch (error) {
    console.error(`[AI侧边栏] 状态操作失败 (${type}):`, error);
    return {
      success: false,
      error: error.message,
      data: {
        status: 'error',
        message: '状态查询失败'
      }
    };
  }
}

/**
 * @function handleNotificationOperation - 处理通知操作
 * @description 处理通知相关的消息
 * @param {Object} data - 请求数据
 * @param {Object} sender - 发送者信息
 * @param {string} type - 消息类型
 * @returns {Promise<Object>} 操作结果
 */
async function handleNotificationOperation(data, sender, type) {
  try {
    switch (type) {
      case MESSAGE_TYPES.NOTIFICATION_CREATE:
        // 创建通知
        const notificationOptions = {
          type: 'basic',
          iconUrl: 'icons/icon-48.png',
          title: data?.title || 'AI侧边栏助手',
          message: data?.message || '操作已完成'
        };

        // 使用Chrome通知API创建通知
        if (chrome.notifications) {
          const notificationId = await chrome.notifications.create(notificationOptions);
          return {
            success: true,
            data: {
              notificationId,
              message: '通知已创建'
            }
          };
        } else {
          throw new Error('通知API不可用');
        }

      case MESSAGE_TYPES.TEST_NOTIFICATION:
        // 测试通知
        const testOptions = {
          type: 'basic',
          iconUrl: 'icons/icon-48.png',
          title: 'AI侧边栏测试',
          message: '这是一个测试通知'
        };

        if (chrome.notifications) {
          const testId = await chrome.notifications.create(testOptions);
          return {
            success: true,
            data: {
              notificationId: testId,
              message: '测试通知已发送'
            }
          };
        } else {
          throw new Error('通知API不可用');
        }

      default:
        throw new Error(`未支持的通知操作: ${type}`);
    }
  } catch (error) {
    console.error(`[AI侧边栏] 通知操作失败 (${type}):`, error);
    return {
      success: false,
      error: error.message,
      data: {
        message: '通知创建失败'
      }
    };
  }
}

/**
 * @function handleGeminiApiStatusCheck - 处理Gemini API状态检查
 * @description 检查Gemini API的连接状态
 * @param {Object} payload - 请求载荷
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 检查结果
 */
async function handleGeminiApiStatusCheck(payload, sender) {
  try {
    console.log('[AI侧边栏] 检查Gemini API状态...');

    // 获取API管理器实例
    if (!globalApiManager) {
      return {
        success: false,
        error: 'API管理器未初始化'
      };
    }

    // 验证API密钥
    await globalApiManager.validateApiKeys();

    // 尝试发送一个简单的测试请求
    const testResponse = await globalApiManager.sendChatMessage('测试连接', {
      maxTokens: 10,
      temperature: 0.1
    });

    if (testResponse && testResponse.success) {
      console.log('[AI侧边栏] Gemini API状态检查成功');
      return {
        success: true,
        data: {
          status: 'connected',
          model: testResponse.model || 'gemini-2.5-flash-lite-preview-06-17'
        }
      };
    } else {
      return {
        success: false,
        error: 'API响应异常'
      };
    }
  } catch (error) {
    console.error('[AI侧边栏] Gemini API状态检查失败:', error);
    return {
      success: false,
      error: error.message || 'API连接失败'
    };
  }
}

/**
 * @function handleNotionStatusCheck - 处理Notion状态检查
 * @description 检查Notion集成的连接状态和知识库加载状态
 * @param {Object} payload - 请求载荷
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 检查结果
 */
async function handleNotionStatusCheck(payload, sender) {
  try {
    console.log('[AI侧边栏] 检查Notion状态...');

    // 获取Notion连接器实例
    if (!globalNotionConnector) {
      return {
        success: false,
        error: 'Notion连接器未初始化'
      };
    }

    // 检查连接状态
    const isConnected = globalNotionConnector.isConnected;

    if (!isConnected) {
      // 尝试连接
      try {
        const connectResult = await globalNotionConnector.connectToNotion();
        if (!connectResult.success) {
          return {
            success: false,
            error: connectResult.error || 'Notion连接失败'
          };
        }
      } catch (connectError) {
        return {
          success: false,
          error: connectError.message || 'Notion连接失败'
        };
      }
    }

    // 检查知识库状态
    let knowledgeBaseLoaded = false;
    let knowledgeCount = 0;

    try {
      // 尝试获取知识库缓存状态
      if (globalNotionConnector.cacheManager) {
        const cacheStatus = await globalNotionConnector.cacheManager.getCacheStatus();
        knowledgeBaseLoaded = cacheStatus.isValid && cacheStatus.itemCount > 0;
        knowledgeCount = cacheStatus.itemCount || 0;
      }
    } catch (cacheError) {
      console.warn('[AI侧边栏] 获取知识库状态失败:', cacheError);
    }

    console.log('[AI侧边栏] Notion状态检查完成');
    return {
      success: true,
      data: {
        connected: true,
        knowledgeBaseLoaded,
        knowledgeCount
      },
      knowledgeBaseLoaded,
      knowledgeCount
    };

  } catch (error) {
    console.error('[AI侧边栏] Notion状态检查失败:', error);
    return {
      success: false,
      error: error.message || 'Notion状态检查失败'
    };
  }
}

/**
 * @function handleContentAnalysis - 处理内容分析请求
 * @description 使用高级分析器进行内容分析
 * @param {Object} payload - 分析请求载荷
 * @param {chrome.runtime.MessageSender} sender - 消息发送者
 * @returns {Promise<Object>} 分析结果
 */
async function handleContentAnalysis(payload, sender) {
  try {
    console.log('[AI侧边栏] 开始内容分析...');

    // 检查高级分析器是否可用
    if (!advancedAnalyzerInstance) {
      console.warn('[AI侧边栏] 高级分析器未初始化，尝试使用API管理器直接分析');

      // 如果高级分析器不可用，使用API管理器进行基础分析
      if (!apiManagerInstance) {
        return {
          success: false,
          error: '分析服务不可用'
        };
      }

      // 使用API管理器进行基础内容分析
      const basicAnalysisResult = await performBasicContentAnalysis(payload);
      return basicAnalysisResult;
    }

    // 使用高级分析器进行分析
    const { content, pageInfo, analysisType } = payload;

    // 构建分析请求
    const analysisRequest = {
      content: content,
      url: pageInfo.url,
      title: pageInfo.title,
      timestamp: pageInfo.timestamp,
      options: {
        includeKeywords: true,
        includeSentiment: true,
        includeStructure: true,
        includeSuggestions: true,
        analysisDepth: analysisType === 'auto' ? 'basic' : 'detailed'
      }
    };

    // 执行分析
    const analysisResult = await advancedAnalyzerInstance.analyzeContent(analysisRequest);

    if (analysisResult && analysisResult.success) {
      console.log('[AI侧边栏] 内容分析完成');
      return {
        success: true,
        data: analysisResult.data
      };
    } else {
      console.warn('[AI侧边栏] 高级分析失败，尝试基础分析');
      const basicAnalysisResult = await performBasicContentAnalysis(payload);
      return basicAnalysisResult;
    }

  } catch (error) {
    console.error('[AI侧边栏] 内容分析失败:', error);

    // 尝试基础分析作为备用方案
    try {
      const basicAnalysisResult = await performBasicContentAnalysis(payload);
      return basicAnalysisResult;
    } catch (fallbackError) {
      console.error('[AI侧边栏] 基础分析也失败:', fallbackError);
      return {
        success: false,
        error: error.message || '内容分析失败'
      };
    }
  }
}

/**
 * @function performBasicContentAnalysis - 执行基础内容分析
 * @description 使用API管理器进行基础的内容分析
 * @param {Object} payload - 分析请求载荷
 * @returns {Promise<Object>} 分析结果
 */
async function performBasicContentAnalysis(payload) {
  try {
    const { content, pageInfo } = payload;

    // 构建分析提示
    const analysisPrompt = `请分析以下网页内容，并提供结构化的分析结果：

网页标题：${pageInfo.title}
网页URL：${pageInfo.url}

内容：
${content.substring(0, 2000)}${content.length > 2000 ? '...' : ''}

请提供以下分析：
1. 内容摘要（100字以内）
2. 关键词（5-8个）
3. 情感倾向（正面/中性/负面）
4. 主要观点或要点
5. 实用建议

请以JSON格式返回结果。`;

    // 调用API管理器
    const response = await apiManagerInstance.sendChatMessage(analysisPrompt, {
      maxTokens: 1000,
      temperature: 0.3
    });

    if (response && response.success) {
      // 尝试解析JSON响应
      let analysisData;
      try {
        analysisData = JSON.parse(response.data);
      } catch (parseError) {
        // 如果不是JSON格式，创建基础结构
        analysisData = {
          summary: response.data.substring(0, 200),
          keywords: ['内容分析', '网页', '信息'],
          sentiment: '中性',
          suggestions: ['查看完整内容', '深入了解相关主题']
        };
      }

      return {
        success: true,
        data: analysisData
      };
    } else {
      throw new Error('API调用失败');
    }

  } catch (error) {
    console.error('[AI侧边栏] 基础内容分析失败:', error);
    return {
      success: false,
      error: error.message || '基础分析失败'
    };
  }
}

// 启动服务工作器
initAiSidebarServiceWorker().catch(error => {
  console.error('[AI侧边栏] 服务工作器启动失败:', error);
});