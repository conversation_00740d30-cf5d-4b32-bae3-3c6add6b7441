/**
 * @file AI高级分析器
 * @description 提供高级分析功能，包括多页面对比分析、趋势分析报告、数据可视化和导出功能
 * <AUTHOR>
 * @version 1.0.0
 */

import { AiContentAnalyzer } from './aiContentAnalyzer.js';

/**
 * @class AiAdvancedAnalyzer
 * @description AI高级分析器类，提供企业级分析功能
 */
export class AiAdvancedAnalyzer {
  // #region 构造函数和初始化
  /**
   * @constructor
   * @description 创建AI高级分析器实例
   * @param {AiApiManager} apiManager - API管理器实例
   */
  constructor(apiManager) {
    /** @type {AiApiManager} API管理器实例 */
    this.apiManager = apiManager;

    /** @type {AiContentAnalyzer} 基础内容分析器实例 */
    this.contentAnalyzer = null;
    
    /** @type {Map<string, Object>} 页面分析结果缓存 */
    this.analysisCache = new Map();
    
    /** @type {Array<Object>} 分析历史记录 */
    this.analysisHistory = [];
    
    /** @type {Object} 配置选项 */
    this.config = {
      // 缓存配置
      maxCacheSize: 100,
      cacheExpiry: 24 * 60 * 60 * 1000, // 24小时
      
      // 分析配置
      maxComparisonPages: 10,
      trendAnalysisPeriod: 30, // 30天
      
      // 导出配置
      exportFormats: ['json', 'csv', 'html', 'pdf'],
      maxExportSize: 10 * 1024 * 1024, // 10MB
      
      // 可视化配置
      chartTypes: ['line', 'bar', 'pie', 'scatter', 'heatmap'],
      maxDataPoints: 1000
    };
    
    /** @type {boolean} 初始化状态 */
    this.isInitialized = false;
    
    /** @type {Map<string, Function>} 事件监听器 */
    this.eventListeners = new Map();
    
    console.log('[AI高级分析器] 实例已创建');
  }
  
  /**
   * @method init - 初始化高级分析器
   * @description 初始化分析器并加载配置
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[AI高级分析器] 正在初始化...');

      // 验证API管理器
      if (!this.apiManager || !this.apiManager.isInitialized) {
        throw new Error('API管理器未正确初始化');
      }

      // 初始化基础内容分析器
      this.contentAnalyzer = new AiContentAnalyzer(this.apiManager);
      await this.contentAnalyzer.init();
      
      // 加载配置
      await this.loadConfiguration();
      
      // 加载分析历史
      await this.loadAnalysisHistory();
      
      // 清理过期缓存
      this.cleanupExpiredCache();
      
      this.isInitialized = true;
      this.emit('initialized', { timestamp: Date.now() });
      
      console.log('[AI高级分析器] 初始化完成');
    } catch (error) {
      console.error('[AI高级分析器] 初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * @method loadConfiguration - 加载配置
   * @description 从存储中加载用户配置
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    try {
      const result = await chrome.storage.sync.get(['ai_advanced_analyzer_config']);
      if (result.ai_advanced_analyzer_config) {
        this.config = { ...this.config, ...result.ai_advanced_analyzer_config };
      }
    } catch (error) {
      console.warn('[AI高级分析器] 配置加载失败，使用默认配置:', error);
    }
  }
  
  /**
   * @method loadAnalysisHistory - 加载分析历史
   * @description 从存储中加载分析历史记录
   * @returns {Promise<void>}
   */
  async loadAnalysisHistory() {
    try {
      const result = await chrome.storage.local.get(['ai_analysis_history']);
      if (result.ai_analysis_history) {
        this.analysisHistory = result.ai_analysis_history;
      }
    } catch (error) {
      console.warn('[AI高级分析器] 分析历史加载失败:', error);
    }
  }
  // #endregion
  
  // #region 多页面对比分析
  /**
   * @method comparePages - 多页面对比分析
   * @description 对比分析多个页面的内容和特征
   * @param {Array<Object>} pages - 页面数据数组
   * @param {Object} options - 对比选项
   * @returns {Promise<Object>} 对比分析结果
   */
  async comparePages(pages, options = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('高级分析器未初始化');
      }
      
      if (!Array.isArray(pages) || pages.length < 2) {
        throw new Error('至少需要两个页面进行对比分析');
      }
      
      if (pages.length > this.config.maxComparisonPages) {
        throw new Error(`对比页面数量不能超过 ${this.config.maxComparisonPages} 个`);
      }
      
      console.log(`[AI高级分析器] 开始对比分析 ${pages.length} 个页面`);
      
      // 分析每个页面
      const pageAnalyses = await Promise.all(
        pages.map(page => this.analyzePageForComparison(page, options))
      );
      
      // 执行对比分析
      const comparisonResult = {
        timestamp: Date.now(),
        pageCount: pages.length,
        pages: pageAnalyses,
        comparison: {
          contentSimilarity: this.calculateContentSimilarity(pageAnalyses),
          structuralDifferences: this.analyzeStructuralDifferences(pageAnalyses),
          topicAnalysis: this.analyzeTopicDistribution(pageAnalyses),
          sentimentComparison: this.compareSentiments(pageAnalyses),
          keywordAnalysis: this.compareKeywords(pageAnalyses),
          performanceMetrics: this.comparePerformanceMetrics(pageAnalyses)
        },
        insights: this.generateComparisonInsights(pageAnalyses),
        recommendations: this.generateComparisonRecommendations(pageAnalyses)
      };
      
      // 缓存结果
      this.cacheAnalysisResult('comparison', comparisonResult);
      
      // 记录到历史
      this.addToHistory('comparison', comparisonResult);
      
      this.emit('comparison:completed', comparisonResult);
      
      console.log('[AI高级分析器] 多页面对比分析完成');
      return comparisonResult;
      
    } catch (error) {
      console.error('[AI高级分析器] 多页面对比分析失败:', error);
      throw error;
    }
  }
  
  /**
   * @method analyzePageForComparison - 分析单个页面用于对比
   * @description 为对比分析准备单个页面的详细分析数据
   * @param {Object} page - 页面数据
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 页面分析结果
   */
  async analyzePageForComparison(page, options) {
    try {
      const cacheKey = `page_${page.url || page.id}_${Date.now()}`;
      
      // 检查缓存
      if (this.analysisCache.has(cacheKey)) {
        return this.analysisCache.get(cacheKey);
      }
      
      // 使用基础分析器进行内容分析
      const basicAnalysis = await this.contentAnalyzer.analyzeContent(page.content, {
        includeKeywords: true,
        includeSentiment: true,
        includeTopics: true,
        ...options
      });
      
      // 扩展分析
      const extendedAnalysis = {
        ...basicAnalysis,
        page: {
          url: page.url,
          title: page.title,
          timestamp: page.timestamp || Date.now()
        },
        structure: this.analyzePageStructure(page.content),
        readability: this.calculateReadabilityMetrics(page.content),
        seo: this.analyzeSEOMetrics(page),
        performance: this.analyzePerformanceMetrics(page),
        userExperience: this.analyzeUserExperience(page)
      };
      
      // 缓存结果
      this.analysisCache.set(cacheKey, extendedAnalysis);
      
      return extendedAnalysis;
      
    } catch (error) {
      console.error('[AI高级分析器] 页面分析失败:', error);
      throw error;
    }
  }
  
  /**
   * @method calculateContentSimilarity - 计算内容相似度
   * @description 计算多个页面之间的内容相似度
   * @param {Array<Object>} pageAnalyses - 页面分析结果数组
   * @returns {Object} 相似度分析结果
   */
  calculateContentSimilarity(pageAnalyses) {
    try {
      const similarities = [];
      
      // 计算两两之间的相似度
      for (let i = 0; i < pageAnalyses.length; i++) {
        for (let j = i + 1; j < pageAnalyses.length; j++) {
          const similarity = this.calculatePairSimilarity(
            pageAnalyses[i], 
            pageAnalyses[j]
          );
          
          similarities.push({
            pageA: i,
            pageB: j,
            titleA: pageAnalyses[i].page.title,
            titleB: pageAnalyses[j].page.title,
            similarity: similarity
          });
        }
      }
      
      // 计算平均相似度
      const averageSimilarity = similarities.reduce(
        (sum, item) => sum + item.similarity.overall, 0
      ) / similarities.length;
      
      return {
        pairwiseSimilarities: similarities,
        averageSimilarity: averageSimilarity,
        mostSimilar: similarities.reduce((max, current) => 
          current.similarity.overall > max.similarity.overall ? current : max
        ),
        leastSimilar: similarities.reduce((min, current) => 
          current.similarity.overall < min.similarity.overall ? current : min
        )
      };
      
    } catch (error) {
      console.error('[AI高级分析器] 内容相似度计算失败:', error);
      return { error: error.message };
    }
  }
  
  /**
   * @method calculatePairSimilarity - 计算两个页面的相似度
   * @description 计算两个页面在多个维度上的相似度
   * @param {Object} pageA - 页面A的分析结果
   * @param {Object} pageB - 页面B的分析结果
   * @returns {Object} 相似度结果
   */
  calculatePairSimilarity(pageA, pageB) {
    try {
      // 关键词相似度
      const keywordSimilarity = this.calculateKeywordSimilarity(
        pageA.keywords || [], 
        pageB.keywords || []
      );
      
      // 主题相似度
      const topicSimilarity = this.calculateTopicSimilarity(
        pageA.topics || [], 
        pageB.topics || []
      );
      
      // 情感相似度
      const sentimentSimilarity = this.calculateSentimentSimilarity(
        pageA.sentiment || {}, 
        pageB.sentiment || {}
      );
      
      // 结构相似度
      const structureSimilarity = this.calculateStructureSimilarity(
        pageA.structure || {}, 
        pageB.structure || {}
      );
      
      // 综合相似度
      const overall = (
        keywordSimilarity * 0.3 +
        topicSimilarity * 0.3 +
        sentimentSimilarity * 0.2 +
        structureSimilarity * 0.2
      );
      
      return {
        overall: Math.round(overall * 100) / 100,
        keywords: Math.round(keywordSimilarity * 100) / 100,
        topics: Math.round(topicSimilarity * 100) / 100,
        sentiment: Math.round(sentimentSimilarity * 100) / 100,
        structure: Math.round(structureSimilarity * 100) / 100
      };
      
    } catch (error) {
      console.error('[AI高级分析器] 相似度计算失败:', error);
      return { overall: 0, error: error.message };
    }
  }
  // #endregion
  
  // #region 趋势分析报告
  /**
   * @method generateTrendReport - 生成趋势分析报告
   * @description 基于历史数据生成趋势分析报告
   * @param {Object} options - 报告选项
   * @returns {Promise<Object>} 趋势分析报告
   */
  async generateTrendReport(options = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('高级分析器未初始化');
      }
      
      console.log('[AI高级分析器] 开始生成趋势分析报告');
      
      const {
        period = this.config.trendAnalysisPeriod,
        metrics = ['sentiment', 'keywords', 'topics', 'performance'],
        groupBy = 'day'
      } = options;
      
      // 获取历史数据
      const historicalData = await this.getHistoricalData(period);
      
      if (historicalData.length === 0) {
        throw new Error('没有足够的历史数据生成趋势报告');
      }
      
      // 生成趋势分析
      const trendReport = {
        timestamp: Date.now(),
        period: period,
        dataPoints: historicalData.length,
        metrics: {},
        insights: [],
        predictions: {},
        visualizations: {}
      };
      
      // 分析各个指标的趋势
      for (const metric of metrics) {
        trendReport.metrics[metric] = await this.analyzeTrend(
          historicalData, 
          metric, 
          groupBy
        );
      }
      
      // 生成洞察
      trendReport.insights = this.generateTrendInsights(trendReport.metrics);
      
      // 生成预测
      trendReport.predictions = this.generateTrendPredictions(trendReport.metrics);
      
      // 生成可视化数据
      trendReport.visualizations = this.generateVisualizationData(trendReport.metrics);
      
      // 缓存报告
      this.cacheAnalysisResult('trend_report', trendReport);
      
      // 记录到历史
      this.addToHistory('trend_report', trendReport);
      
      this.emit('trend:completed', trendReport);
      
      console.log('[AI高级分析器] 趋势分析报告生成完成');
      return trendReport;
      
    } catch (error) {
      console.error('[AI高级分析器] 趋势分析报告生成失败:', error);
      throw error;
    }
  }
  
  /**
   * @method getHistoricalData - 获取历史数据
   * @description 从存储中获取指定时间段的历史分析数据
   * @param {number} period - 时间段（天数）
   * @returns {Promise<Array>} 历史数据数组
   */
  async getHistoricalData(period) {
    try {
      const cutoffTime = Date.now() - (period * 24 * 60 * 60 * 1000);
      
      // 从分析历史中筛选数据
      const historicalData = this.analysisHistory.filter(
        record => record.timestamp >= cutoffTime
      );
      
      // 如果本地数据不足，尝试从扩展存储中获取更多数据
      if (historicalData.length < 10) {
        const storageResult = await chrome.storage.local.get(['ai_extended_history']);
        if (storageResult.ai_extended_history) {
          const extendedHistory = storageResult.ai_extended_history.filter(
            record => record.timestamp >= cutoffTime
          );
          historicalData.push(...extendedHistory);
        }
      }
      
      return historicalData.sort((a, b) => a.timestamp - b.timestamp);
      
    } catch (error) {
      console.error('[AI高级分析器] 历史数据获取失败:', error);
      return [];
    }
  }
  
  /**
   * @method analyzeTrend - 分析单个指标的趋势
   * @description 分析指定指标在时间序列上的变化趋势
   * @param {Array} data - 历史数据
   * @param {string} metric - 指标名称
   * @param {string} groupBy - 分组方式
   * @returns {Object} 趋势分析结果
   */
  analyzeTrend(data, metric, groupBy) {
    try {
      // 提取指标数据
      const metricData = data.map(record => ({
        timestamp: record.timestamp,
        value: this.extractMetricValue(record, metric)
      })).filter(item => item.value !== null && item.value !== undefined);
      
      if (metricData.length === 0) {
        return { error: `没有找到指标 ${metric} 的数据` };
      }
      
      // 按时间分组
      const groupedData = this.groupDataByTime(metricData, groupBy);
      
      // 计算趋势统计
      const trendStats = this.calculateTrendStatistics(groupedData);
      
      // 检测趋势模式
      const patterns = this.detectTrendPatterns(groupedData);
      
      return {
        metric: metric,
        dataPoints: metricData.length,
        groupedData: groupedData,
        statistics: trendStats,
        patterns: patterns,
        trend: this.determineTrendDirection(groupedData),
        volatility: this.calculateVolatility(groupedData),
        correlation: this.calculateAutoCorrelation(groupedData)
      };
      
    } catch (error) {
      console.error(`[AI高级分析器] 指标 ${metric} 趋势分析失败:`, error);
      return { error: error.message };
    }
  }
  // #endregion
  
  // #region 数据可视化
  /**
   * @method generateVisualization - 生成数据可视化
   * @description 为分析结果生成可视化图表数据
   * @param {Object} analysisResult - 分析结果
   * @param {Object} options - 可视化选项
   * @returns {Object} 可视化数据
   */
  generateVisualization(analysisResult, options = {}) {
    try {
      const {
        chartType = 'auto',
        dimensions = { width: 800, height: 400 },
        theme = 'default',
        interactive = true
      } = options;
      
      console.log(`[AI高级分析器] 生成 ${chartType} 类型的可视化`);
      
      const visualization = {
        timestamp: Date.now(),
        type: chartType,
        data: this.prepareVisualizationData(analysisResult, chartType),
        config: {
          dimensions: dimensions,
          theme: theme,
          interactive: interactive,
          responsive: true
        },
        metadata: {
          title: this.generateChartTitle(analysisResult, chartType),
          description: this.generateChartDescription(analysisResult),
          dataSource: analysisResult.timestamp || Date.now()
        }
      };
      
      // 根据图表类型优化配置
      this.optimizeChartConfig(visualization, analysisResult);
      
      return visualization;
      
    } catch (error) {
      console.error('[AI高级分析器] 可视化生成失败:', error);
      return { error: error.message };
    }
  }
  
  /**
   * @method prepareVisualizationData - 准备可视化数据
   * @description 将分析结果转换为图表可用的数据格式
   * @param {Object} analysisResult - 分析结果
   * @param {string} chartType - 图表类型
   * @returns {Object} 可视化数据
   */
  prepareVisualizationData(analysisResult, chartType) {
    try {
      switch (chartType) {
        case 'line':
          return this.prepareLineChartData(analysisResult);
          
        case 'bar':
          return this.prepareBarChartData(analysisResult);
          
        case 'pie':
          return this.preparePieChartData(analysisResult);
          
        case 'scatter':
          return this.prepareScatterChartData(analysisResult);
          
        case 'heatmap':
          return this.prepareHeatmapData(analysisResult);
          
        case 'auto':
          return this.prepareAutoChartData(analysisResult);
          
        default:
          throw new Error(`不支持的图表类型: ${chartType}`);
      }
    } catch (error) {
      console.error('[AI高级分析器] 可视化数据准备失败:', error);
      return { error: error.message };
    }
  }
  
  /**
   * @method prepareLineChartData - 准备折线图数据
   * @description 为折线图准备时间序列数据
   * @param {Object} analysisResult - 分析结果
   * @returns {Object} 折线图数据
   */
  prepareLineChartData(analysisResult) {
    try {
      if (analysisResult.metrics) {
        // 趋势报告的折线图
        const series = [];
        
        Object.keys(analysisResult.metrics).forEach(metric => {
          const metricData = analysisResult.metrics[metric];
          if (metricData.groupedData) {
            series.push({
              name: metric,
              data: metricData.groupedData.map(point => ({
                x: point.timestamp,
                y: point.value
              }))
            });
          }
        });
        
        return {
          series: series,
          xAxis: { type: 'datetime', title: '时间' },
          yAxis: { title: '数值' }
        };
      }
      
      // 其他类型的数据处理
      return this.extractTimeSeriesData(analysisResult);
      
    } catch (error) {
      console.error('[AI高级分析器] 折线图数据准备失败:', error);
      return { error: error.message };
    }
  }
  
  /**
   * @method prepareBarChartData - 准备柱状图数据
   * @description 为柱状图准备分类数据
   * @param {Object} analysisResult - 分析结果
   * @returns {Object} 柱状图数据
   */
  prepareBarChartData(analysisResult) {
    try {
      if (analysisResult.comparison) {
        // 对比分析的柱状图
        const categories = analysisResult.pages.map(page => page.page.title);
        const series = [];
        
        // 添加各种指标的数据系列
        if (analysisResult.comparison.sentimentComparison) {
          series.push({
            name: '正面情感',
            data: analysisResult.pages.map(page => page.sentiment?.positive || 0)
          });
          series.push({
            name: '负面情感',
            data: analysisResult.pages.map(page => page.sentiment?.negative || 0)
          });
        }
        
        return {
          categories: categories,
          series: series,
          xAxis: { title: '页面' },
          yAxis: { title: '数值' }
        };
      }
      
      // 其他类型的数据处理
      return this.extractCategoricalData(analysisResult);
      
    } catch (error) {
      console.error('[AI高级分析器] 柱状图数据准备失败:', error);
      return { error: error.message };
    }
  }
  // #endregion
  
  // #region 导出功能
  /**
   * @method exportAnalysis - 导出分析结果
   * @description 将分析结果导出为指定格式
   * @param {Object} analysisResult - 分析结果
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 导出结果
   */
  async exportAnalysis(analysisResult, options = {}) {
    try {
      const {
        format = 'json',
        filename = `analysis_${Date.now()}`,
        includeVisualizations = true,
        compress = false
      } = options;
      
      if (!this.config.exportFormats.includes(format)) {
        throw new Error(`不支持的导出格式: ${format}`);
      }
      
      console.log(`[AI高级分析器] 导出分析结果为 ${format} 格式`);
      
      let exportData;
      let mimeType;
      
      switch (format) {
        case 'json':
          exportData = await this.exportToJSON(analysisResult, options);
          mimeType = 'application/json';
          break;
          
        case 'csv':
          exportData = await this.exportToCSV(analysisResult, options);
          mimeType = 'text/csv';
          break;
          
        case 'html':
          exportData = await this.exportToHTML(analysisResult, options);
          mimeType = 'text/html';
          break;
          
        case 'pdf':
          exportData = await this.exportToPDF(analysisResult, options);
          mimeType = 'application/pdf';
          break;
          
        default:
          throw new Error(`未实现的导出格式: ${format}`);
      }
      
      // 检查文件大小
      if (exportData.length > this.config.maxExportSize) {
        throw new Error(`导出文件过大 (${exportData.length} bytes > ${this.config.maxExportSize} bytes)`);
      }
      
      // 压缩处理
      if (compress && format !== 'pdf') {
        exportData = await this.compressData(exportData);
      }
      
      const exportResult = {
        filename: `${filename}.${format}`,
        format: format,
        size: exportData.length,
        mimeType: mimeType,
        data: exportData,
        timestamp: Date.now(),
        compressed: compress
      };
      
      this.emit('export:completed', exportResult);
      
      console.log('[AI高级分析器] 分析结果导出完成');
      return exportResult;
      
    } catch (error) {
      console.error('[AI高级分析器] 分析结果导出失败:', error);
      throw error;
    }
  }
  
  /**
   * @method exportToJSON - 导出为JSON格式
   * @description 将分析结果导出为JSON格式
   * @param {Object} analysisResult - 分析结果
   * @param {Object} options - 导出选项
   * @returns {string} JSON字符串
   */
  async exportToJSON(analysisResult, options) {
    try {
      const exportData = {
        metadata: {
          exportTime: new Date().toISOString(),
          version: '1.0.0',
          source: 'AI侧边栏助手 - 高级分析器'
        },
        analysis: analysisResult
      };
      
      if (options.includeVisualizations && analysisResult.visualizations) {
        exportData.visualizations = analysisResult.visualizations;
      }
      
      return JSON.stringify(exportData, null, 2);
      
    } catch (error) {
      console.error('[AI高级分析器] JSON导出失败:', error);
      throw error;
    }
  }
  
  /**
   * @method exportToCSV - 导出为CSV格式
   * @description 将分析结果导出为CSV格式
   * @param {Object} analysisResult - 分析结果
   * @param {Object} options - 导出选项
   * @returns {string} CSV字符串
   */
  async exportToCSV(analysisResult, options) {
    try {
      const csvRows = [];
      
      // 添加标题行
      csvRows.push(['分析类型', '时间戳', '指标', '数值', '描述']);
      
      // 处理不同类型的分析结果
      if (analysisResult.comparison) {
        this.addComparisonDataToCSV(csvRows, analysisResult);
      }
      
      if (analysisResult.metrics) {
        this.addTrendDataToCSV(csvRows, analysisResult);
      }
      
      // 转换为CSV字符串
      return csvRows.map(row => 
        row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
      ).join('\n');
      
    } catch (error) {
      console.error('[AI高级分析器] CSV导出失败:', error);
      throw error;
    }
  }
  
  /**
   * @method exportToHTML - 导出为HTML格式
   * @description 将分析结果导出为HTML报告
   * @param {Object} analysisResult - 分析结果
   * @param {Object} options - 导出选项
   * @returns {string} HTML字符串
   */
  async exportToHTML(analysisResult, options) {
    try {
      const htmlTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
        .header { border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .chart-placeholder { background: #e9ecef; padding: 20px; text-align: center; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI分析报告</h1>
        <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
    </div>
    
    ${this.generateHTMLContent(analysisResult)}
    
    <div class="footer">
        <p><small>由 AI侧边栏助手 - 高级分析器 生成</small></p>
    </div>
</body>
</html>`;
      
      return htmlTemplate;
      
    } catch (error) {
      console.error('[AI高级分析器] HTML导出失败:', error);
      throw error;
    }
  }
  // #endregion
  
  // #region 工具方法
  /**
   * @method emit - 触发事件
   * @description 触发指定事件并调用所有监听器
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   */
  emit(eventName, data) {
    try {
      if (this.eventListeners.has(eventName)) {
        const listeners = this.eventListeners.get(eventName);
        listeners.forEach(listener => {
          try {
            listener(data);
          } catch (error) {
            console.error(`[AI高级分析器] 事件监听器执行失败 (${eventName}):`, error);
          }
        });
      }
    } catch (error) {
      console.error(`[AI高级分析器] 事件触发失败 (${eventName}):`, error);
    }
  }
  
  /**
   * @method on - 添加事件监听器
   * @description 为指定事件添加监听器
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(eventName, listener) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName).push(listener);
  }
  
  /**
   * @method cacheAnalysisResult - 缓存分析结果
   * @description 将分析结果缓存到内存中
   * @param {string} type - 分析类型
   * @param {Object} result - 分析结果
   */
  cacheAnalysisResult(type, result) {
    try {
      const cacheKey = `${type}_${result.timestamp}`;
      
      // 检查缓存大小限制
      if (this.analysisCache.size >= this.config.maxCacheSize) {
        // 删除最旧的缓存项
        const oldestKey = this.analysisCache.keys().next().value;
        this.analysisCache.delete(oldestKey);
      }
      
      this.analysisCache.set(cacheKey, {
        ...result,
        cached: true,
        cacheTime: Date.now()
      });
      
    } catch (error) {
      console.error('[AI高级分析器] 结果缓存失败:', error);
    }
  }
  
  /**
   * @method addToHistory - 添加到历史记录
   * @description 将分析结果添加到历史记录
   * @param {string} type - 分析类型
   * @param {Object} result - 分析结果
   */
  addToHistory(type, result) {
    try {
      const historyRecord = {
        type: type,
        timestamp: result.timestamp,
        summary: this.generateResultSummary(result),
        result: result
      };
      
      this.analysisHistory.push(historyRecord);
      
      // 保存到存储
      this.saveAnalysisHistory();
      
    } catch (error) {
      console.error('[AI高级分析器] 历史记录添加失败:', error);
    }
  }
  
  /**
   * @method saveAnalysisHistory - 保存分析历史
   * @description 将分析历史保存到扩展存储
   */
  async saveAnalysisHistory() {
    try {
      await chrome.storage.local.set({
        'ai_analysis_history': this.analysisHistory.slice(-100) // 只保存最近100条
      });
    } catch (error) {
      console.error('[AI高级分析器] 历史记录保存失败:', error);
    }
  }
  
  /**
   * @method cleanupExpiredCache - 清理过期缓存
   * @description 清理过期的缓存项
   */
  cleanupExpiredCache() {
    try {
      const now = Date.now();
      const expiredKeys = [];
      
      for (const [key, value] of this.analysisCache.entries()) {
        if (value.cacheTime && now - value.cacheTime > this.config.cacheExpiry) {
          expiredKeys.push(key);
        }
      }
      
      expiredKeys.forEach(key => this.analysisCache.delete(key));
      
      if (expiredKeys.length > 0) {
        console.log(`[AI高级分析器] 清理了 ${expiredKeys.length} 个过期缓存项`);
      }
      
    } catch (error) {
      console.error('[AI高级分析器] 缓存清理失败:', error);
    }
  }
  
  /**
   * @method cleanup - 清理资源
   * @description 清理分析器使用的资源
   */
  cleanup() {
    try {
      console.log('[AI高级分析器] 正在清理资源...');
      
      // 清理缓存
      this.analysisCache.clear();
      
      // 清理事件监听器
      this.eventListeners.clear();
      
      // 保存分析历史
      this.saveAnalysisHistory();
      
      // 清理基础分析器
      if (this.contentAnalyzer) {
        this.contentAnalyzer.cleanup();
      }
      
      this.isInitialized = false;
      
      console.log('[AI高级分析器] 资源清理完成');
    } catch (error) {
      console.error('[AI高级分析器] 资源清理失败:', error);
    }
  }
  // #endregion
} 