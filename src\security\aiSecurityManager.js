/**
 * @file AI安全管理模块
 * @description 负责API密钥加密存储、数据安全处理等功能
 */

/**
 * @class AiSecurityManager - AI安全管理器
 * @description 提供统一的安全管理功能，包括加密存储、数据验证等
 */
export class AiSecurityManager {
  /**
   * @function constructor - 构造函数
   * @description 初始化安全管理器
   */
  constructor() {
    this.encryptionKey = null;
    this.isInitialized = false;
    
    // 安全配置常量
    this.STORAGE_KEYS = {
      API_KEYS: 'ai_sidebar_encrypted_api_keys',
      USER_CONFIG: 'ai_sidebar_user_config',
      SECURITY_SETTINGS: 'ai_sidebar_security_settings'
    };
    
    this.ENCRYPTION_CONFIG = {
      ALGORITHM: 'AES-GCM',
      KEY_LENGTH: 256,
      IV_LENGTH: 12
    };
  }

  /**
   * @function init - 初始化安全管理器
   * @description 设置加密密钥和安全配置
   * @returns {Promise<void>}
   */
  async init() {
    try {
      console.log('[AI安全管理] 初始化安全管理器...');
      
      // 初始化或获取加密密钥
      await this.initializeEncryptionKey();
      
      // 验证安全设置
      await this.validateSecuritySettings();
      
      this.isInitialized = true;
      console.log('[AI安全管理] 安全管理器初始化完成');
    } catch (error) {
      console.error('[AI安全管理] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function initializeEncryptionKey - 初始化加密密钥
   * @description 生成或获取用于数据加密的密钥
   * @returns {Promise<void>}
   */
  async initializeEncryptionKey() {
    try {
      // 尝试从存储中获取现有密钥
      const storedKeyData = await chrome.storage.local.get(['ai_sidebar_master_key']);
      
      if (storedKeyData.ai_sidebar_master_key) {
        // 使用存储的密钥数据重新生成密钥
        this.encryptionKey = await this.deriveKeyFromData(storedKeyData.ai_sidebar_master_key);
      } else {
        // 生成新的主密钥
        const masterKeyData = crypto.getRandomValues(new Uint8Array(32));
        await chrome.storage.local.set({
          'ai_sidebar_master_key': Array.from(masterKeyData)
        });
        
        this.encryptionKey = await this.deriveKeyFromData(masterKeyData);
      }
    } catch (error) {
      console.error('[AI安全管理] 密钥初始化失败:', error);
      throw error;
    }
  }

  /**
   * @function deriveKeyFromData - 从数据派生密钥
   * @description 使用Web Crypto API从原始数据派生加密密钥
   * @param {Uint8Array|Array} keyData - 原始密钥数据
   * @returns {Promise<CryptoKey>} 派生的加密密钥
   */
  async deriveKeyFromData(keyData) {
    const keyArray = keyData instanceof Uint8Array ? keyData : new Uint8Array(keyData);
    
    return await crypto.subtle.importKey(
      'raw',
      keyArray,
      this.ENCRYPTION_CONFIG.ALGORITHM,
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * @function encryptData - 加密数据
   * @description 使用AES-GCM算法加密敏感数据
   * @param {string} data - 要加密的数据
   * @returns {Promise<Object>} 加密结果对象
   */
  async encryptData(data) {
    if (!this.isInitialized) {
      throw new Error('安全管理器未初始化');
    }

    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      
      // 生成随机初始化向量
      const iv = crypto.getRandomValues(new Uint8Array(this.ENCRYPTION_CONFIG.IV_LENGTH));
      
      // 加密数据
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: this.ENCRYPTION_CONFIG.ALGORITHM,
          iv: iv
        },
        this.encryptionKey,
        dataBuffer
      );
      
      return {
        encryptedData: Array.from(new Uint8Array(encryptedBuffer)),
        iv: Array.from(iv),
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[AI安全管理] 数据加密失败:', error);
      throw error;
    }
  }

  /**
   * @function decryptData - 解密数据
   * @description 解密使用AES-GCM算法加密的数据
   * @param {Object} encryptedObj - 加密对象
   * @returns {Promise<string>} 解密后的原始数据
   */
  async decryptData(encryptedObj) {
    if (!this.isInitialized) {
      throw new Error('安全管理器未初始化');
    }

    try {
      const { encryptedData, iv } = encryptedObj;
      
      // 重建数组缓冲区
      const encryptedBuffer = new Uint8Array(encryptedData).buffer;
      const ivArray = new Uint8Array(iv);
      
      // 解密数据
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: this.ENCRYPTION_CONFIG.ALGORITHM,
          iv: ivArray
        },
        this.encryptionKey,
        encryptedBuffer
      );
      
      const decoder = new TextDecoder();
      return decoder.decode(decryptedBuffer);
    } catch (error) {
      console.error('[AI安全管理] 数据解密失败:', error);
      throw error;
    }
  }

  /**
   * @function storeApiKey - 安全存储API密钥
   * @description 加密存储AI服务提供商的API密钥
   * @param {string} provider - 服务提供商标识
   * @param {string} apiKey - API密钥
   * @returns {Promise<void>}
   */
  async storeApiKey(provider, apiKey) {
    try {
      // 获取现有的API密钥存储
      const existingKeys = await this.getStoredApiKeys();
      
      // 加密新的API密钥
      const encryptedKey = await this.encryptData(apiKey);
      
      // 更新密钥存储
      existingKeys[provider] = encryptedKey;
      
      // 保存到Chrome存储
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.API_KEYS]: existingKeys
      });
      
      console.log(`[AI安全管理] ${provider} API密钥已安全存储`);
    } catch (error) {
      console.error('[AI安全管理] API密钥存储失败:', error);
      throw error;
    }
  }

  /**
   * @function getApiKey - 获取API密钥
   * @description 从安全存储中获取并解密API密钥
   * @param {string} provider - 服务提供商标识
   * @returns {Promise<string|null>} 解密后的API密钥
   */
  async getApiKey(provider) {
    try {
      const storedKeys = await this.getStoredApiKeys();
      
      if (!storedKeys[provider]) {
        return null;
      }
      
      // 解密API密钥
      return await this.decryptData(storedKeys[provider]);
    } catch (error) {
      console.error('[AI安全管理] API密钥获取失败:', error);
      return null;
    }
  }

  /**
   * @function getStoredApiKeys - 获取存储的API密钥
   * @description 从Chrome存储获取加密的API密钥对象
   * @returns {Promise<Object>} 存储的API密钥对象
   */
  async getStoredApiKeys() {
    const result = await chrome.storage.local.get([this.STORAGE_KEYS.API_KEYS]);
    return result[this.STORAGE_KEYS.API_KEYS] || {};
  }

  /**
   * @function removeApiKey - 移除API密钥
   * @description 从安全存储中删除指定的API密钥
   * @param {string} provider - 服务提供商标识
   * @returns {Promise<void>}
   */
  async removeApiKey(provider) {
    try {
      const existingKeys = await this.getStoredApiKeys();
      delete existingKeys[provider];
      
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.API_KEYS]: existingKeys
      });
      
      console.log(`[AI安全管理] ${provider} API密钥已删除`);
    } catch (error) {
      console.error('[AI安全管理] API密钥删除失败:', error);
      throw error;
    }
  }

  /**
   * @function validateSecuritySettings - 验证安全设置
   * @description 检查和验证当前的安全配置
   * @returns {Promise<void>}
   */
  async validateSecuritySettings() {
    try {
      const settings = await chrome.storage.sync.get([this.STORAGE_KEYS.SECURITY_SETTINGS]);
      
      const defaultSettings = {
        encryptionEnabled: true,
        autoLogout: false,
        autoLogoutMinutes: 30,
        lastSecurityCheck: Date.now()
      };
      
      if (!settings[this.STORAGE_KEYS.SECURITY_SETTINGS]) {
        await chrome.storage.sync.set({
          [this.STORAGE_KEYS.SECURITY_SETTINGS]: defaultSettings
        });
      }
    } catch (error) {
      console.error('[AI安全管理] 安全设置验证失败:', error);
    }
  }

  /**
   * @function sanitizeUserInput - 清理用户输入
   * @description 对用户输入进行安全清理，防止注入攻击
   * @param {string} input - 用户输入
   * @returns {string} 清理后的安全输入
   */
  sanitizeUserInput(input) {
    if (typeof input !== 'string') {
      return '';
    }
    
    // 移除潜在的恶意字符
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * @function generateSecureId - 生成安全ID
   * @description 生成用于标识的安全随机ID
   * @returns {string} 安全的随机ID
   */
  generateSecureId() {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * @function getSecureData - 获取安全存储的数据
   * @description 从加密存储中获取并解密数据
   * @param {string} key - 存储键
   * @returns {Promise<any>} 解密后的数据
   */
  async getSecureData(key) {
    if (!this.isInitialized) {
      throw new Error('安全管理器未初始化');
    }

    try {
      const storageKey = `ai_sidebar_secure_${key}`;
      const result = await chrome.storage.local.get([storageKey]);
      
      if (!result[storageKey]) {
        return null;
      }
      
      // 解密数据
      const decryptedData = await this.decryptData(result[storageKey]);
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error(`[AI安全管理] 获取安全数据失败 (${key}):`, error);
      return null;
    }
  }

  /**
   * @function storeSecureData - 安全存储数据
   * @description 加密并存储敏感数据
   * @param {string} key - 存储键
   * @param {any} data - 要存储的数据
   * @returns {Promise<void>}
   */
  async storeSecureData(key, data) {
    if (!this.isInitialized) {
      throw new Error('安全管理器未初始化');
    }

    try {
      // 加密数据
      const encryptedData = await this.encryptData(JSON.stringify(data));
      
      // 存储到Chrome存储
      const storageKey = `ai_sidebar_secure_${key}`;
      await chrome.storage.local.set({
        [storageKey]: encryptedData
      });
      
      console.log(`[AI安全管理] 安全数据已存储 (${key})`);
    } catch (error) {
      console.error(`[AI安全管理] 存储安全数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * @function removeSecureData - 删除安全存储的数据
   * @description 从安全存储中删除指定的数据
   * @param {string} key - 存储键
   * @returns {Promise<void>}
   */
  async removeSecureData(key) {
    try {
      const storageKey = `ai_sidebar_secure_${key}`;
      await chrome.storage.local.remove([storageKey]);
      console.log(`[AI安全管理] 安全数据已删除 (${key})`);
    } catch (error) {
      console.error(`[AI安全管理] 删除安全数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * @function cleanup - 清理安全管理器
   * @description 清理敏感数据和资源
   */
  cleanup() {
    this.encryptionKey = null;
    this.isInitialized = false;
    console.log('[AI安全管理] 安全管理器已清理');
  }
} 