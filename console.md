---
type: "manual"
---

<div id="errorsList">
        <!--?lit$*********$--><!---->
          <div class="item-container">
            <div class="cr-row error-item selected">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="true">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="234">
                  <!--?lit$*********$-->Service worker registration failed. Status code: 15
                </div>
                <div class="cr-icon icon-expand-less">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="234" aria-describedby="234" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="false" class="collapse-opened" style="" opened="">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                <extensions-code-section could-not-display-code="Nothing to see here, move along." is-active="">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="235">
                  <!--?lit$*********$-->Uncaught SyntaxError: Export 'MESSAGE_TYPES' is not defined in module
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="235" aria-describedby="235" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/shared/messageTypes.js
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->src/shared/messageTypes.js:119 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="236">
                  <!--?lit$*********$-->Uncaught SyntaxError: Unexpected token 'export'
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="236" aria-describedby="236" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://www.gomyhire.com.my/contact_us
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$*********$-->src/shared/messageTypes.js:119 (anonymous function)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="237">
                  <!--?lit$*********$-->[AI内容捕获] 内容捕获失败: TypeError: content.textContent.trim is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="237" aria-describedby="237" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->https://www.gomyhire.com.my/contact_us
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$*********$-->src/content/aiContentCaptureScript.js:198 (performCapture)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="238">
                  <!--?lit$*********$-->获取页面信息失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="238" aria-describedby="238" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:1225 (getCurrentPageInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="239">
                  <!--?lit$*********$-->[AI侧边栏] 检查API状态失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="239" aria-describedby="239" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:1467 (checkApiStatus)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="6" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="241">
                  <!--?lit$*********$-->发送消息失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="241" aria-describedby="241" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="6">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:451 (sendMessage)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="7" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="243">
                  <!--?lit$*********$-->保存到Notion失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="243" aria-describedby="243" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="7">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:646 (saveToNotion)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="8" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="242">
                  <!--?lit$*********$-->翻译失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="242" aria-describedby="242" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="8">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:617 (translateMessage)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="9" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="244">
                  <!--?lit$*********$-->加载设置失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="244" aria-describedby="244" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="9">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="9" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:807 (loadCurrentSettings)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="10" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="240">
                  <!--?lit$*********$-->[AI侧边栏] 检查Notion状态失败: Error: 消息发送超时
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="240" aria-describedby="240" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="10">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="10" tabindex="-1" class="">
                        <!--?lit$*********$-->src/ui/sidebar/aiSidebarPanel.js:1507 (checkNotionStatus)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>