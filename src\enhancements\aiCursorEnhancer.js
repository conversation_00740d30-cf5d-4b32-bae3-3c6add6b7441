/**
 * @file AI光标输入增强器
 * @description 负责智能输入预测、自动补全、上下文感知等功能
 */

// 使用IIFE包装，避免全局污染，同时在content script中可用
(function() {
  'use strict';

  /**
   * @class AiCursorEnhancer - AI光标输入增强器
   * @description 提供智能输入增强功能
   */
  class AiCursorEnhancer {
    /**
     * @function constructor - 构造函数
     * @description 初始化光标增强器
     * @param {AiSidebarCore} sidebarCore - 侧边栏核心实例
     * @param {AiContentAnalyzer} contentAnalyzer - 内容分析器实例
     */
    constructor(sidebarCore, contentAnalyzer) {
      this.sidebarCore = sidebarCore;
      this.contentAnalyzer = contentAnalyzer;
      this.isInitialized = false;
      this.isActive = false;
      
      // 输入监听配置
      this.inputConfig = {
        enabledInputTypes: ['input', 'textarea', 'div[contenteditable]'],
        minTextLength: 3,
        triggerDelay: 500, // 毫秒
        maxSuggestions: 5,
        contextWindow: 200 // 字符数
      };
      
      // 当前状态
      this.currentState = {
        activeElement: null,
        lastInputTime: 0,
        currentText: '',
        cursorPosition: 0,
        contextBefore: '',
        contextAfter: '',
        pageContext: null
      };
      
      // 建议系统
      this.suggestionSystem = {
        isVisible: false,
        suggestions: [],
        selectedIndex: 0,
        element: null,
        position: { x: 0, y: 0 }
      };
      
      // 事件监听器
      this.eventListeners = new Map();
      
      // 防抖计时器
      this.debounceTimers = new Map();
      
      // 缓存系统
      this.cache = new Map();
      this.cacheExpiry = 5 * 60 * 1000; // 5分钟
      
      // 快捷键配置
      this.shortcuts = {
        acceptSuggestion: 'Tab',
        nextSuggestion: 'ArrowDown',
        prevSuggestion: 'ArrowUp',
        dismissSuggestions: 'Escape',
        triggerManual: 'Ctrl+Space'
      };
      
      // 性能统计
      this.stats = {
        totalSuggestions: 0,
        acceptedSuggestions: 0,
        rejectedSuggestions: 0,
        averageResponseTime: 0
      };
    }

    /**
     * @function init - 初始化光标增强器
     * @description 设置事件监听和初始化组件
     * @returns {Promise<void>}
     */
    async init() {
      try {
        console.log('[光标增强] 初始化光标增强器...');
        
        if (!this.sidebarCore || !this.contentAnalyzer) {
          throw new Error('依赖模块未正确初始化');
        }
        
        // 创建建议界面元素
        this.createSuggestionUI();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 设置快捷键监听
        this.setupKeyboardListeners();
        
        // 初始化页面上下文
        await this.initializePageContext();
        
        this.isInitialized = true;
        console.log('[光标增强] 光标增强器初始化完成');
      } catch (error) {
        console.error('[光标增强] 初始化失败:', error);
        throw error;
      }
    }

    /**
     * @function activate - 激活增强器
     * @description 激活光标输入增强功能
     */
    activate() {
      if (!this.isInitialized) {
        console.warn('[光标增强] 增强器未初始化');
        return;
      }
      
      this.isActive = true;
      this.attachInputListeners();
      console.log('[光标增强] 光标增强器已激活');
    }

    /**
     * @function deactivate - 停用增强器
     * @description 停用光标输入增强功能
     */
    deactivate() {
      this.isActive = false;
      this.detachInputListeners();
      this.hideSuggestions();
      console.log('[光标增强] 光标增强器已停用');
    }

    /**
     * @function createSuggestionUI - 创建建议界面
     * @description 创建智能建议的UI元素
     */
    createSuggestionUI() {
      // 创建建议容器
      this.suggestionSystem.element = document.createElement('div');
      this.suggestionSystem.element.className = 'ai-cursor-suggestions';
      this.suggestionSystem.element.style.cssText = `
        position: fixed;
        z-index: 10000;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-width: 300px;
        max-height: 200px;
        overflow-y: auto;
        display: none;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
      `;
      
      document.body.appendChild(this.suggestionSystem.element);
    }

    /**
     * @function setupEventListeners - 设置事件监听器
     * @description 设置全局事件监听器
     */
    setupEventListeners() {
      // 页面点击事件（隐藏建议）
      this.addEventListener(document, 'click', (event) => {
        if (!this.suggestionSystem.element.contains(event.target)) {
          this.hideSuggestions();
        }
      });
      
      // 页面滚动事件（更新建议位置）
      this.addEventListener(window, 'scroll', () => {
        if (this.suggestionSystem.isVisible) {
          this.updateSuggestionPosition();
        }
      });
      
      // 窗口大小变化事件
      this.addEventListener(window, 'resize', () => {
        if (this.suggestionSystem.isVisible) {
          this.updateSuggestionPosition();
        }
      });
    }

    /**
     * @function setupKeyboardListeners - 设置键盘监听器
     * @description 设置快捷键监听器
     */
    setupKeyboardListeners() {
      this.addEventListener(document, 'keydown', (event) => {
        if (!this.isActive || !this.suggestionSystem.isVisible) {
          return;
        }
        
        switch (event.key) {
          case this.shortcuts.acceptSuggestion:
            event.preventDefault();
            this.acceptSelectedSuggestion();
            break;
            
          case this.shortcuts.nextSuggestion:
            event.preventDefault();
            this.selectNextSuggestion();
            break;
            
          case this.shortcuts.prevSuggestion:
            event.preventDefault();
            this.selectPrevSuggestion();
            break;
            
          case this.shortcuts.dismissSuggestions:
            event.preventDefault();
            this.hideSuggestions();
            break;
        }
        
        // 手动触发建议
        if (event.ctrlKey && event.key === ' ') {
          event.preventDefault();
          this.triggerManualSuggestion();
        }
      });
    }

    /**
     * @function attachInputListeners - 附加输入监听器
     * @description 为页面上的输入元素附加监听器
     */
    attachInputListeners() {
      // 查找所有可输入元素
      const inputElements = this.findInputElements();
      
      inputElements.forEach(element => {
        this.attachElementListeners(element);
      });
      
      // 监听动态添加的元素
      this.observeNewElements();
    }

    /**
     * @function detachInputListeners - 分离输入监听器
     * @description 移除所有输入监听器
     */
    detachInputListeners() {
      // 清理所有事件监听器
      this.eventListeners.forEach((listeners, element) => {
        listeners.forEach(({ event, handler }) => {
          element.removeEventListener(event, handler);
        });
      });
      
      this.eventListeners.clear();
      
      // 停止观察器
      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
      }
    }

    /**
     * @function findInputElements - 查找输入元素
     * @description 查找页面上所有可输入的元素
     * @returns {Array} 输入元素数组
     */
    findInputElements() {
      const selectors = this.inputConfig.enabledInputTypes.join(', ');
      return Array.from(document.querySelectorAll(selectors));
    }

    /**
     * @function attachElementListeners - 为元素附加监听器
     * @description 为特定元素附加输入监听器
     * @param {Element} element - 目标元素
     */
    attachElementListeners(element) {
      // 输入事件
      this.addEventListener(element, 'input', (event) => {
        this.handleInput(event);
      });
      
      // 焦点事件
      this.addEventListener(element, 'focus', (event) => {
        this.handleFocus(event);
      });
      
      // 失焦事件
      this.addEventListener(element, 'blur', (event) => {
        this.handleBlur(event);
      });
      
      // 键盘事件
      this.addEventListener(element, 'keydown', (event) => {
        this.handleKeyDown(event);
      });
      
      // 光标位置变化
      this.addEventListener(element, 'selectionchange', (event) => {
        this.handleSelectionChange(event);
      });
    }

    /**
     * @function observeNewElements - 观察新元素
     * @description 使用MutationObserver监听动态添加的输入元素
     */
    observeNewElements() {
      this.mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查新添加的元素是否为输入元素
              const inputElements = this.findInputElementsInNode(node);
              inputElements.forEach(element => {
                this.attachElementListeners(element);
              });
            }
          });
        });
      });
      
      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    /**
     * @function findInputElementsInNode - 在节点中查找输入元素
     * @description 在指定节点中查找所有输入元素
     * @param {Node} node - 目标节点
     * @returns {Array} 输入元素数组
     */
    findInputElementsInNode(node) {
      const elements = [];
      const selectors = this.inputConfig.enabledInputTypes.join(', ');
      
      // 检查节点本身
      if (node.matches && node.matches(selectors)) {
        elements.push(node);
      }
      
      // 检查子节点
      if (node.querySelectorAll) {
        elements.push(...Array.from(node.querySelectorAll(selectors)));
      }
      
      return elements;
    }

    /**
     * @function handleInput - 处理输入事件
     * @description 处理用户输入事件
     * @param {Event} event - 输入事件
     */
    handleInput(event) {
      if (!this.isActive) return;
      
      const element = event.target;
      this.updateCurrentState(element);
      
      // 防抖处理
      this.debounce('input', () => {
        this.processInput(element);
      }, this.inputConfig.triggerDelay);
    }

    /**
     * @function handleFocus - 处理焦点事件
     * @description 处理输入元素获得焦点事件
     * @param {Event} event - 焦点事件
     */
    handleFocus(event) {
      if (!this.isActive) return;
      
      const element = event.target;
      this.currentState.activeElement = element;
      this.updateCurrentState(element);
      
      // 分析当前页面上下文
      this.analyzePageContext();
    }

    /**
     * @function handleBlur - 处理失焦事件
     * @description 处理输入元素失去焦点事件
     * @param {Event} event - 失焦事件
     */
    handleBlur(event) {
      // 延迟隐藏建议，允许用户点击建议
      setTimeout(() => {
        if (document.activeElement !== this.suggestionSystem.element) {
          this.hideSuggestions();
        }
      }, 150);
    }

    /**
     * @function handleKeyDown - 处理键盘按下事件
     * @description 处理键盘按下事件
     * @param {Event} event - 键盘事件
     */
    handleKeyDown(event) {
      if (!this.isActive) return;
      
      // 如果建议可见，处理导航键
      if (this.suggestionSystem.isVisible) {
        switch (event.key) {
          case 'ArrowDown':
          case 'ArrowUp':
          case 'Tab':
          case 'Enter':
          case 'Escape':
            // 这些键由全局监听器处理
            return;
        }
      }
      
      // 记录按键时间
      this.currentState.lastInputTime = Date.now();
    }

    /**
     * @function handleSelectionChange - 处理选择变化事件
     * @description 处理光标位置变化事件
     * @param {Event} event - 选择变化事件
     */
    handleSelectionChange(event) {
      if (!this.isActive || !this.currentState.activeElement) return;
      
      this.updateCursorPosition();
    }

    /**
     * @function updateCurrentState - 更新当前状态
     * @description 更新输入状态信息
     * @param {Element} element - 输入元素
     */
    updateCurrentState(element) {
      try {
        // 获取当前文本
        this.currentState.currentText = this.getElementText(element);
        
        // 更新光标位置
        this.updateCursorPosition();
        
        // 更新上下文
        this.updateContext();
      } catch (error) {
        console.error('[光标增强] 更新状态失败:', error);
      }
    }

    /**
     * @function getElementText - 获取元素文本
     * @description 获取输入元素的文本内容
     * @param {Element} element - 输入元素
     * @returns {string} 文本内容
     */
    getElementText(element) {
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        return element.value;
      } else if (element.contentEditable === 'true') {
        return element.textContent || element.innerText;
      }
      return '';
    }

    /**
     * @function updateCursorPosition - 更新光标位置
     * @description 更新当前光标位置信息
     */
    updateCursorPosition() {
      try {
        const element = this.currentState.activeElement;
        if (!element) return;
        
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
          this.currentState.cursorPosition = element.selectionStart;
        } else if (element.contentEditable === 'true') {
          const selection = window.getSelection();
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            this.currentState.cursorPosition = range.startOffset;
          }
        }
      } catch (error) {
        console.error('[光标增强] 更新光标位置失败:', error);
      }
    }

    /**
     * @function updateContext - 更新上下文
     * @description 更新输入上下文信息
     */
    updateContext() {
      const text = this.currentState.currentText;
      const position = this.currentState.cursorPosition;
      const windowSize = this.inputConfig.contextWindow;
      
      // 获取光标前后的上下文
      this.currentState.contextBefore = text.substring(
        Math.max(0, position - windowSize),
        position
      );
      
      this.currentState.contextAfter = text.substring(
        position,
        Math.min(text.length, position + windowSize)
      );
    }

    /**
     * @function processInput - 处理输入
     * @description 处理用户输入并生成建议
     * @param {Element} element - 输入元素
     */
    async processInput(element) {
      try {
        const text = this.currentState.currentText;
        
        // 检查是否满足触发条件
        if (text.length < this.inputConfig.minTextLength) {
          this.hideSuggestions();
          return;
        }
        
        // 生成建议
        const suggestions = await this.generateSuggestions();
        
        if (suggestions && suggestions.length > 0) {
          this.showSuggestions(suggestions);
        } else {
          this.hideSuggestions();
        }
      } catch (error) {
        console.error('[光标增强] 处理输入失败:', error);
      }
    }

    /**
     * @function generateSuggestions - 生成建议
     * @description 基于当前上下文生成智能建议
     * @returns {Promise<Array>} 建议数组
     */
    async generateSuggestions() {
      try {
        const startTime = Date.now();
        
        // 构建请求上下文
        const context = {
          currentText: this.currentState.currentText,
          cursorPosition: this.currentState.cursorPosition,
          contextBefore: this.currentState.contextBefore,
          contextAfter: this.currentState.contextAfter,
          pageContext: this.currentState.pageContext,
          elementType: this.currentState.activeElement?.tagName?.toLowerCase(),
          elementAttributes: this.getElementAttributes(this.currentState.activeElement)
        };
        
        // 检查缓存
        const cacheKey = this.generateCacheKey(context);
        const cachedSuggestions = this.getFromCache(cacheKey);
        if (cachedSuggestions) {
          return cachedSuggestions;
        }
        
        // 调用AI生成建议
        const suggestions = await this.sidebarCore.generateInputSuggestions(context);
        
        // 缓存结果
        this.setCache(cacheKey, suggestions);
        
        // 更新统计
        const responseTime = Date.now() - startTime;
        this.updateStats(suggestions.length, responseTime);
        
        return suggestions;
      } catch (error) {
        console.error('[光标增强] 生成建议失败:', error);
        return [];
      }
    }

    /**
     * @function showSuggestions - 显示建议
     * @description 显示智能建议列表
     * @param {Array} suggestions - 建议数组
     */
    showSuggestions(suggestions) {
      try {
        this.suggestionSystem.suggestions = suggestions;
        this.suggestionSystem.selectedIndex = 0;
        
        // 清空容器
        this.suggestionSystem.element.innerHTML = '';
        
        // 创建建议项
        suggestions.forEach((suggestion, index) => {
          const item = document.createElement('div');
          item.className = `ai-suggestion-item ${index === 0 ? 'selected' : ''}`;
          item.style.cssText = `
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
          `;
          
          // 建议内容
          const content = document.createElement('div');
          content.className = 'suggestion-content';
          content.textContent = suggestion.text;
          content.style.cssText = `
            font-weight: 500;
            margin-bottom: 2px;
          `;
          
          // 建议描述
          if (suggestion.description) {
            const description = document.createElement('div');
            description.className = 'suggestion-description';
            description.textContent = suggestion.description;
            description.style.cssText = `
              font-size: 12px;
              color: #666;
            `;
            item.appendChild(description);
          }
          
          item.appendChild(content);
          
          // 点击事件
          item.addEventListener('click', () => {
            this.applySuggestion(suggestion);
          });
          
          // 悬停事件
          item.addEventListener('mouseenter', () => {
            this.selectSuggestion(index);
          });
          
          this.suggestionSystem.element.appendChild(item);
        });
        
        // 更新位置并显示
        this.updateSuggestionPosition();
        this.suggestionSystem.element.style.display = 'block';
        this.suggestionSystem.isVisible = true;
        
        this.stats.totalSuggestions++;
      } catch (error) {
        console.error('[光标增强] 显示建议失败:', error);
      }
    }

    /**
     * @function hideSuggestions - 隐藏建议
     * @description 隐藏建议列表
     */
    hideSuggestions() {
      this.suggestionSystem.element.style.display = 'none';
      this.suggestionSystem.isVisible = false;
      this.suggestionSystem.suggestions = [];
      this.suggestionSystem.selectedIndex = 0;
    }

    /**
     * @function updateSuggestionPosition - 更新建议位置
     * @description 更新建议框的位置
     */
    updateSuggestionPosition() {
      try {
        const element = this.currentState.activeElement;
        if (!element) return;
        
        // 获取元素位置
        const rect = element.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        // 计算建议框位置
        let x = rect.left + scrollLeft;
        let y = rect.bottom + scrollTop + 5;
        
        // 边界检查
        const suggestionRect = this.suggestionSystem.element.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 水平边界检查
        if (x + suggestionRect.width > viewportWidth) {
          x = viewportWidth - suggestionRect.width - 10;
        }
        
        // 垂直边界检查
        if (y + suggestionRect.height > viewportHeight + scrollTop) {
          y = rect.top + scrollTop - suggestionRect.height - 5;
        }
        
        // 应用位置
        this.suggestionSystem.element.style.left = `${x}px`;
        this.suggestionSystem.element.style.top = `${y}px`;
        
        this.suggestionSystem.position = { x, y };
      } catch (error) {
        console.error('[光标增强] 更新建议位置失败:', error);
      }
    }

    /**
     * @function selectSuggestion - 选择建议
     * @description 选择指定索引的建议
     * @param {number} index - 建议索引
     */
    selectSuggestion(index) {
      if (index < 0 || index >= this.suggestionSystem.suggestions.length) {
        return;
      }
      
      // 更新选中状态
      const items = this.suggestionSystem.element.querySelectorAll('.ai-suggestion-item');
      items.forEach((item, i) => {
        item.classList.toggle('selected', i === index);
        if (i === index) {
          item.style.backgroundColor = '#f0f8ff';
        } else {
          item.style.backgroundColor = '';
        }
      });
      
      this.suggestionSystem.selectedIndex = index;
    }

    /**
     * @function selectNextSuggestion - 选择下一个建议
     * @description 选择下一个建议项
     */
    selectNextSuggestion() {
      const nextIndex = (this.suggestionSystem.selectedIndex + 1) % this.suggestionSystem.suggestions.length;
      this.selectSuggestion(nextIndex);
    }

    /**
     * @function selectPrevSuggestion - 选择上一个建议
     * @description 选择上一个建议项
     */
    selectPrevSuggestion() {
      const prevIndex = this.suggestionSystem.selectedIndex === 0 
        ? this.suggestionSystem.suggestions.length - 1 
        : this.suggestionSystem.selectedIndex - 1;
      this.selectSuggestion(prevIndex);
    }

    /**
     * @function acceptSelectedSuggestion - 接受选中建议
     * @description 接受当前选中的建议
     */
    acceptSelectedSuggestion() {
      const suggestion = this.suggestionSystem.suggestions[this.suggestionSystem.selectedIndex];
      if (suggestion) {
        this.applySuggestion(suggestion);
      }
    }

    /**
     * @function applySuggestion - 应用建议
     * @description 将建议应用到输入元素
     * @param {Object} suggestion - 建议对象
     */
    applySuggestion(suggestion) {
      try {
        const element = this.currentState.activeElement;
        if (!element) return;
        
        // 根据建议类型应用
        switch (suggestion.type) {
          case 'completion':
            this.applyCompletion(element, suggestion);
            break;
          case 'replacement':
            this.applyReplacement(element, suggestion);
            break;
          case 'insertion':
            this.applyInsertion(element, suggestion);
            break;
          default:
            this.applyCompletion(element, suggestion);
        }
        
        // 隐藏建议
        this.hideSuggestions();
        
        // 更新统计
        this.stats.acceptedSuggestions++;
        
        // 触发输入事件
        this.triggerInputEvent(element);
        
      } catch (error) {
        console.error('[光标增强] 应用建议失败:', error);
      }
    }

    /**
     * @function applyCompletion - 应用补全建议
     * @description 应用文本补全建议
     * @param {Element} element - 输入元素
     * @param {Object} suggestion - 建议对象
     */
    applyCompletion(element, suggestion) {
      const currentText = this.getElementText(element);
      const cursorPos = this.currentState.cursorPosition;
      
      // 在光标位置插入建议文本
      const newText = currentText.substring(0, cursorPos) + 
                     suggestion.text + 
                     currentText.substring(cursorPos);
      
      this.setElementText(element, newText);
      this.setCursorPosition(element, cursorPos + suggestion.text.length);
    }

    /**
     * @function applyReplacement - 应用替换建议
     * @description 应用文本替换建议
     * @param {Element} element - 输入元素
     * @param {Object} suggestion - 建议对象
     */
    applyReplacement(element, suggestion) {
      const currentText = this.getElementText(element);
      const { start = 0, end = currentText.length } = suggestion.range || {};
      
      // 替换指定范围的文本
      const newText = currentText.substring(0, start) + 
                     suggestion.text + 
                     currentText.substring(end);
      
      this.setElementText(element, newText);
      this.setCursorPosition(element, start + suggestion.text.length);
    }

    /**
     * @function applyInsertion - 应用插入建议
     * @description 应用文本插入建议
     * @param {Element} element - 输入元素
     * @param {Object} suggestion - 建议对象
     */
    applyInsertion(element, suggestion) {
      const currentText = this.getElementText(element);
      const insertPos = suggestion.position || this.currentState.cursorPosition;
      
      // 在指定位置插入文本
      const newText = currentText.substring(0, insertPos) + 
                     suggestion.text + 
                     currentText.substring(insertPos);
      
      this.setElementText(element, newText);
      this.setCursorPosition(element, insertPos + suggestion.text.length);
    }

    /**
     * @function setElementText - 设置元素文本
     * @description 设置输入元素的文本内容
     * @param {Element} element - 输入元素
     * @param {string} text - 文本内容
     */
    setElementText(element, text) {
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        element.value = text;
      } else if (element.contentEditable === 'true') {
        element.textContent = text;
      }
    }

    /**
     * @function setCursorPosition - 设置光标位置
     * @description 设置输入元素的光标位置
     * @param {Element} element - 输入元素
     * @param {number} position - 光标位置
     */
    setCursorPosition(element, position) {
      try {
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
          element.setSelectionRange(position, position);
        } else if (element.contentEditable === 'true') {
          const range = document.createRange();
          const selection = window.getSelection();
          
          if (element.firstChild) {
            range.setStart(element.firstChild, Math.min(position, element.firstChild.textContent.length));
            range.setEnd(element.firstChild, Math.min(position, element.firstChild.textContent.length));
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
        
        element.focus();
      } catch (error) {
        console.error('[光标增强] 设置光标位置失败:', error);
      }
    }

    /**
     * @function triggerInputEvent - 触发输入事件
     * @description 触发输入元素的input事件
     * @param {Element} element - 输入元素
     */
    triggerInputEvent(element) {
      const event = new Event('input', { bubbles: true, cancelable: true });
      element.dispatchEvent(event);
    }

    /**
     * @function triggerManualSuggestion - 手动触发建议
     * @description 手动触发智能建议
     */
    async triggerManualSuggestion() {
      if (!this.currentState.activeElement) return;
      
      this.updateCurrentState(this.currentState.activeElement);
      await this.processInput(this.currentState.activeElement);
    }

    /**
     * @function initializePageContext - 初始化页面上下文
     * @description 分析并初始化当前页面的上下文信息
     * @returns {Promise<void>}
     */
    async initializePageContext() {
      try {
        // 获取页面基本信息
        const pageInfo = {
          title: document.title,
          url: window.location.href,
          domain: window.location.hostname,
          language: document.documentElement.lang || 'en'
        };
        
        // 分析页面内容
        if (this.contentAnalyzer) {
          const analysis = await this.contentAnalyzer.analyzePageContent();
          this.currentState.pageContext = {
            ...pageInfo,
            analysis: analysis
          };
        } else {
          this.currentState.pageContext = pageInfo;
        }
      } catch (error) {
        console.error('[光标增强] 初始化页面上下文失败:', error);
      }
    }

    /**
     * @function analyzePageContext - 分析页面上下文
     * @description 分析当前页面的上下文变化
     */
    async analyzePageContext() {
      // 检查页面是否发生变化
      const currentUrl = window.location.href;
      if (this.currentState.pageContext?.url !== currentUrl) {
        await this.initializePageContext();
      }
    }

    /**
     * @function getElementAttributes - 获取元素属性
     * @description 获取输入元素的相关属性
     * @param {Element} element - 输入元素
     * @returns {Object} 元素属性对象
     */
    getElementAttributes(element) {
      if (!element) return {};
      
      return {
        id: element.id,
        className: element.className,
        placeholder: element.placeholder,
        name: element.name,
        type: element.type,
        role: element.role,
        'aria-label': element.getAttribute('aria-label'),
        'data-*': Array.from(element.attributes)
          .filter(attr => attr.name.startsWith('data-'))
          .reduce((acc, attr) => {
            acc[attr.name] = attr.value;
            return acc;
          }, {})
      };
    }

    /**
     * @function generateCacheKey - 生成缓存键
     * @description 为上下文生成缓存键
     * @param {Object} context - 上下文对象
     * @returns {string} 缓存键
     */
    generateCacheKey(context) {
      const keyData = {
        text: context.contextBefore.slice(-50), // 只使用最后50个字符
        position: context.cursorPosition,
        elementType: context.elementType,
        pageUrl: context.pageContext?.url
      };
      
      return btoa(JSON.stringify(keyData)).slice(0, 32);
    }

    /**
     * @function getFromCache - 从缓存获取
     * @description 从缓存中获取建议
     * @param {string} key - 缓存键
     * @returns {Array|null} 缓存的建议或null
     */
    getFromCache(key) {
      const cached = this.cache.get(key);
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }
      return null;
    }

    /**
     * @function setCache - 设置缓存
     * @description 设置缓存数据
     * @param {string} key - 缓存键
     * @param {Array} data - 要缓存的数据
     */
    setCache(key, data) {
      this.cache.set(key, {
        data: data,
        timestamp: Date.now()
      });
      
      // 清理过期缓存
      if (this.cache.size > 100) {
        this.cleanExpiredCache();
      }
    }

    /**
     * @function cleanExpiredCache - 清理过期缓存
     * @description 清理过期的缓存项
     */
    cleanExpiredCache() {
      const now = Date.now();
      for (const [key, value] of this.cache.entries()) {
        if (now - value.timestamp >= this.cacheExpiry) {
          this.cache.delete(key);
        }
      }
    }

    /**
     * @function updateStats - 更新统计信息
     * @description 更新性能统计信息
     * @param {number} suggestionCount - 建议数量
     * @param {number} responseTime - 响应时间
     */
    updateStats(suggestionCount, responseTime) {
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime + responseTime) / 2;
    }

    /**
     * @function addEventListener - 添加事件监听器
     * @description 添加事件监听器并记录
     * @param {Element} element - 目标元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理器
     */
    addEventListener(element, event, handler) {
      element.addEventListener(event, handler);
      
      if (!this.eventListeners.has(element)) {
        this.eventListeners.set(element, []);
      }
      
      this.eventListeners.get(element).push({ event, handler });
    }

    /**
     * @function debounce - 防抖函数
     * @description 防抖执行函数
     * @param {string} key - 防抖键
     * @param {Function} func - 要执行的函数
     * @param {number} delay - 延迟时间
     */
    debounce(key, func, delay) {
      if (this.debounceTimers.has(key)) {
        clearTimeout(this.debounceTimers.get(key));
      }
      
      const timer = setTimeout(() => {
        func();
        this.debounceTimers.delete(key);
      }, delay);
      
      this.debounceTimers.set(key, timer);
    }

    /**
     * @function getStats - 获取统计信息
     * @description 获取光标增强器的统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
      return {
        ...this.stats,
        isActive: this.isActive,
        cacheSize: this.cache.size,
        acceptanceRate: this.stats.totalSuggestions > 0 ? 
          (this.stats.acceptedSuggestions / this.stats.totalSuggestions * 100).toFixed(2) + '%' : '0%'
      };
    }

    /**
     * @function cleanup - 清理增强器
     * @description 清理资源和事件监听器
     */
    cleanup() {
      // 停用增强器
      this.deactivate();
      
      // 清理缓存
      this.cache.clear();
      
      // 清理防抖计时器
      this.debounceTimers.forEach(timer => clearTimeout(timer));
      this.debounceTimers.clear();
      
      // 移除UI元素
      if (this.suggestionSystem.element && this.suggestionSystem.element.parentNode) {
        this.suggestionSystem.element.parentNode.removeChild(this.suggestionSystem.element);
      }
      
      this.isInitialized = false;
      console.log('[光标增强] 光标增强器已清理');
    }
  }

  // 将AiCursorEnhancer类暴露到全局作用域，供Content Script使用
  if (typeof window !== 'undefined') {
    window.AiCursorEnhancer = AiCursorEnhancer;
  }

  // ES6 模块支持 - 供 Service Worker 使用
  if (typeof globalThis !== 'undefined') {
    globalThis.AiCursorEnhancer = AiCursorEnhancer;
  }
})();