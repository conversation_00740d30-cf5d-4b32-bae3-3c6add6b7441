/**
 * @file AI侧边栏API优化器
 * @description 优化API调用性能，包括批处理、缓存、去重和重试逻辑
 */

/**
 * @class AiApiOptimizer
 * @description API优化器，负责优化所有API调用的性能
 */
class AiApiOptimizer {
  /**
   * @function constructor - 构造函数
   * @description 初始化API优化器
   */
  constructor() {
    // 优化配置
    this.config = {
      // 批处理配置
      batchSize: 5, // 批处理大小
      batchTimeout: 100, // 批处理超时时间(ms)
      maxBatchWait: 500, // 最大批处理等待时间(ms)
      
      // 缓存配置
      cacheEnabled: true,
      defaultTTL: 5 * 60 * 1000, // 5分钟默认TTL
      maxCacheSize: 100, // 最大缓存条目数
      cacheCleanupInterval: 10 * 60 * 1000, // 10分钟清理间隔
      
      // 重试配置
      maxRetries: 3,
      baseDelay: 1000, // 基础延迟1秒
      maxDelay: 10000, // 最大延迟10秒
      backoffMultiplier: 2, // 指数退避倍数
      
      // 去重配置
      deduplicationWindow: 1000, // 去重时间窗口(ms)
      
      // 并发控制
      maxConcurrentRequests: 10,
      requestQueueSize: 50
    };
    
    // 内部状态
    this.requestCache = new Map();
    this.pendingBatches = new Map();
    this.pendingRequests = new Map();
    this.activeRequests = new Set();
    this.requestQueue = [];
    
    // 统计信息
    this.stats = {
      totalRequests: 0,
      cachedRequests: 0,
      batchedRequests: 0,
      retriedRequests: 0,
      deduplicatedRequests: 0,
      failedRequests: 0
    };
    
    // 定时器
    this.cacheCleanupTimer = null;
    
    // 初始化
    this.init();
  }

  /**
   * @function init - 初始化API优化器
   * @description 启动缓存清理和其他初始化任务
   */
  init() {
    console.log('[API优化] 初始化API优化器...');
    
    // 启动缓存清理
    this.startCacheCleanup();
    
    console.log('[API优化] API优化器初始化完成');
  }

  /**
   * @function optimizedRequest - 优化的API请求
   * @description 执行优化的API请求，包括缓存、去重、批处理等
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 请求结果
   */
  async optimizedRequest(options) {
    const {
      url,
      method = 'GET',
      data = null,
      headers = {},
      cache = true,
      ttl = this.config.defaultTTL,
      batch = false,
      retries = this.config.maxRetries,
      timeout = 30000
    } = options;
    
    this.stats.totalRequests++;
    
    // 生成请求键用于缓存和去重
    const requestKey = this.generateRequestKey(url, method, data);
    
    try {
      // 1. 检查缓存
      if (cache && method === 'GET') {
        const cachedResult = this.getCachedResult(requestKey);
        if (cachedResult) {
          this.stats.cachedRequests++;
          // 使用缓存结果
          return cachedResult;
        }
      }
      
      // 2. 检查去重
      if (this.pendingRequests.has(requestKey)) {
        this.stats.deduplicatedRequests++;
        // 去重请求，等待现有请求
        return await this.pendingRequests.get(requestKey);
      }
      
      // 3. 创建请求Promise
      const requestPromise = this.executeRequest({
        url, method, data, headers, batch, retries, timeout, ttl, requestKey
      });
      
      // 4. 记录待处理请求
      this.pendingRequests.set(requestKey, requestPromise);
      
      // 5. 执行请求
      const result = await requestPromise;
      
      // 6. 清理待处理请求
      this.pendingRequests.delete(requestKey);
      
      return result;
      
    } catch (error) {
      this.stats.failedRequests++;
      this.pendingRequests.delete(requestKey);
      throw error;
    }
  }

  /**
   * @function executeRequest - 执行请求
   * @description 执行实际的API请求，包括批处理和重试逻辑
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 请求结果
   */
  async executeRequest(options) {
    const { url, method, data, headers, batch, retries, timeout, ttl, requestKey } = options;
    
    // 检查是否支持批处理
    if (batch && this.supportsBatching(url, method)) {
      return await this.addToBatch(options);
    }
    
    // 检查并发限制
    await this.waitForConcurrencySlot();
    
    // 执行带重试的请求
    return await this.requestWithRetry(options);
  }

  /**
   * @function requestWithRetry - 带重试的请求
   * @description 执行带指数退避重试的请求
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 请求结果
   */
  async requestWithRetry(options) {
    const { url, method, data, headers, retries, timeout, ttl, requestKey } = options;
    let lastError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // 标记活跃请求
        this.activeRequests.add(requestKey);
        
        // 执行请求
        const result = await this.performRequest({
          url, method, data, headers, timeout
        });
        
        // 缓存结果
        if (method === 'GET' && result) {
          this.setCachedResult(requestKey, result, ttl);
        }
        
        // 清理活跃请求
        this.activeRequests.delete(requestKey);
        
        if (attempt > 0) {
          this.stats.retriedRequests++;
          console.log(`[API优化] ✅ 重试成功 (第${attempt}次):`, url);
        }
        
        return result;
        
      } catch (error) {
        lastError = error;
        this.activeRequests.delete(requestKey);
        
        // 检查是否应该重试
        if (attempt < retries && this.shouldRetry(error)) {
          const delay = this.calculateRetryDelay(attempt);
          console.warn(`[API优化] 🔄 请求失败，${delay}ms后重试 (第${attempt + 1}次):`, url, error.message);
          await this.sleep(delay);
        } else {
          console.error(`[API优化] ❌ 请求最终失败:`, url, error.message);
          throw error;
        }
      }
    }
    
    throw lastError;
  }

  /**
   * @function performRequest - 执行实际请求
   * @description 执行实际的HTTP请求
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 请求结果
   */
  async performRequest({ url, method, data, headers, timeout }) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const fetchOptions = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        signal: controller.signal
      };
      
      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        fetchOptions.body = JSON.stringify(data);
      }
      
      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      return result;
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`请求超时: ${timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * @function addToBatch - 添加到批处理
   * @description 将请求添加到批处理队列
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 请求结果
   */
  async addToBatch(options) {
    const { url } = options;
    const batchKey = this.getBatchKey(url);
    
    // 创建批处理Promise
    return new Promise((resolve, reject) => {
      // 获取或创建批处理
      if (!this.pendingBatches.has(batchKey)) {
        this.pendingBatches.set(batchKey, {
          requests: [],
          timer: null
        });
      }
      
      const batch = this.pendingBatches.get(batchKey);
      
      // 添加请求到批处理
      batch.requests.push({
        options,
        resolve,
        reject
      });
      
      // 检查是否达到批处理大小
      if (batch.requests.length >= this.config.batchSize) {
        this.executeBatch(batchKey);
      } else if (!batch.timer) {
        // 设置批处理超时
        batch.timer = setTimeout(() => {
          this.executeBatch(batchKey);
        }, this.config.batchTimeout);
      }
    });
  }

  /**
   * @function executeBatch - 执行批处理
   * @description 执行批处理中的所有请求
   * @param {string} batchKey - 批处理键
   */
  async executeBatch(batchKey) {
    const batch = this.pendingBatches.get(batchKey);
    if (!batch || batch.requests.length === 0) {
      return;
    }
    
    // 清理批处理
    this.pendingBatches.delete(batchKey);
    if (batch.timer) {
      clearTimeout(batch.timer);
    }
    
    console.log(`[API优化] 🚀 执行批处理，包含 ${batch.requests.length} 个请求`);
    this.stats.batchedRequests += batch.requests.length;
    
    // 构建批处理请求
    const batchRequest = this.buildBatchRequest(batch.requests);
    
    try {
      // 执行批处理请求
      const batchResult = await this.performRequest(batchRequest);
      
      // 分发结果
      this.distributeBatchResults(batch.requests, batchResult);
      
    } catch (error) {
      // 批处理失败，回退到单独请求
      console.warn('[API优化] 批处理失败，回退到单独请求:', error.message);
      this.fallbackToIndividualRequests(batch.requests);
    }
  }

  /**
   * @function buildBatchRequest - 构建批处理请求
   * @description 将多个请求合并为一个批处理请求
   * @param {Array} requests - 请求数组
   * @returns {Object} 批处理请求对象
   */
  buildBatchRequest(requests) {
    // 这里需要根据具体的API实现批处理格式
    // 示例实现：
    const batchData = {
      requests: requests.map((req, index) => ({
        id: index,
        method: req.options.method,
        url: req.options.url,
        data: req.options.data
      }))
    };
    
    return {
      url: this.getBatchEndpoint(requests[0].options.url),
      method: 'POST',
      data: batchData,
      headers: requests[0].options.headers,
      timeout: requests[0].options.timeout
    };
  }

  /**
   * @function distributeBatchResults - 分发批处理结果
   * @description 将批处理结果分发给各个请求
   * @param {Array} requests - 请求数组
   * @param {Object} batchResult - 批处理结果
   */
  distributeBatchResults(requests, batchResult) {
    const results = batchResult.responses || [];
    
    requests.forEach((req, index) => {
      try {
        const result = results[index];
        if (result && result.success !== false) {
          req.resolve(result);
        } else {
          req.reject(new Error(result?.error || '批处理请求失败'));
        }
      } catch (error) {
        req.reject(error);
      }
    });
  }

  /**
   * @function fallbackToIndividualRequests - 回退到单独请求
   * @description 当批处理失败时，回退到单独执行请求
   * @param {Array} requests - 请求数组
   */
  async fallbackToIndividualRequests(requests) {
    for (const req of requests) {
      try {
        const result = await this.requestWithRetry(req.options);
        req.resolve(result);
      } catch (error) {
        req.reject(error);
      }
    }
  }

  /**
   * @function getCachedResult - 获取缓存结果
   * @description 从缓存中获取结果
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存的结果或null
   */
  getCachedResult(key) {
    const cached = this.requestCache.get(key);
    if (!cached) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() > cached.expiry) {
      this.requestCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * @function setCachedResult - 设置缓存结果
   * @description 将结果存储到缓存
   * @param {string} key - 缓存键
   * @param {Object} data - 要缓存的数据
   * @param {number} ttl - 生存时间(ms)
   */
  setCachedResult(key, data, ttl) {
    // 检查缓存大小限制
    if (this.requestCache.size >= this.config.maxCacheSize) {
      this.evictOldestCache();
    }
    
    this.requestCache.set(key, {
      data: data,
      expiry: Date.now() + ttl,
      created: Date.now()
    });
  }

  /**
   * @function evictOldestCache - 驱逐最旧的缓存
   * @description 当缓存达到大小限制时，移除最旧的缓存项
   */
  evictOldestCache() {
    let oldestKey = null;
    let oldestTime = Date.now();
    
    for (const [key, value] of this.requestCache.entries()) {
      if (value.created < oldestTime) {
        oldestTime = value.created;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.requestCache.delete(oldestKey);
    }
  }

  /**
   * @function startCacheCleanup - 启动缓存清理
   * @description 定期清理过期的缓存项
   */
  startCacheCleanup() {
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer);
    }
    
    this.cacheCleanupTimer = setInterval(() => {
      this.cleanupExpiredCache();
    }, this.config.cacheCleanupInterval);
  }

  /**
   * @function cleanupExpiredCache - 清理过期缓存
   * @description 移除所有过期的缓存项
   */
  cleanupExpiredCache() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, value] of this.requestCache.entries()) {
      if (now > value.expiry) {
        this.requestCache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`[API优化] 🧹 清理了 ${cleanedCount} 个过期缓存项`);
    }
  }

  /**
   * @function waitForConcurrencySlot - 等待并发槽位
   * @description 等待可用的并发槽位
   */
  async waitForConcurrencySlot() {
    while (this.activeRequests.size >= this.config.maxConcurrentRequests) {
      await this.sleep(10); // 等待10ms
    }
  }

  /**
   * @function generateRequestKey - 生成请求键
   * @description 为请求生成唯一键用于缓存和去重
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @param {Object} data - 请求数据
   * @returns {string} 请求键
   */
  generateRequestKey(url, method, data) {
    const dataStr = data ? JSON.stringify(data) : '';
    return `${method}:${url}:${this.hashString(dataStr)}`;
  }

  /**
   * @function hashString - 字符串哈希
   * @description 为字符串生成简单哈希
   * @param {string} str - 输入字符串
   * @returns {string} 哈希值
   */
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * @function supportsBatching - 检查是否支持批处理
   * @description 检查给定的URL和方法是否支持批处理
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @returns {boolean} 是否支持批处理
   */
  supportsBatching(url, method) {
    // 这里可以根据具体的API实现批处理支持检查
    // 示例：只有特定的API端点支持批处理
    const batchableEndpoints = [
      '/api/gemini/chat',
      '/api/analysis/batch'
    ];
    
    return method === 'POST' && batchableEndpoints.some(endpoint => url.includes(endpoint));
  }

  /**
   * @function getBatchKey - 获取批处理键
   * @description 为批处理生成键
   * @param {string} url - 请求URL
   * @returns {string} 批处理键
   */
  getBatchKey(url) {
    // 提取API端点作为批处理键
    const urlParts = url.split('/');
    return urlParts.slice(0, -1).join('/'); // 移除最后一部分，保留端点路径
  }

  /**
   * @function getBatchEndpoint - 获取批处理端点
   * @description 获取批处理API端点
   * @param {string} url - 原始URL
   * @returns {string} 批处理端点URL
   */
  getBatchEndpoint(url) {
    // 这里需要根据具体的API实现
    // 示例：将单个请求URL转换为批处理URL
    return url.replace('/chat', '/batch');
  }

  /**
   * @function shouldRetry - 检查是否应该重试
   * @description 根据错误类型决定是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或服务器错误通常可以重试
    const retryableErrors = [
      'NetworkError',
      'TimeoutError',
      'AbortError'
    ];
    
    // HTTP状态码5xx通常可以重试
    const retryableStatus = [500, 502, 503, 504];
    
    return retryableErrors.includes(error.name) ||
           retryableStatus.some(status => error.message.includes(status.toString()));
  }

  /**
   * @function calculateRetryDelay - 计算重试延迟
   * @description 使用指数退避算法计算重试延迟
   * @param {number} attempt - 重试次数
   * @returns {number} 延迟时间(ms)
   */
  calculateRetryDelay(attempt) {
    const delay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt);
    return Math.min(delay, this.config.maxDelay);
  }

  /**
   * @function sleep - 睡眠函数
   * @description 异步睡眠指定时间
   * @param {number} ms - 睡眠时间(ms)
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * @function getStats - 获取统计信息
   * @description 获取API优化器的统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      cacheSize: this.requestCache.size,
      activeRequests: this.activeRequests.size,
      pendingBatches: this.pendingBatches.size,
      cacheHitRate: this.stats.totalRequests > 0 ? 
        (this.stats.cachedRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * @function clearCache - 清空缓存
   * @description 清空所有缓存数据
   */
  clearCache() {
    this.requestCache.clear();
    console.log('[API优化] 缓存已清空');
  }

  /**
   * @function cleanup - 清理资源
   * @description 清理API优化器使用的资源
   */
  cleanup() {
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer);
      this.cacheCleanupTimer = null;
    }
    
    this.requestCache.clear();
    this.pendingBatches.clear();
    this.pendingRequests.clear();
    this.activeRequests.clear();
    this.requestQueue = [];
    
    console.log('[API优化] API优化器已清理');
  }
}

// 导出API优化器类
export { AiApiOptimizer };
