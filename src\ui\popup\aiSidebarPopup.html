<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI侧边栏控制中心</title>
  <link rel="stylesheet" href="aiSidebarPopup.css">
</head>
<body>
  <!-- #region 主容器 -->
  <div id="ai-popup-container" class="ai-popup">
    
    <!-- #region 头部标题 -->
    <header class="ai-popup__header">
      <div class="ai-popup__logo">
        <span class="ai-popup__logo-text">AI助手</span>
        <span class="ai-popup__version">v1.0.0</span>
      </div>
    </header>
    <!-- #endregion -->
    
    <!-- #region 连接状态 -->
    <section class="ai-popup__status">
      <div id="ai-connection-status" class="ai-popup__status-item">
        <div class="ai-popup__status-indicator ai-popup__status-indicator--connected"></div>
        <span>已连接</span>
      </div>
    </section>
    <!-- #endregion -->
    
    <!-- #region 快速操作 -->
    <section class="ai-popup__actions">
      <button id="ai-toggle-sidebar-btn" class="ai-popup__action-btn ai-popup__action-btn--primary">
        <svg class="ai-popup__icon" viewBox="0 0 24 24">
          <path d="M3,9H17V7H3V9M3,13H17V11H3V13M3,17H17V15H3V17M19,17H21V15H19V17M19,7V9H21V7H19M19,13H21V11H19V13Z"/>
        </svg>
        <span>打开侧边栏</span>
      </button>
      
      <button id="ai-analyze-current-page-btn" class="ai-popup__action-btn">
        <svg class="ai-popup__icon" viewBox="0 0 24 24">
          <path d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z"/>
        </svg>
        <span>分析页面</span>
      </button>
      
      <button id="ai-quick-reply-btn" class="ai-popup__action-btn">
        <svg class="ai-popup__icon" viewBox="0 0 24 24">
          <path d="M12,3C17.5,3 22,6.58 22,11C22,15.42 17.5,19 12,19C10.76,19 9.57,18.82 8.47,18.5C5.55,21 2,21 2,21C4.33,18.67 4.7,17.1 4.75,16.5C3.05,15.07 2,13.13 2,11C2,6.58 6.5,3 12,3Z"/>
        </svg>
        <span>智能回复</span>
      </button>
    </section>
    <!-- #endregion -->
    
    <!-- #region 设置和工具 -->
    <section class="ai-popup__tools">
      <button id="ai-settings-btn" class="ai-popup__tool-btn">
        <svg class="ai-popup__icon" viewBox="0 0 24 24">
          <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
        </svg>
        <span>设置</span>
      </button>
      

    </section>
    <!-- #endregion -->
    
    <!-- #region 底部信息 -->
    <footer class="ai-popup__footer">
      <div class="ai-popup__info">
        <span id="ai-current-page-status">当前页面：已分析</span>
      </div>
    </footer>
    <!-- #endregion -->
    
  </div>
  <!-- #endregion -->
  
  <!-- JavaScript模块 -->
  <script type="module" src="aiSidebarPopup.js"></script>
</body>
</html> 